#!/bin/bash

if [ -z $ENV ]; then
  echo "please input ENV: [test|prod]";
  exit 1;
fi
if [ -z $DB ]; then
  echo "please input DB: [dbuser|dbmeta|dbredeem]";
  exit 1;
fi

echo "running migration for env:${ENV},database:${DB} ... "

LAMBDA_CMD="aws lambda invoke --function-name function:fn-kktv-db-migration:${ENV} \
                     --qualifier ${ENV} \
                     --payload '{\"cmd\": \"up\", \"database\":[\"$DB\"]}' \
                     lambda-result.json"

echo "Lambda CMD: $LAMBDA_CMD"
resp=$(eval "$LAMBDA_CMD")

if [ $? -ne 0 ]; then
  echo "Migration failed for env:${ENV}, database:${DB}"
  exit 1
fi

err=$(echo "${resp}" | awk '/FunctionError/ {print $2}')

if [ ! -z "$err" ]; then
  errMsg=$(sed 's/.*"errorMessage":"\{0,1\}\([^,"]*\)"\{0,1\}.*/\1/' lambda-result.json);
  echo "ERR: ${err}, $errMsg";
  exit 3;
fi;

echo ok message from ${DB}: $(sed 's/.*"msg":"\{0,1\}\([^,"]*\)"\{0,1\}.*/\1/' lambda-result.json)
echo FINISH.
