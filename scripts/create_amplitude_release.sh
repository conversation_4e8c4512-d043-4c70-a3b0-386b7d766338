#!/bin/bash

if [ -z "$GITHUB_TOKEN" ]; then
  echo "please input GITHUB_TOKEN";
  exit 1;
fi
if [ -z "$AMPLITUDE_AUTH_TOKEN" ]; then
  echo "please input AMPLITUDE_AUTH_TOKEN";
  exit 1;
fi
if [ -z "$RELEASE_TAG" ]; then
  echo "please input RELEASE_TAG, e.g. v0.137.1";
  exit 1;
fi

SEMANTIC_TAG=$(echo $RELEASE_TAG | sed 's/^v//')  # Amplitude only supports semantic versioning format: major.minor.patch (e.g. 1.2.3)
RELEASE_NOTE=$(curl -H "Authorization: token $GITHUB_TOKEN" https://api.github.com/repos/KKTV/kktv-api-v3/releases/tags/"$RELEASE_TAG" | jq '.body' | sed 's/\"//g')
CURRENT_TIME=$(date +"%Y-%m-%d %H:%M:%S")
echo "$RELEASE_NOTE"
curl -i -v -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic $AMPLITUDE_AUTH_TOKEN" \
  -d "{\"title\":\"KKTV-API $RELEASE_TAG\",\"version\":\"$SEMANTIC_TAG\",\"created_by\": \"kktv-api\",\"description\":\"$RELEASE_NOTE\", \"release_start\":\"$CURRENT_TIME\", \
  \"platforms\": [\"iOS\", \"Android\", \"Web\", \"Android TV\", \"tvOS\", \"iPadOS\", \"MOD\"]}" \
  https://amplitude.com/api/2/release
