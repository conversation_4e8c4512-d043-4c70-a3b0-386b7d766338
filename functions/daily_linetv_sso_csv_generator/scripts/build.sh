#!/bin/bash

# 用法: ./build.sh dev
ENV=$1

if [ -z "$ENV" ]; then
  echo "請輸入環境參數，例如: test、prod"
  exit 1
fi

# 檔案路徑在前面兩層
INPUT_FILE="../../project.${ENV}.json"
OUTPUT_FILE=".env"

if [ ! -f "$INPUT_FILE" ]; then
  echo "找不到檔案: $INPUT_FILE"
  exit 1
fi

echo "產生 $OUTPUT_FILE"

# 主要轉換邏輯
jq -r '.environment | to_entries[] | "\(.key)=\(.value)"' "$INPUT_FILE" > "$OUTPUT_FILE"

echo "完成 ✅"

# 先刪除舊的 main
rm -f main

# 交叉編譯出 linux/arm64 的 main
GOOS=linux GOARCH=arm64 CGO_ENABLED=0 go build -o main main.go

# 建立 arm64 的 docker image
# 注意：需搭配 arm64 base image 的 Dockerfile

docker buildx build --platform linux/arm64 -t kktv-$ENV-daily-linetv-sso-csv-generator:latest .

# 登入 aws registry
aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin 312530604583.dkr.ecr.ap-northeast-1.amazonaws.com

# 上傳到 aws registry
docker tag kktv-$ENV-daily-linetv-sso-csv-generator:latest 312530604583.dkr.ecr.ap-northeast-1.amazonaws.com/kktv-$ENV-daily-linetv-sso-csv-generator:latest
docker push 312530604583.dkr.ecr.ap-northeast-1.amazonaws.com/kktv-$ENV-daily-linetv-sso-csv-generator:latest
