// Code generated by MockGen. DO NOT EDIT.
// Source: internal/appauth/repository.go

// Package appauth is a generated GoMock package.
package appauth

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// GetActiveByAppName mocks base method.
func (m *MockRepository) GetActiveByAppName(appName string) (*dbuser.AuthedApp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveByAppName", appName)
	ret0, _ := ret[0].(*dbuser.AuthedApp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveByAppName indicates an expected call of GetActiveByAppName.
func (mr *MockRepositoryMockRecorder) GetActiveByAppName(appName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveByAppName", reflect.TypeOf((*MockRepository)(nil).GetActiveByAppName), appName)
}
