package appauth

import (
	"database/sql"
	"errors"

	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

type Repository interface {
	GetActiveByAppName(appName string) (*dbuser.AuthedApp, error)
}

type repository struct {
	db database.DB
}

func NewRepository(db database.DB) Repository {
	return &repository{db: db}
}

func (r *repository) GetActiveByAppName(appName string) (*dbuser.AuthedApp, error) {
	app := new(dbuser.AuthedApp)
	err := r.db.Get(app, `SELECT * FROM authed_apps WHERE name = $1 AND status = 'active'`, appName)
	if errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return app, nil
}
