package main

import (
	"bytes"
	"errors"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/datatype"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"

	appauthmock "github.com/KKTV/kktv-api-v3/functions/daily_linetv_sso_csv_generator/internal/appauth"
	usermock "github.com/KKTV/kktv-api-v3/kktvapi/pkg/user"
	userpkg "github.com/KKTV/kktv-api-v3/kktvapi/pkg/user"
	wrappermock "github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper"
)

type HandlerSuite struct {
	suite.Suite
	h           *handler
	ctrl        *gomock.Controller
	appauthRepo *appauthmock.MockRepository
	oauthSvc    *wrappermock.MockOauthAuthorizationService
	userSvc     *usermock.MockService
}

func (suite *HandlerSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.appauthRepo = appauthmock.NewMockRepository(suite.ctrl)
	suite.oauthSvc = wrappermock.NewMockOauthAuthorizationService(suite.ctrl)
	suite.userSvc = usermock.NewMockService(suite.ctrl)

	suite.h = &handler{
		appauthRepo:  suite.appauthRepo,
		oauthService: suite.oauthSvc,
		userService:  suite.userSvc,
	}
}

func (suite *HandlerSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestHandlerSuite(t *testing.T) {
	suite.Run(t, new(HandlerSuite))
}

func (suite *HandlerSuite) TestGenerateLinetvSsoCSV_HappyPath() {
	suite.appauthRepo.EXPECT().GetActiveByAppName("line_app").Return(&dbuser.AuthedApp{AppID: "app-123"}, nil)
	suite.oauthSvc.EXPECT().GetUserIDsByAppID("app-123").Return([]string{"test-user-id-1", "test-user-id-2"}, nil)
	now1 := datatype.DateTime(time.Now())
	now2 := datatype.DateTime(time.Now())
	suite.userSvc.EXPECT().GetUserServiceStatus(gomock.Any()).Return(&userpkg.UserServiceStatus{MembershipRole: "Premium", ExpiredAt: now1, NextPaymentAt: &now1, IsInSubscription: true}, nil).AnyTimes()
	suite.userSvc.EXPECT().GetUserServiceStatus(gomock.Any()).Return(&userpkg.UserServiceStatus{MembershipRole: "freetrial", ExpiredAt: now2, NextPaymentAt: &now2, IsInSubscription: false}, nil).AnyTimes()

	filename, csvData, err := suite.h.generateLinetvSsoCSV()

	suite.NoError(err)
	suite.NotEmpty(filename)
	csvRows := 0
	if len(csvData) > 0 {
		lines := bytes.Split(csvData, []byte("\n"))
		for _, l := range lines {
			if len(bytes.TrimSpace(l)) > 0 {
				csvRows++
			}
		}
	}
	suite.GreaterOrEqual(csvRows, 3, "csv 應該至少有 header+2筆資料")
}

func (suite *HandlerSuite) TestGenerateLinetvSsoCSV_AppNotFound() {
	suite.appauthRepo.EXPECT().GetActiveByAppName("line_app").Return(nil, errors.New("not found"))

	filename, csvData, err := suite.h.generateLinetvSsoCSV()

	suite.Error(err)
	suite.Contains(err.Error(), "not found")
	suite.Empty(filename)
	suite.Nil(csvData)
}

func (suite *HandlerSuite) TestGenerateLinetvSsoCSV_OauthError() {
	suite.appauthRepo.EXPECT().GetActiveByAppName("line_app").Return(&dbuser.AuthedApp{AppID: "app-123"}, nil)
	suite.oauthSvc.EXPECT().GetUserIDsByAppID("app-123").Return(nil, errors.New("db error"))

	filename, csvData, err := suite.h.generateLinetvSsoCSV()

	suite.Error(err)
	suite.Contains(err.Error(), "db error")
	suite.Empty(filename)
	suite.Nil(csvData)
}

func (suite *HandlerSuite) TestGenerateLinetvSsoCSV_UserNotFound() {
	suite.appauthRepo.EXPECT().GetActiveByAppName("line_app").Return(&dbuser.AuthedApp{AppID: "app-123"}, nil)
	suite.oauthSvc.EXPECT().GetUserIDsByAppID("app-123").Return([]string{"u1", "u2"}, nil)
	suite.userSvc.EXPECT().GetUserServiceStatus("u1").Return(nil, errors.New("not found"))
	now3 := datatype.DateTime(time.Now())
	suite.userSvc.EXPECT().GetUserServiceStatus("u2").Return(&userpkg.UserServiceStatus{MembershipRole: "F", ExpiredAt: now3, NextPaymentAt: &now3, IsInSubscription: false}, nil)

	filename, csvData, err := suite.h.generateLinetvSsoCSV()

	suite.NoError(err) // user not found 只會被略過，不會導致失敗
	suite.NotEmpty(filename)
	csvRows := 0
	if len(csvData) > 0 {
		lines := bytes.Split(csvData, []byte("\n"))
		for _, l := range lines {
			if len(bytes.TrimSpace(l)) > 0 {
				csvRows++
			}
		}
	}
	suite.GreaterOrEqual(csvRows, 2, "csv 應該至少有 header+1筆資料")
}
