package main

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/datatype"
	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	"github.com/KKTV/kktv-api-v3/pkg/secret"

	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/pkg/billing"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	_ "github.com/lib/pq"

	"github.com/KKTV/kktv-api-v3/functions/daily_linetv_sso_csv_generator/internal/appauth"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper"
	"github.com/KKTV/kktv-api-v3/pkg/database"

	userpkg "github.com/KKTV/kktv-api-v3/kktvapi/pkg/user"
	"github.com/KKTV/kktv-api-v3/pkg/aws/s3"
	s3Client "github.com/KKTV/kktv-api-v3/pkg/aws/s3"
	"github.com/aws/aws-lambda-go/lambda"
)

func init() {
	if err := config.Init(); err != nil {
		log.Fatal("Failed to initialize config").Err(err).Send()
	}
	log.Init(config.Env)
}

type UserServiceStatus struct {
	UserID           string
	MembershipRole   string
	ExpiredAt        datatype.DateTime
	NextPaymentAt    *datatype.DateTime
	IsInSubscription bool
}

type handler struct {
	appauthRepo        appauth.Repository
	userService        userpkg.Service
	orderService       wrapper.OrderService
	paymentInfoService wrapper.PaymentInfoService
	oauthService       wrapper.OauthAuthorizationService
}

func fetchUserStatus(userIDs []string, userService userpkg.Service, ch chan<- []UserServiceStatus) {
	var result []UserServiceStatus
	for _, userID := range userIDs {
		status, err := userService.GetUserServiceStatus(userID)
		if err != nil {
			log.Error("Failed to query user status: ").Err(err).Send()
			continue
		}
		if status == nil {
			log.Warn("User returned nil status: " + userID).Send()
			continue
		}
		result = append(result, UserServiceStatus{
			UserID:           userID,
			MembershipRole:   status.MembershipRole,
			ExpiredAt:        status.ExpiredAt,
			NextPaymentAt:    status.NextPaymentAt,
			IsInSubscription: status.IsInSubscription,
		})
	}
	ch <- result
}

func (h *handler) generateLinetvSsoCSV() (filename string, csvData []byte, err error) {
	now := time.Now().In(datetimer.LocationTaipei)
	filename = now.Format("2006-01-02") + ".csv"

	app, err := h.appauthRepo.GetActiveByAppName("line_app")
	if err != nil {
		return "", nil, fmt.Errorf("get line_app app_id failed: %w", err)
	}
	appID := app.AppID

	userIDs, err := h.oauthService.GetUserIDsByAppID(appID)
	if err != nil {
		return "", nil, fmt.Errorf("get oauth_authorizations failed: %w", err)
	}

	log.Info(fmt.Sprintf("Total %d users", len(userIDs))).Send()

	const workerCount = 20
	groupSize := (len(userIDs) + workerCount - 1) / workerCount
	ch := make(chan []UserServiceStatus, workerCount)

	for i := 0; i < workerCount; i++ {
		start := i * groupSize
		end := start + groupSize
		if end > len(userIDs) {
			end = len(userIDs)
		}
		if start >= end {
			ch <- nil // 沒有要查的
			continue
		}
		go fetchUserStatus(userIDs[start:end], h.userService, ch)
	}

	var statusList []UserServiceStatus
	for i := 0; i < workerCount; i++ {
		part := <-ch
		if part != nil {
			statusList = append(statusList, part...)
		}
	}

	csvData, err = generateCSV(statusList)
	if err != nil {
		return "", nil, fmt.Errorf("generate CSV failed: %w", err)
	}

	return filename, csvData, nil
}

func (h *handler) LambdaHandle(ctx context.Context) error {
	start := time.Now()
	s3Client := s3Client.NewS3Client(config.AWSConfig)
	filename, csvData, err := h.generateLinetvSsoCSV()
	if err != nil {
		log.Fatal("daily_linetv_sso_csv_generator failed: ").Err(err).Send()
		return err
	}
	bucket := os.Getenv("LINE_TV_SSO_CSV_BUCKET")

	if err := s3Client.PutObject(ctx, bucket, filename, csvData, s3.WithACL("")); err != nil {
		log.Error("S3 upload failed: ").Err(err).Send()
		return err
	}
	log.Info(fmt.Sprintf("Successfully uploaded %s to S3 bucket %s", filename, bucket)).Send()

	elapsed := time.Since(start)
	minutes := int(elapsed.Minutes())
	seconds := int(elapsed.Seconds()) % 60
	log.Info(fmt.Sprintf("total elapsed time: %d min %d sec", minutes, seconds)).Send()

	return nil
}

func generateCSV(statusList []UserServiceStatus) ([]byte, error) {
	var buf bytes.Buffer
	writer := csv.NewWriter(&buf)
	writer.Write([]string{"user_id", "membership_role", "expired_at", "next_payment_at", "is_in_subscription"})
	for _, s := range statusList {
		writer.Write([]string{
			s.UserID,
			s.MembershipRole,
			s.ExpiredAt.String(),
			func() string {
				if s.NextPaymentAt != nil {
					return s.NextPaymentAt.String()
				}
				return ""
			}(),
			fmt.Sprintf("%v", s.IsInSubscription),
		})
	}
	writer.Flush()
	return buf.Bytes(), nil
}

func newDBPool(uri string) *datastore.DBPool {
	dbstring := strings.Split(uri, ",")
	for i, db := range dbstring {
		dbstring[i] = strings.TrimSpace(db)
	}
	return datastore.NewDBPool(dbstring)
}

func newRedis(uri string) *datastore.RedisPool {
	redisHosts := strings.Split(uri, ",")
	for i, r := range redisHosts {
		redisHosts[i] = strings.TrimSpace(r)
	}
	return datastore.NewRedisPool(redisHosts)
}

func main() {

	// 依賴初始化
	dbPoolUser := newDBPool(config.DbUser)
	userRedisPool := newRedis(config.RedisUser)
	userCacheWriter, userCacheReader := cache.New(userRedisPool.Master()), cache.New(userRedisPool.Slave())

	dbConn := &database.Conn{DB: dbPoolUser.Master().Unsafe()}
	appauthRepo := appauth.NewRepository(dbPoolUser.Master().Unsafe())

	orderService := wrapper.NewOrderService(dbPoolUser.Master().Unsafe(), dbPoolUser.Master().Unsafe())
	paymentInfoService := wrapper.NewPaymentInfoService(dbPoolUser.Master().Unsafe())
	oauthService := wrapper.NewOauthAuthorizationService(dbConn)
	wrapperUserService := wrapper.NewUserService(dbPoolUser.Slave().Unsafe())

	secret.Init(config.Env)
	billingClient := billing.NewClient(config.BillingAPIHost, userCacheWriter, userCacheReader)
	userService := userpkg.NewService(
		orderService.GetRepository(),
		billingClient,
		wrapperUserService.GetRepository(),
		paymentInfoService.GetRepository(),
	)
	// LineTV S3 is in us-east-1
	config.AWSConfig.Region = "us-east-1"

	h := &handler{
		appauthRepo:        appauthRepo,
		userService:        userService,
		orderService:       orderService,
		paymentInfoService: paymentInfoService,
		oauthService:       oauthService,
	}

	log.Info("LAMBDA_TASK_ROOT: " + os.Getenv("LAMBDA_TASK_ROOT")).Send()
	if os.Getenv("LAMBDA_TASK_ROOT") != "" {
		lambda.Start(h.LambdaHandle)
		return
	}

	// 本地測試
	if err := h.LambdaHandle(context.Background()); err != nil {
		log.Error("fail to handle: " + err.Error()).Send()
	}
}
