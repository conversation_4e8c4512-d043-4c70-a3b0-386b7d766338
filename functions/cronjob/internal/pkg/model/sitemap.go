package model

import "encoding/json"

type SitemapTitleResp struct {
	DateTime string         `json:"date_time"`
	Titles   []SitemapTitle `json:"titles"`
}

type SitemapTitle struct {
	TitleID    string           `json:"title_id" copier:"ID"`
	Name       string           `json:"name" copier:"Title"`
	Summary    string           `json:"summary" copier:"Summary"`
	TitleType  string           `json:"title_type" copier:"TitleType"`
	ChildLock  bool             `json:"child_lock" copier:"ChildLock"`
	UserRating float64          `json:"user_rating" copier:"UserRating"`
	Status     string           `json:"status" copier:"Status"`
	Series     []*SitemapSeries `json:"series" copier:"Series"`
}

type SitemapSeries struct {
	ID       string            `json:"series_id" copier:"ID"`
	Name     string            `json:"name" copier:"Title"`
	Episodes []*SitemapEpisode `json:"episodes" copier:"Episodes"`
}

type SitemapEpisode struct {
	ID       string             `json:"episode_id" copier:"ID"`
	IsAvod   bool               `json:"is_avod" copier:"IsAvod"`
	Still    string             `json:"still" copier:"Still"`
	Name     string             `json:"name" copier:"Title"`
	VideoURL *SitemapMezzanines `json:"video_url" copier:"Mezzanines"`
	Duration float64            `json:"duration" copier:"Duration"`
	Pub      int64              `json:"pub" copier:"Pub"`
	Unpub    int64              `json:"unpub" copier:"UnPub"`
}

type SitemapMezzanines struct {
	Hls *SitemapHLS `json:"hls" copier:"Hls"`
}

type SitemapHLS struct {
	URI string `json:"uri" copier:"URI"`
}

func (m *SitemapMezzanines) MarshalJSON() ([]byte, error) {
	if m == nil || m.Hls == nil {
		return []byte("null"), nil
	}
	return json.Marshal(m.Hls.URI)
}
