//go:generate mockgen -source slack.go -destination slack_mock.go -package slack
package slack

import (
	slackapi "github.com/nlopes/slack"
)

type Client interface {
	PostMessage(channel string, options ...slackapi.MsgOption) (string, error)
}

type client struct {
	slackAPI *slackapi.Client
}

func New(token string) Client {
	return &client{
		slackAPI: slackapi.New(token),
	}
}

func (c *client) PostMessage(channel string, options ...slackapi.MsgOption) (string, error) {
	respChannel, _, err := c.slackAPI.PostMessage(channel, options...)
	return respChannel, err
}
