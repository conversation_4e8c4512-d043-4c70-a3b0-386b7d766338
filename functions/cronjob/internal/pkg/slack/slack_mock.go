// Code generated by MockGen. DO NOT EDIT.
// Source: slack.go

// Package slack is a generated GoMock package.
package slack

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	slack "github.com/nlopes/slack"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// PostMessage mocks base method.
func (m *MockClient) PostMessage(channel string, options ...slack.MsgOption) (string, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{channel}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostMessage", varargs...)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostMessage indicates an expected call of PostMessage.
func (mr *MockClientMockRecorder) PostMessage(channel interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{channel}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostMessage", reflect.TypeOf((*MockClient)(nil).PostMessage), varargs...)
}
