package sitemap

import (
	"context"
	"encoding/json"
	"time"

	"github.com/KKTV/kktv-api-v3/functions/cronjob/internal/meta"
	pmodel "github.com/KKTV/kktv-api-v3/functions/cronjob/internal/pkg/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/pkg/aws/s3"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/jinzhu/copier"
)

type handler struct {
	env       string
	titleRepo meta.Repository
}

func NewHandler(env string, dbReader database.DB) *handler {
	return &handler{
		env:       env,
		titleRepo: meta.NewTitleRepository(dbReader),
	}
}

// 1. list all available title ids
// 2. get title details from Redis
// 3. generate sitemap titles
// 4. write to s3
func (h *handler) GenerateTitles() error {
	fields := []string{
		"id",
		"title",
		"summary",
		"title_type",
		"child_lock",
		"user_rating",
		"status",
		"available",
		"series",
	}
	ids, err := h.titleRepo.ListAllAvailableTitleIDs()
	if err != nil {
		log.Error("sitemapHandler: ListAllAvailableTitleIDs").Err(err).Send()
		return err
	}

	bulkTitleDetails, err := model.NewBulkTitleDetailsViaFields(ids, fields)
	if err != nil {
		log.Error("sitemapHandler: NewBulkTitleDetailsViaFields").Err(err).Send()
		return err
	}

	sitemapTitlesResp := pmodel.SitemapTitleResp{}
	sitemapTitles := make([]pmodel.SitemapTitle, 0)
	for _, td := range bulkTitleDetails {
		sitemapTitle := pmodel.SitemapTitle{}
		err := copier.CopyWithOption(&sitemapTitle, td, copier.Option{IgnoreEmpty: true, DeepCopy: true})
		if err != nil {
			log.Warn("sitemapHandler: Generate: Copier").Err(err).Send()
		}

		// It is because expired titles don't need series in sitemap
		if sitemapTitle.Status != "license_valid" {
			sitemapTitle.Series = nil
		}

		sitemapTitles = append(sitemapTitles, sitemapTitle)
	}

	sitemapTitlesResp.Titles = sitemapTitles
	sitemapTitlesResp.DateTime = time.Now().Format(time.RFC3339)

	jsonData, err := json.MarshalIndent(sitemapTitlesResp, "", "  ")
	if err != nil {
		log.Error("sitemapHandler: MarshalIndent").Err(err).Send()
		return err
	}

	s3Client := s3.NewS3Client(config.AWSConfig)
	if err := s3Client.PutObject(context.Background(), "kktv-prod-web-app", "app.kktv.me-sitemap/source-sitemap-titles.json", jsonData); err != nil {
		log.Error("sitemapHandler: PutObject").Err(err).Send()
		return err
	}

	return nil
}
