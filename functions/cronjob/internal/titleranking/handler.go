package titleranking

import (
	"encoding/json"
	"fmt"

	"log"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/lambdacontext"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/athena"
	"github.com/nlopes/slack"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"

	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	keys "github.com/KKTV/kktv-api-v3/pkg/key"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
)

type handler struct {
	cacheReader   cache.Cacher
	cacheWriter   cache.Cacher
	clock         clock.Clock
	awsAthenaSvc  *awsAthena
	redisPoolMeta *datastore.RedisPool
	dbPoolMeta    *datastore.DBPool
}

func NewHandler(redisPoolMeta *datastore.RedisPool, cacheReader, cacheWriter cache.Cacher, dbPoolMeta *datastore.DBPool) *handler {
	return &handler{
		redisPoolMeta: redisPoolMeta,
		dbPoolMeta:    dbPoolMeta,
		cacheReader:   cacheReader,
		cacheWriter:   cacheWriter,
		clock:         clock.New(),
		awsAthenaSvc:  NewAwsAthenaService(),
	}
}

func (h *handler) RefreshRanking() error {
	var err error

	defer func() {
		attachment := slack.Attachment{
			Color: "good",
			Text:  "Generate daily title ranking success.",
		}
		if err != nil {
			attachment = slack.Attachment{
				Color: "danger",
				Text:  fmt.Sprintf("Generate daily title ranking failed.\nError: %s", err.Error()),
			}
		}
		SendSlackMessage(fmt.Sprintf("#kktv-log-%s", config.Env), "", attachment)
	}()

	athenaSvc := h.awsAthenaSvc

	// 查詢已建立的 partitions
	partitions, err := athenaSvc.QueryAmplitudeLogs("SHOW PARTITIONS kktv_prod_raw_log")
	if err != nil {
		log.Println("[ERROR] Show partitions failed: ", err)
		return err
	}

	// 檢查兩天前的 partition 是否已經新增
	// * 目前 amplitude 資料上傳前一天資料到 s3 的時間為早上 09:00，所以先改為取前兩天的資料
	date := h.clock.Now().AddDate(0, 0, -2)
	formattedDate := date.Format("2006-01-02")
	log.Println("[INFO] Check partition: ", formattedDate)

	partitionCreated := false
	for _, row := range partitions {
		data := row.Data[0]
		partitionName := strings.Replace(*data.VarCharValue, "dt=", "", -1)
		if partitionName == formattedDate {
			partitionCreated = true
		}
	}

	// 如果 partition 尚未新增，新增 partition
	if !partitionCreated {
		queryString := fmt.Sprintf("ALTER TABLE kktv_prod_raw_log ADD PARTITION (dt='%s')", formattedDate)
		log.Println("[INFO] Alter table partition, query string: ", queryString)
		_, err = athenaSvc.QueryAmplitudeLogs(queryString)
		if err != nil {
			log.Println("[ERROR] Add partitions failed: ", err)
			return err
		}
	}

	// 查詢各 genre 的 title 排行榜資料
	queryString := fmt.Sprintf(`SELECT
		json_extract_scalar(event_properties, '$["title id"]') AS title_id,
		COUNT(2) AS valid_clicks
	FROM kktv_prod_raw_log
	WHERE dt = '%s'
	AND event_type = 'Video Playing Stopped'
	AND json_extract_scalar(event_properties, '$["service zone"]') = 'svod'
	AND json_extract_scalar(event_properties, '$["video played percentage"]') NOT IN ('∞', '-∞')
	AND CAST(json_extract_scalar(event_properties, '$["video played percentage"]') AS REAL) >= 0.1
	AND json_extract_scalar(user_properties, '$["account current membership"]') IN ('premium','kkbox prime','premium x kkbox prime')
	AND json_extract_scalar(event_properties, '$["action trigger"]') IN ('video ended', 'next episode', 'previous episode', 'leave')
	AND length(json_extract_scalar(event_properties, '$["title id"]')) = 8
	AND json_extract_scalar(event_properties, '$["episode id"]') != ''
	GROUP BY 1
	ORDER BY 2 DESC`, formattedDate)

	rows, err := athenaSvc.QueryAmplitudeLogs(queryString)
	if err != nil {
		log.Println("[ERROR] Query title ranking failed: ", err)
		return err
	}

	titleIds := []string{}
	for i, row := range rows {
		if i > 0 {
			titleId := *row.Data[0].VarCharValue
			titleIds = append(titleIds, titleId)
		}
	}

	// 查詢 title detail
	excludeFields := []string{"series", "end_year"}
	var titleDetails model.CollectionTitles
	titleDetails, err = model.NewTitleDetailsDB(titleIds, excludeFields) //TODO use legacyHelper as wrapper
	if err != nil {
		log.Println("[ERROR] Get title details failed: ", err)
		return err
	}
	titleDetails = titleDetails.FilterNotExpired()

	// 按照 title genre 分類，並過濾成人內容
	collectionTitlesMap := map[string][]string{}
	featuredPageKey := "genre:featured"

	coldstartPool := new(ColdstartTitlePool)
	coldstartPool.Init(h.redisPoolMeta)

	if err := loadPlanLustTitleIDs(h.redisPoolMeta); err != nil {
		log.Println("[ERROR] loadPlanLustTitleIDs failed: ", err)
		return err
	}

	for _, titleDetail := range titleDetails {
		// 將 title 放到對應的 coldstart titles pool
		country := ""
		if titleDetail.Country != nil {
			country = titleDetail.Country.CollectionName
		}

		for _, genre := range titleDetail.Genres {
			genreCountryKey := fmt.Sprintf("genre:%s+country:%s", genre.CollectionName, country)
			if _, ok := coldstartPool.TitleIDsMap[genreCountryKey]; ok {
				coldstartPool.TitleIDsMap[genreCountryKey] = append(coldstartPool.TitleIDsMap[genreCountryKey], titleDetail.ID)
			}

			genreKey := fmt.Sprintf("genre:%s", genre.CollectionName)
			if _, ok := coldstartPool.TitleIDsMap[genreKey]; ok {
				coldstartPool.TitleIDsMap[genreKey] = append(coldstartPool.TitleIDsMap[genreKey], titleDetail.ID)
			}
		}

		for _, theme := range titleDetail.Themes {
			themeKey := fmt.Sprintf("theme:%s", theme.CollectionName)
			if _, ok := coldstartPool.TitleIDsMap[themeKey]; ok {
				coldstartPool.TitleIDsMap[themeKey] = append(coldstartPool.TitleIDsMap[themeKey], titleDetail.ID)
			}
		}

		// 過濾成人內容
		if !titleDetail.ChildLock {
			// 加入精選頁
			collectionTitlesMap[featuredPageKey] = append(collectionTitlesMap[featuredPageKey], titleDetail.ID)

			// 依 title 設定的 genres 將 title 加入所屬的 genre 頁
			genres := ""
			for i, genre := range titleDetail.Genres {
				collectionKey := fmt.Sprintf("genre:%s", genre.CollectionName)
				collectionTitlesMap[collectionKey] = append(collectionTitlesMap[collectionKey], titleDetail.ID)

				// for logging title genres
				genres += genre.CollectionName
				if i < len(titleDetail.Genres)-1 {
					genres += ","
				}
			}
			log.Printf("[INFO] title id: %v, title name: %v, genres: %v\n", titleDetail.ID, titleDetail.Title, genres)
		}

		if _, ok := lustPlanTitleIDs[titleDetail.ID]; ok {
			const collectionKey = "plan:lust"
			collectionTitlesMap[collectionKey] = append(collectionTitlesMap[collectionKey], titleDetail.ID)
			log.Printf("[INFO] title id: %v, title name: %v, in plan:lust", titleDetail.ID, titleDetail.Title)
		}
	}

	// 寫入 redis
	// 1. 寫入 coldstart options titles hash
	redisPool := h.redisPoolMeta.Master()

	hashCmd := []interface{}{}
	for key, titleIds := range coldstartPool.TitleIDsMap {
		jsonBytes, _ := json.Marshal(titleIds)
		hashCmd = append(hashCmd, key, jsonBytes)
	}
	// 不設定 expire 時間，避免 cache 資料如果因故被清除後沒有資料回傳給 client
	redisPool.Cmd("HMSET", keys.ColdStartOptionTitlesMap(), hashCmd)

	// 2. 寫入 title ranking hash
	hashCmd = []interface{}{}
	for k, v := range collectionTitlesMap {
		jsonBytes, _ := json.Marshal(v)
		hashCmd = append(hashCmd, k, jsonBytes)
	}
	// 不設定 expire 時間，避免 cache 資料如果因故被清除後沒有資料回傳給 client
	redisPool.Cmd("HMSET", keys.CollectionKeyTitleRankingMap(), hashCmd)

	db := h.dbPoolMeta.Master()
	// 查詢 DB 所有有效的排行榜片單
	queryString = `SELECT id, meta FROM meta_titlelist WHERE list_type = 'ranking' AND enabled = TRUE`
	var titleListSlice []dbmeta.TitleList
	err = db.Select(&titleListSlice, queryString)
	if err != nil {
		log.Println("[ERROR] Get ranking title list failed: ", err)
		return err
	}

	// 更新排行榜片單內容
	for _, titleList := range titleListSlice {
		if len(titleList.Meta.Collections) == 0 {
			continue
		}
		collectionKey := titleList.Meta.Collections[0]

		if titleIDs, ok := collectionTitlesMap[collectionKey]; ok {
			if len(titleIDs) >= 10 {
				titleIDs = titleIDs[0:10]
			}

			titleIDsBytes, err := json.Marshal(titleIDs)
			if err != nil {
				log.Println("[ERROR] Marshal title ids failed, error: ", err)
				return err
			}

			log.Printf("[INFO] Update meta_titlelist meta, titlelist id: %d, title_ids: %v\n", titleList.ID, titleIDs)
			queryString = `UPDATE meta_titlelist SET meta = jsonb_set(COALESCE(meta,'{}')::jsonb, '{title_id}', $1), updated_at = NOW() WHERE ID = $2`
			_, err = db.Exec(queryString, string(titleIDsBytes), titleList.ID)
			if err != nil {
				log.Printf("[ERROR] Update meta_titlelist failed, titlelist id: %d error: %v\n", titleList.ID, err)
				return err
			}
		}
	} // for _, titleList := range titleListSlice { ... }
	return nil
}

const (
	awsConfigRegion = "ap-northeast-1"

	amplitudeLogsDatabase             = "kktv_prod_amplitude_logs"
	amplitudeLogsResultOutputLocation = "s3://kktv-athena-query-result-prod-amplitude-logs"
	slackToken                        = "*********************************************************"
)

type Lv0Relation struct {
	Genre       string   `json:"genre"`
	Collections []string `json:"collections"`
}

type Lv1Relation struct {
	TypeName          string              `json:"type"`
	Collection        []string            `json:"collection"`
	MappingCollection map[string][]string `json:"mapping_collection"`
}

type ColdstartConfig struct {
	Lv0RelationMap map[string]Lv0Relation `json:"categories"`
	Lv1RelationMap map[string]Lv1Relation `json:"relations"`
}

var (
	lustPlanTitleIDs map[string]struct{}
)

type ColdstartTitlePool struct {
	Config      ColdstartConfig     `json:"coldstart_config"`
	TitleIDsMap map[string][]string `json:"titles_ids_map"`
}

func (c *ColdstartTitlePool) FetchConfig(redisMeta *datastore.RedisPool) (err error) {
	redisPool := redisMeta.Slave()
	hashMap, err := redisPool.Cmd("HGETALL", keys.GetColdStartConfig()).Map()
	if err != nil {
		return err
	}
	for k, v := range hashMap {
		switch k {
		case "lv0_relation":
			err = json.Unmarshal([]byte(v), &c.Config.Lv0RelationMap)
			if err != nil {
				return err
			}
		case "lv1_relation":
			err = json.Unmarshal([]byte(v), &c.Config.Lv1RelationMap)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (c *ColdstartTitlePool) Init(redisMeta *datastore.RedisPool) *ColdstartTitlePool {
	err := c.FetchConfig(redisMeta)
	if err != nil {
		log.Println("[ERROR] Coldstart.FetchConfig() failed: ", err)
		return nil
	}
	c.TitleIDsMap = make(map[string][]string)

	for k := range c.Config.Lv0RelationMap {
		if _, ok := c.TitleIDsMap[k]; !ok {
			c.TitleIDsMap[k] = []string{}
		}
	}

	for _, lv1Rel := range c.Config.Lv1RelationMap {
		if len(lv1Rel.Collection) > 0 {
			for _, collection := range lv1Rel.Collection {
				if _, ok := c.TitleIDsMap[collection]; !ok {
					c.TitleIDsMap[collection] = []string{}
				}
			}
		}
		if len(lv1Rel.MappingCollection) > 0 {
			for _, mappingCollection := range lv1Rel.MappingCollection {
				for _, collection := range mappingCollection {
					if _, ok := c.TitleIDsMap[collection]; !ok {
						c.TitleIDsMap[collection] = []string{}
					}
				}
			}
		}
	}

	return c
}

func init() {

	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	kkapp.ContextInit()
}

func loadPlanLustTitleIDs(redisMeta *datastore.RedisPool) error {
	//load title ids from redis
	redisPool := redisMeta.Slave()
	jsonStr, err := redisPool.Cmd("HGET", keys.MetaGetServiceGeneralConfig(), keys.MetaServiceGeneralConfigHashKeys.PlanLustTitleIDs).Bytes()
	if err != nil {
		return err
	}

	type titleList struct {
		TitleIDs []string `json:"title_ids"`
	}

	var obj titleList
	err = json.Unmarshal(jsonStr, &obj)
	if err != nil {
		return err
	}

	lustPlanTitleIDs = make(map[string]struct{})
	for _, titleID := range obj.TitleIDs {
		lustPlanTitleIDs[titleID] = struct{}{}
	}
	return nil
}

type awsAthena struct {
	service *athena.Athena
}

// NewAwsAthenaService return aws athena service instance
func NewAwsAthenaService() *awsAthena {
	session := session.New()
	awsConfig := &aws.Config{
		Region: aws.String(awsConfigRegion),
	}
	svc := athena.New(session, awsConfig)
	return &awsAthena{
		service: svc,
	}
}

func (a *awsAthena) QueryAmplitudeLogs(queryString string) ([]*athena.Row, error) {
	var input athena.StartQueryExecutionInput
	input.SetQueryString(queryString)

	var context athena.QueryExecutionContext
	// 目前只有建立 prod 的 amplitude logs database
	context.SetDatabase(amplitudeLogsDatabase)
	input.SetQueryExecutionContext(&context)

	var resultConfig athena.ResultConfiguration
	resultConfig.SetOutputLocation(amplitudeLogsResultOutputLocation)
	input.SetResultConfiguration(&resultConfig)

	result, err := a.service.StartQueryExecution(&input)
	if err != nil {
		log.Println("[ERROR] AWS Athena StartQueryExecution failed: ", err)
		return nil, err
	}
	log.Println("[INFO] StartQueryExecution result: ", result.GoString())

	var qri athena.GetQueryExecutionInput
	qri.SetQueryExecutionId(*result.QueryExecutionId)

	var qrop *athena.GetQueryExecutionOutput
	duration := time.Duration(2) * time.Second // Pause for 2 seconds

	var state string
	for {
		qrop, err = a.service.GetQueryExecution(&qri)
		if err != nil {
			log.Println("[ERROR] AWS Athena GetQueryExecution failed: ", err)
			return nil, err
		}

		state = *qrop.QueryExecution.Status.State
		if state == "SUCCEEDED" || state == "FAILED" || state == "CANCELLED" {
			break
		}
		log.Println("[INFO] waiting.")
		time.Sleep(duration)
	}

	log.Println("[INFO] AWS Athena GetQueryExecution state: ", state)

	if state == "SUCCEEDED" {
		return a.IterateAmplitudeLogsResults(*result.QueryExecutionId, nil, nil)

	} else {
		return nil, err
	}
}

func (a *awsAthena) IterateAmplitudeLogsResults(queryExecutionId string, nextToken *string, resultSetRows []*athena.Row) ([]*athena.Row, error) {
	var err error
	var ip athena.GetQueryResultsInput
	ip.SetQueryExecutionId(queryExecutionId)

	if nextToken != nil {
		log.Println("[INFO] SetNextToken:", *nextToken)
		ip.SetNextToken(*nextToken)
	}
	op, err := a.service.GetQueryResults(&ip)
	if err != nil {
		log.Println("[ERROR] AWS Athena GetQueryResults failed: ", err)
		return nil, err
	}
	if resultSetRows == nil {
		resultSetRows = op.ResultSet.Rows
	} else {
		resultSetRows = append(resultSetRows, op.ResultSet.Rows...)
	}
	log.Println("[INFO] data length: ", len(op.ResultSet.Rows))
	log.Println("[INFO] total data length: ", len(resultSetRows))

	if op.NextToken != nil {
		log.Println("[INFO] NextToken:", *op.NextToken)
		return a.IterateAmplitudeLogsResults(queryExecutionId, op.NextToken, resultSetRows)

	} else {
		return resultSetRows, err
	}
}

func SendSlackMessage(channel, msg string, attach slack.Attachment) {
	slackAPI := slack.New(slackToken)

	channelID, timestamp, err := slackAPI.PostMessage(
		channel,
		slack.MsgOptionText(msg, false),
		slack.MsgOptionAttachments(attach),
		slack.MsgOptionUsername(fmt.Sprintf("%s#%s(%s)", lambdacontext.FunctionName, lambdacontext.FunctionVersion, config.Env)),
	)
	if err != nil {
		log.Printf("Unable to send slack message to %s, err: %v", channel, err)
	}
	log.Printf("Message successfully sent to channel %s at %s\n", channelID, timestamp)
}
