//go:generate mockgen -source user_repository.go -destination user_repository_mock.go -package user
package user

import (
	"fmt"
	"sync"

	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

var (
	userRepo     Repository
	onceUserRepo sync.Once
)

type MembershipDetail struct {
	ID         string            `db:"id"`
	Role       dbuser.Role       `db:"role"`
	Type       dbuser.Type       `db:"type"`
	Membership dbuser.Membership `db:"membership"`
}

type Repository interface {
	ListByUnsyncedExpired() ([]*MembershipDetail, error)
	ListByUnsyncedFreeTrial() ([]*MembershipDetail, error)
	ListByUnsyncedPR() ([]*MembershipDetail, error)
	ListByUnsyncedPremium() ([]*MembershipDetail, error)
}

type userRepository struct {
	dbReader database.DB
}

func NewUserRepository(dbReader database.DB) Repository {
	onceUserRepo.Do(func() {
		userRepo = &userRepository{
			dbReader: dbReader,
		}
	})
	return userRepo
}

func (u *userRepository) ListByUnsyncedExpired() ([]*MembershipDetail, error) {
	return u.listByUnmatched("role = 'expired'", dbuser.MemberRoleExpired.String())
}

func (u *userRepository) ListByUnsyncedPR() ([]*MembershipDetail, error) {
	return u.listByUnmatched("role = 'premium' AND type = 'pr'", dbuser.MemberRolePR.String())
}

func (u *userRepository) ListByUnsyncedFreeTrial() ([]*MembershipDetail, error) {
	return u.listByUnmatched("role = 'freetrial'", dbuser.MemberRoleFreeTrial.String())
}

func (u *userRepository) ListByUnsyncedPremium() ([]*MembershipDetail, error) {
	return u.listByUnmatched("role = 'premium' AND type != 'pr'", dbuser.MemberRolePremium.String())
}

func (u *userRepository) listByUnmatched(whereClause string, memberRole string) ([]*MembershipDetail, error) {
	basic := fmt.Sprintf(`SELECT id, role, type, membership FROM users WHERE %s AND revoked_at IS NULL `, whereClause)
	notMatched := make([]*MembershipDetail, 0)
	if err := u.dbReader.Select(&notMatched, basic+fmt.Sprintf(`AND membership != '[{"role": "%s"}]';`, memberRole)); err != nil {
		return nil, err
	}

	nullMembership := make([]*MembershipDetail, 0)
	if err := u.dbReader.Select(&nullMembership, basic+`AND membership IS NULL;`); err != nil {
		return nil, err
	}
	return append(notMatched, nullMembership...), nil
}
