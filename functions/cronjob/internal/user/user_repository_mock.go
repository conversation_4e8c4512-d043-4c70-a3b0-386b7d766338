// Code generated by MockGen. DO NOT EDIT.
// Source: user_repository.go

// Package user is a generated GoMock package.
package user

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// ListByUnsyncedExpired mocks base method.
func (m *MockRepository) ListByUnsyncedExpired() ([]*MembershipDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByUnsyncedExpired")
	ret0, _ := ret[0].([]*MembershipDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByUnsyncedExpired indicates an expected call of ListByUnsyncedExpired.
func (mr *MockRepositoryMockRecorder) ListByUnsyncedExpired() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByUnsyncedExpired", reflect.TypeOf((*MockRepository)(nil).ListByUnsyncedExpired))
}

// ListByUnsyncedFreeTrial mocks base method.
func (m *MockRepository) ListByUnsyncedFreeTrial() ([]*MembershipDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByUnsyncedFreeTrial")
	ret0, _ := ret[0].([]*MembershipDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByUnsyncedFreeTrial indicates an expected call of ListByUnsyncedFreeTrial.
func (mr *MockRepositoryMockRecorder) ListByUnsyncedFreeTrial() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByUnsyncedFreeTrial", reflect.TypeOf((*MockRepository)(nil).ListByUnsyncedFreeTrial))
}

// ListByUnsyncedPR mocks base method.
func (m *MockRepository) ListByUnsyncedPR() ([]*MembershipDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByUnsyncedPR")
	ret0, _ := ret[0].([]*MembershipDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByUnsyncedPR indicates an expected call of ListByUnsyncedPR.
func (mr *MockRepositoryMockRecorder) ListByUnsyncedPR() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByUnsyncedPR", reflect.TypeOf((*MockRepository)(nil).ListByUnsyncedPR))
}

// ListByUnsyncedPremium mocks base method.
func (m *MockRepository) ListByUnsyncedPremium() ([]*MembershipDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByUnsyncedPremium")
	ret0, _ := ret[0].([]*MembershipDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByUnsyncedPremium indicates an expected call of ListByUnsyncedPremium.
func (mr *MockRepositoryMockRecorder) ListByUnsyncedPremium() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByUnsyncedPremium", reflect.TypeOf((*MockRepository)(nil).ListByUnsyncedPremium))
}
