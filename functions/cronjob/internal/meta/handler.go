package meta

import (
	"fmt"

	legacydbmeta "github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper/meta"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/encoding"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbmeta"
	"github.com/duke-git/lancet/v2/slice"
	"golang.org/x/sync/errgroup"
	"gopkg.in/guregu/null.v3"
)

type Handler struct {
	titleRepo       meta.TitleRepository
	metaCacheWriter cache.Cacher
	legacyHelper    legacyHelper
}

func New<PERSON>andler(titleRepo meta.TitleRepository, metaCacheWriter, metaCacheReader cache.Cacher) *Handler {
	return &Handler{
		titleRepo:       titleRepo,
		metaCacheWriter: metaCacheWriter,
		legacyHelper: &legacy{
			cacheReader: metaCacheReader,
		},
	}
}

func (h *Handler) UpdateUnitedTitles() error {

	groups, err := h.titleRepo.ListAllUnitedTitleIDs()
	if err != nil {
		return fmt.Errorf("title repo: fail to list united ids, %w", err)
	}

	// batch update for every 10 items
	for i := 0; i < len(groups); i += 10 {
		end := i + 10
		if end > len(groups) {
			end = len(groups)
		}
		batch := groups[i:end]
		if err := h.updateUnitedTitles(batch...); err != nil {
			log.Error("MetaHandler: UpdateUnitedTitles: fail to update united titles").Err(err).Interface("batch", batch).Send()
		}
	}

	return nil
}

func (h *Handler) updateUnitedTitles(unitedTitleGroups ...meta.UnitedTitleGroup) error {
	log.Info("MetaHandler: UpdateUnitedTitles: start to update united titles").Interface("groups", unitedTitleGroups).Send()

	var g errgroup.Group
	for _, i := range unitedTitleGroups {
		item := i
		g.Go(func() error {

			legacyDetails, err := h.listOriginTitleDetails(item.OriginTitleIDs)
			if err != nil {
				return fmt.Errorf("meta handler: fail to get title details, %w", err)
			}
			if len(legacyDetails) == 0 {
				log.Debug("MetaHandler: UpdateUnitedTitle: no title details found").Interface("item", item).Send()
				return nil
			}

			partitions := slice.Partition(legacyDetails, func(d *legacyTitleMeta) bool {
				return d.ID == item.UnitedID
			})
			if found := len(partitions) == 2 && len(partitions[0]) > 0; !found {
				return fmt.Errorf("meta handler: fail to find united title %s", item.UnitedID)
			}
			unitedTitle, othersTitles := partitions[0][0], partitions[1]

			byt, err := encoding.JsonEncode(genUnitedTitleDetail(item.UnitedID, unitedTitle, othersTitles))
			if err != nil {
				return fmt.Errorf("meta handler: json marshal fail, %w", err)
			}
			cKey := key.MetaDataUnitedTitleDetail(item.UnitedID)
			if err := h.metaCacheWriter.HmSet(cKey, []any{"whole", byt}); err != nil {
				return fmt.Errorf("meta handler: cache fail to set: %s, %w", cKey, err)
			}
			return nil
		})
	}

	if err := g.Wait(); err != nil {
		return err
	}
	return nil
}

func genUnitedTitleDetail(unitedID string, unitedTitle *legacyTitleMeta, othersTitles []*legacyTitleMeta) *cachemeta.UnitedTitleDetail {
	seriesIDToTitleMap := map[string]*legacyTitleMeta{}
	for _, s := range unitedTitle.Series {
		seriesIDToTitleMap[s.ID] = unitedTitle
	}
	for _, t := range othersTitles {
		for _, s := range t.Series {
			seriesIDToTitleMap[s.ID] = t
		}
	}

	episodeOfSeriesCount := map[string]int{}
	parent := unitedTitle
	licenseStart, licenseEnd := parent.TotalLicenseStart, parent.TotalLicenseEnd
	titleName := parent.Title
	if parent.UnitedName != "" {
		titleName = parent.UnitedName
	}
	cacheTitle := cachemeta.UnitedTitleDetail{
		UnitedID:         unitedID,
		Name:             titleName,
		TitleType:        dbmeta.TitleType(parent.TitleType),
		Status:           dbmeta.TitleStatus(parent.Status),
		IsEnding:         parent.IsEnding,
		IsContainingAVOD: parent.IsContainingAvod,
		FreeTrial:        parent.FreeTrial,
		ChildLock:        parent.ChildLock,
		Available:        parent.Available,
		IsValidated:      parent.IsValidated,
		StartYear:        parent.ReleaseYear,
		EndYear:          parent.EndYear,
		Cover:            parent.Cover,
		Stills:           parent.Stills,
		ReleaseInfo:      parent.ReleaseInfo,
		LatestUpdateInfo: parent.LatestUpdateInfo,
		AiringInfo:       parent.AiringInfo,
		Summary:          parent.Summary,
		Copyright:        parent.Copyright,
		Genres:           collectionItemsToStrArr(parent.Genres),
		Themes:           collectionItemsToStrArr(parent.Themes),
		Tags:             collectionItemsToStrArr(parent.Tags),
		Casts:            collectionItemsToStrArr(parent.Casts),
		Producers:        collectionItemsToStrArr(parent.Producers),
		Directors:        collectionItemsToStrArr(parent.Directors),
		ContentProviders: collectionItemsToStrArr(parent.ContentProviders),
		Writers:          collectionItemsToStrArr(parent.Writers),
		TitleAliases:     parent.TitleAliases,
		WikiOrig:         parent.WikiOrig,
		WikiZh:           parent.WikiZh,
		ContentRating:    null.IntFrom(parent.Rating),
		// [BEGIN] use the parent's rating for temporary, should be replaced by the average rating of all titles
		UserRating:      parent.UserRating,
		RatingUserCount: parent.UserRatingCount,
		// [END]
	}
	for _, v := range othersTitles {
		if v.TotalLicenseStart > 0 && v.TotalLicenseStart < licenseStart {
			licenseStart = v.TotalLicenseStart
		}
		if v.TotalLicenseEnd > licenseEnd {
			licenseEnd = v.TotalLicenseEnd
		}
	}

	if sameAs := parent.SameAs; sameAs != nil {
		cacheTitle.SameAs = &dbmeta.SameAs{
			Imdb:  sameAs.Imdb,
			Wiki:  sameAs.Wiki,
			Other: sameAs.Other,
		}
	}
	if review := parent.Review; review != nil {
		cacheTitle.ContentDesc = review["content"]
	}
	if country := parent.Country; country != nil {
		cacheTitle.Country = country.CollectionName
	}

	allSeries := slice.Concat(
		parent.Series,
		slice.FlatMap(othersTitles, func(i int, v *legacyTitleMeta) []*legacydbmeta.Series {
			return v.Series
		}),
	)

	cacheSeriesList := make([]cachemeta.UnitedSeriesDetail, 0)
	for _, s := range allSeries {
		if s.UnitedID.IsZero() {
			continue
		}
		episodeOfSeriesCount[s.ID] = len(s.Episodes)
		title := seriesIDToTitleMap[s.ID]
		seriesDetail := cachemeta.UnitedSeriesDetail{
			ID:                 s.ID,
			UnitedID:           s.UnitedID.String,
			Name:               s.Title,
			AudioTrackLanguage: s.AudioTrackLanguage,
			IsContainingAVOD:   s.IsContainingAvod,
			Summary:            s.Summary,
			Cover:              title.Cover,
			Stills:             title.Stills,
			Writers:            s.Writers,
			Producers:          s.Producers,
			Casts:              s.Casts,
			Directors:          s.Directors,
			ContentProvider:    s.ContentProvider,
			Copyright:          s.Copyright,
		}
		if !s.UnitedName.IsZero() {
			seriesDetail.Name = s.UnitedName.String
		}
		if ost := title.Ost; ost != nil {
			seriesDetail.Ost = &dbmeta.Ost{
				ArtistName: ost.ArtistName,
				Image:      ost.Image,
				Title:      ost.Title,
				URL:        ost.URL,
			}
		}
		cacheEpisodes := make([]cachemeta.Episode, 0, len(s.Episodes))
		for _, e := range s.Episodes {
			epDetail := cachemeta.Episode{
				ID:                    e.ID,
				Name:                  e.Title,
				Duration:              e.Duration,
				EndOffset:             e.EndOffset,
				HasSubtitles:          e.HasSubtitles,
				IsValidated:           e.IsValidated,
				IsAVOD:                e.IsAvod,
				LicenseEnd:            e.LicenseEnd,
				LicenseStart:          e.LicenseStart,
				PublishDate:           e.PublishTime,
				Still:                 e.Still,
				SubtitleFileLocations: e.SubtitleMap,
				UpdatedAt:             e.UpdatedAt,
			}
			if hls := e.Mezzanines.Hls; hls != nil {
				epDetail.VideoURL = hls.URI
			}
			cacheEpisodes = append(cacheEpisodes, epDetail)
		}
		seriesDetail.Episodes = cacheEpisodes
		cacheSeriesList = append(cacheSeriesList, seriesDetail)
	}
	cacheTitle.Series = cacheSeriesList
	cacheTitle.SeriesCount = len(cacheSeriesList)
	cacheTitle.EpisodeOfSeriesCount = episodeOfSeriesCount
	cacheTitle.TotalLicenseStart = licenseStart
	cacheTitle.TotalLicenseEnd = licenseEnd

	return &cacheTitle
}

func collectionItemsToStrArr(col []*legacydbmeta.CollectionItem) []string {
	if len(col) == 0 {
		return nil
	}
	filtered := slice.Filter(col, func(i int, v *legacydbmeta.CollectionItem) bool {
		return v != nil
	})

	return slice.Map(filtered, func(i int, v *legacydbmeta.CollectionItem) string {
		return v.CollectionName
	})
}

func (h *Handler) listOriginTitleDetails(originTitleIDs []string) ([]*legacyTitleMeta, error) {
	details := make([]*legacyTitleMeta, 0)
	for _, id := range originTitleIDs {
		detail, err := h.legacyHelper.GetTitleByID(id)
		if err != nil {
			return nil, fmt.Errorf("meta handler: fail to get title by id, %w", err)
		} else if detail == nil {
			continue
		}
		details = append(details, detail)
	}
	return details, nil
}
