package config

import (
	"github.com/caarlos0/env/v7"
	"github.com/joho/godotenv"
)

var (
	Env               string
	RedisMeta         []string
	DBMeta            []string
	DBUser            []string
	SlackToken        string
	ElasticSearchHost string
)

type Config struct {
	Env               string   `env:"ENV,notEmpty"`
	RedisMeta         []string `env:"REDISMETA,notEmpty" envSeparator:","`
	DBMeta            []string `env:"DBMETA,notEmpty" envSeparator:","`
	DBUser            []string `env:"DBUSER,notEmpty" envSeparator:","`
	SlackToken        string   `env:"SLACK_TOKEN,notEmpty"`
	ElasticSearchHost string   `env:"SEARCHHOST,notEmpty"`
}

func Init() error {
	_ = godotenv.Load()

	var cfg Config
	if err := env.Parse(&cfg); err != nil {
		return err
	}
	Env = cfg.Env
	RedisMeta = cfg.RedisMeta
	DBMeta = cfg.DBMeta
	DBUser = cfg.DBUser
	SlackToken = cfg.SlackToken
	ElasticSearchHost = cfg.ElasticSearchHost
	return nil
}
