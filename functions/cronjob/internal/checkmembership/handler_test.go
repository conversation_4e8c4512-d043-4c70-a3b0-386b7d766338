package checkmembership

import (
	"testing"

	"github.com/KKTV/kktv-api-v3/functions/cronjob/internal/pkg/slack"
	"github.com/KKTV/kktv-api-v3/functions/cronjob/internal/user"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/golang/mock/gomock"
	slackapi "github.com/nlopes/slack"
	"github.com/stretchr/testify/suite"
)

type HandlerSuite struct {
	suite.Suite

	hdr             *handler
	mockUserRepo    *user.MockRepository
	ctrl            *gomock.Controller
	mockSlackClient *slack.MockClient
}

func TestHandlerSuite(t *testing.T) {
	suite.Run(t, new(HandlerSuite))
}

func (suite *HandlerSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockUserRepo = user.NewMockRepository(suite.ctrl)
	suite.mockSlackClient = slack.NewMockClient(suite.ctrl)

	suite.hdr = &handler{
		env:      "prod",
		userRepo: suite.mockUserRepo,
		slackAPI: suite.mockSlackClient,
	}
}

func (suite *HandlerSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func (suite *HandlerSuite) TestCheck() {
	testcases := []struct {
		name       string
		given      func()
		thenAssert func(err error)
	}{
		{
			name: "Not found wrong membership",
			given: func() {
				suite.mockUserRepo.EXPECT().ListByUnsyncedExpired().Return(nil, nil)
				suite.mockUserRepo.EXPECT().ListByUnsyncedFreeTrial().Return(nil, nil)
				suite.mockUserRepo.EXPECT().ListByUnsyncedPR().Return(nil, nil)
				suite.mockUserRepo.EXPECT().ListByUnsyncedPremium().Return(nil, nil)
			},
			thenAssert: func(err error) {
				suite.NoError(err)
			},
		},
		{
			name: "found wrong membership records in 2 queries, THEN ignore AnimePass membership",
			given: func() {
				users := []*user.MembershipDetail{
					{
						ID:   "josie1",
						Role: dbuser.RoleFreeTrial, Type: dbuser.TypeGeneral,
						Membership: nil,
					},
					{
						ID:   "josie2",
						Role: dbuser.RoleExpired, Type: dbuser.TypeGeneral,
						Membership: dbuser.MembershipPremiumOnly,
					},
				}
				suite.mockUserRepo.EXPECT().ListByUnsyncedExpired().Return(nil, nil)
				suite.mockUserRepo.EXPECT().ListByUnsyncedFreeTrial().Return(users, nil)
				suite.mockUserRepo.EXPECT().ListByUnsyncedPR().Return(nil, nil)
				suite.mockUserRepo.EXPECT().ListByUnsyncedPremium().Return([]*user.MembershipDetail{
					{
						ID:   "josie3",
						Role: dbuser.RolePremium, Type: dbuser.TypeGeneral,
						Membership: dbuser.Membership{{Role: dbuser.MemberRolePaidAnime}},
					},
				}, nil)

				suite.mockSlackClient.EXPECT().PostMessage("#kktv-log-prod",
					gomock.Any(), gomock.Any(), gomock.Any()).
					DoAndReturn(func(ch string, opts ...slackapi.MsgOption) (string, error) {
						suite.Equal("#kktv-log-prod", ch)
						suite.Len(opts, 3)
						return "testchannelid", nil
					})
			},
			thenAssert: func(err error) {
				suite.NoError(err)
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()
			err := suite.hdr.Check()
			tc.thenAssert(err)
		})
	}
}
