package checkmembership

import (
	"fmt"
	"reflect"
	"runtime"
	"strings"

	"github.com/KKTV/kktv-api-v3/functions/cronjob/internal/pkg/slack"
	"github.com/KKTV/kktv-api-v3/functions/cronjob/internal/user"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/aws/aws-lambda-go/lambdacontext"
	"github.com/duke-git/lancet/v2/slice"
	slackapi "github.com/nlopes/slack"
)

type handler struct {
	userRepo user.Repository
	slackAPI slack.Client
	env      string
}

func <PERSON>Handler(userRepo user.Repository, slackAPI slack.Client, env string) *handler {
	return &handler{
		userRepo: userRepo,
		slackAPI: slackAPI,
		env:      env,
	}
}

func getFunctionName(i any) string {
	return runtime.FuncForPC(reflect.ValueOf(i).Pointer()).Name()
}

func (h *handler) Check() error {
	queryFuncs := []func() ([]*user.MembershipDetail, error){
		h.userRepo.ListByUnsyncedExpired,
		h.userRepo.ListByUnsyncedFreeTrial,
		h.userRepo.ListByUnsyncedPR,
		h.userRepo.ListByUnsyncedPremium,
	}
	invalidRecords := make([]*user.MembershipDetail, 0)
	for _, f := range queryFuncs {
		userIDs, err := f()
		funcName := getFunctionName(f)
		if err != nil {
			return fmt.Errorf("user repo: list with %s failed: %w", funcName, err)
		}
		if len(userIDs) == 0 {
			log.Info("CheckMembership: no invalid records found").Str("func", funcName).Send()
			continue
		}
		log.Info("CheckMembership: invalid records found").Str("func", funcName).Int("count", len(userIDs)).Send()
		invalidRecords = append(invalidRecords, userIDs...)
	}

	if len(invalidRecords) == 0 {
		log.Info("CheckMembership: finally no invalid records found").Send()
		return nil
	}
	invalidRecords = slice.Filter(invalidRecords, func(idx int, r *user.MembershipDetail) bool {
		ignoreAnimePass := r.Role == dbuser.RolePremium && r.Membership.Equal(dbuser.Membership{{Role: dbuser.MemberRolePaidAnime}})
		return !ignoreAnimePass
	})

	h.notify(invalidRecords)

	return nil
}

func (h *handler) notify(invalidRecords []*user.MembershipDetail) {
	invalidIDs := make([]string, 0)
	for _, r := range invalidRecords {
		invalidIDs = append(invalidIDs, r.ID)
	}

	query := fmt.Sprintf(`SELECT id, role, type, membership FROM users WHERE id IN ('%s')`, strings.Join(invalidIDs, "','"))
	log.Warn("CheckMembership: invalid records found").Int("count", len(invalidIDs)).
		Strs("ids", invalidIDs).Str("query", query).Send()

	env := h.env
	if env != "prod" {
		return
	}

	msg := fmt.Sprintf("CheckMembership: invalid records found: %d", len(invalidIDs))

	attach := slackapi.Attachment{
		Color: "danger",
		Fields: []slackapi.AttachmentField{
			{
				Title: "Invalid Records",
				Value: strings.Join(invalidIDs, "\n"),
			},
			{
				Title: "Query",
				Value: fmt.Sprintf("```%s```", query),
			},
		},
	}
	channel := fmt.Sprintf("#kktv-log-%s", env)
	h.sendToSlack(channel, msg, attach)
}

func (h *handler) sendToSlack(channel, msg string, attach slackapi.Attachment) {

	channelID, err := h.slackAPI.PostMessage(
		channel,
		slackapi.MsgOptionText(msg, false),
		slackapi.MsgOptionAttachments(attach),
		slackapi.MsgOptionUsername(fmt.Sprintf("%s#%s(%s)", lambdacontext.FunctionName, lambdacontext.FunctionVersion, h.env)),
	)
	if err != nil {
		log.Warn("CheckMembership: send slack message failed").Err(err).
			Str("msg", msg).Interface("attach", attach).Send()
	} else {
		log.Debug("CheckMembership: send slack message success").Str("msg", msg).Interface("channel_id", channelID).Send()
	}
}
