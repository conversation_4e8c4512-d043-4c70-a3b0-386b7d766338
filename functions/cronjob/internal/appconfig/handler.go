package appconfig

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	myaws "github.com/KKTV/kktv-api-v3/pkg/aws"
	"github.com/KKTV/kktv-api-v3/pkg/aws/ssm"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
)

const (
	maxDurationAwsAppConfigToken = time.Hour * 24
)

type handler struct {
	cacheReader cache.Cacher
	cacheWriter cache.Cacher
	appConfiger ssm.AppConfiger
	clock       clock.Clock
}

func NewHandler(cacheReader, cacheWriter cache.C<PERSON>, appConfiger ssm.AppConfiger) *handler {
	return &handler{
		cacheReader: cacheReader,
		cacheWriter: cacheWriter,
		appConfiger: appConfiger,
		clock:       clock.New(),
	}
}

// Update updates app config from AWS AppConfig to Redis
// refer to the HandlerSuite.TestUpdate test case
func (h *handler) Update() error {
	const appName, profile = "KKTV", "APIConf"

	syncInfo := &SyncInfo{}
	var appConfigPolling ssm.AppConfigPolling
	ctx := context.Background()
	if err := h.cacheReader.Get(key.MetaGetAppConfigSyncInfo(), syncInfo); errors.Is(err, cache.ErrCacheMiss) {
		// token from previous sync not found, then build a new session
		if appConfigPolling, err = h.appConfiger.CreatePolling(ctx, appName, profile); err != nil {
			return err
		}
	} else if err == nil {
		// use token got from previous sync
		if appConfigPolling, err = h.appConfiger.CreatePollingByToken(ctx, syncInfo.NextToken); err != nil {
			return err
		}
	} else {
		return fmt.Errorf("failed to get sync info from cache: %w", err)
	}

	var err error
	defer func() {
		if err != nil {
			// clean the sync info in redis so that it will build a new session to poll next time
			if err := h.cacheWriter.Del(key.MetaGetAppConfigSyncInfo()); err != nil {
				log.Warn("app config: update: fail to delete cache [MetaGetAppConfigSyncInfo]").Err(err).Send()
			}
		}
	}()

	var conf apiConf
	if err = appConfigPolling.Poll(&conf); err == nil {
		// update sync info
		appConfig := createAppConfiguration(conf)
		b, berr := json.Marshal(appConfig)
		if berr != nil {
			err = berr
			return berr
		}
		if err = h.cacheWriter.HSet(key.MetaGetServiceGeneralConfig(), key.MetaServiceGeneralConfigHashKeys.AppConfig, b); err != nil {
			return err
		}
		log.Info("app config: update: successfully updated app config").
			RawJSON("app_config", b).Interface("aws_app_config", conf).Send()
	} else if errors.Is(err, myaws.ErrNoNewData) {
		log.Info("app config: update: no new data").Send()
	} else if err != nil {
		return err
	}

	if err = h.cacheWriter.Set(key.MetaGetAppConfigSyncInfo(), &SyncInfo{
		NextToken:         appConfigPolling.GetNextToken(),
		NextInvokeAfterAt: h.clock.Now().Unix() + appConfigPolling.GetNextPollInterval(),
	}, maxDurationAwsAppConfigToken); err != nil {
		return fmt.Errorf("failed to set sync info to cache: %w", err)
	}
	return nil
}

func createAppConfiguration(conf apiConf) cachemeta.AppConfiguration {
	appConfig := cachemeta.AppConfiguration{}
	if conf.BvIntegration.Enabled {
		appConfig.BvIntegration = &cachemeta.BvIntegration{
			PlatformVersions: conf.BvIntegration.Config.PlatformVersions,
		}
	}
	if conf.BrowseEntrySupportProtect.Enabled {
		appConfig.BrowseEntrySupportProtect = &cachemeta.BrowseEntrySupportProtect{
			PlatformVersions: conf.BrowseEntrySupportProtect.Config.PlatformVersions,
		}
	}
	if conf.V4WatchHistoryAPI.Enabled {
		appConfig.V4WatchHistoryAPI = &cachemeta.V4WatchHistoryAPI{
			PlatformVersions: conf.V4WatchHistoryAPI.Config.PlatformVersions,
		}
	}
	if conf.EnablingMembership.Enabled {
		appConfig.EnablingMembership = &cachemeta.EnablingMembership{}
		appConfig.EnablingMembership.Condition.TriggerType = conf.EnablingMembership.Config.Condition.TriggerType
		appConfig.EnablingMembership.Condition.Args = conf.EnablingMembership.Config.Condition.Args
	}
	if conf.AnimeAiringSchedule.Enabled {
		appConfig.AnimeAiringSchedule = &cachemeta.AnimeAiringSchedule{
			PlatformVersions: conf.AnimeAiringSchedule.Config.PlatformVersions,
		}
	}
	if conf.EnablingAnimeAiring.Enabled {
		appConfig.EnablingAnimeAiring = &cachemeta.EnablingAnimeAiring{}
		appConfig.EnablingAnimeAiring.Condition.TriggerType = conf.EnablingAnimeAiring.Config.Condition.TriggerType
		appConfig.EnablingAnimeAiring.Condition.Args = conf.EnablingAnimeAiring.Config.Condition.Args
	}
	return appConfig
}

type SyncInfo struct {
	NextToken         string `json:"next_token"`
	NextInvokeAfterAt int64  `json:"next_invoke_after_at"` // unix timestamp to next available invoke time
}
