package appconfig

import (
	"encoding/json"
	"errors"
	"testing"
	"time"

	myaws "github.com/KKTV/kktv-api-v3/pkg/aws"
	awsssm "github.com/KKTV/kktv-api-v3/pkg/aws/ssm"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type HandlerSuite struct {
	suite.Suite

	hdr             *handler
	mockCacheReader *cache.MockCacher
	mockCacheWriter *cache.MockCacher
	mockAppConfiger *awsssm.MockAppConfiger
	mockClock       *clock.MockClock
	ctrl            *gomock.Controller
}

func TestHandlerSuite(t *testing.T) {
	suite.Run(t, new(HandlerSuite))
}

func (suite *HandlerSuite) SetupTest() {

	suite.ctrl = gomock.NewController(suite.T())
	suite.mockCacheReader = cache.NewMockCacher(suite.ctrl)
	suite.mockCacheWriter = cache.NewMockCacher(suite.ctrl)
	suite.mockAppConfiger = awsssm.NewMockAppConfiger(suite.ctrl)
	suite.mockClock = clock.NewMockClock(suite.ctrl)
	suite.hdr = &handler{
		cacheReader: suite.mockCacheReader,
		cacheWriter: suite.mockCacheWriter,
		appConfiger: suite.mockAppConfiger,
		clock:       suite.mockClock,
	}
}

func (suite *HandlerSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func (suite *HandlerSuite) TestUpdate() {
	now := time.Date(2020, 4, 29, 0, 0, 0, 0, time.UTC)
	redisKeySync := key.MetaGetAppConfigSyncInfo()
	defaultApiConf := apiConf{
		BvIntegration: bvIntegration{
			freeFormCommon: freeFormCommon{Enabled: true},
			Config: struct {
				PlatformVersions map[string]string `json:"platform_versions"`
			}{
				PlatformVersions: map[string]string{"ios": "1.0.0"},
			},
		},
		BrowseEntrySupportProtect: browseEntrySupportProtect{
			freeFormCommon: freeFormCommon{Enabled: true},
			Config: struct {
				PlatformVersions map[string]string `json:"platform_versions"`
			}{
				PlatformVersions: map[string]string{"android": "1.2.0"},
			},
		},
		V4WatchHistoryAPI: v4WatchHistoryAPI{
			freeFormCommon: freeFormCommon{Enabled: true},
			Config: struct {
				PlatformVersions map[string]string `json:"platform_versions"`
			}{
				PlatformVersions: map[string]string{"android": "3.48.0"},
			},
		},
		EnablingMembership: enablingMembership{
			freeFormCommon: freeFormCommon{Enabled: true},
			Config: struct {
				Condition triggerCondition[[]string] `json:"condition"`
			}{
				Condition: triggerCondition[[]string]{
					TriggerType: "ALL",
				},
			},
		},
	}
	suite.mockClock.EXPECT().Now().Return(now).AnyTimes()

	testcases := []struct {
		name    string
		given   func()
		wantErr error
	}{
		{
			name: "AppConfigSyncInfo exists in Redis, just call GetLatestConfiguration and got newer config",
			given: func() {
				suite.mockCacheReader.EXPECT().Get(redisKeySync, gomock.AssignableToTypeOf(&SyncInfo{})).DoAndReturn(
					func(key string, v interface{}) error {
						*v.(*SyncInfo) = SyncInfo{
							NextToken:         "next_token",
							NextInvokeAfterAt: now.Add(2 * time.Hour).Unix(),
						}
						return nil
					})

				mockPolling := suite.mockPollingActing(defaultApiConf, "next_token2", int64(2*60*60))
				suite.mockAppConfiger.EXPECT().CreatePollingByToken(gomock.Any(), "next_token").Return(mockPolling, nil)

				// assert that the cache is updated
				suite.mockCacheSetAppConfig(cachemeta.AppConfiguration{
					BvIntegration: &cachemeta.BvIntegration{
						PlatformVersions: map[string]string{"ios": "1.0.0"},
					},
					BrowseEntrySupportProtect: &cachemeta.BrowseEntrySupportProtect{
						PlatformVersions: map[string]string{"android": "1.2.0"},
					},
					V4WatchHistoryAPI: &cachemeta.V4WatchHistoryAPI{
						PlatformVersions: map[string]string{"android": "3.48.0"},
					},
					EnablingMembership: &cachemeta.EnablingMembership{
						Condition: cachemeta.TriggerCondition[[]string]{
							TriggerType: "ALL",
						},
					},
				})

				suite.mockCacheWriter.EXPECT().Set(redisKeySync, &SyncInfo{
					NextToken:         "next_token2",
					NextInvokeAfterAt: now.Add(2 * time.Hour).Unix(),
				}, 24*time.Hour).Return(nil)
			},
		},
		{
			name: "AppConfigSyncInfo not in Redis, new session to get AppConfig then put syncInfo to Redis",
			given: func() {
				suite.mockCacheReader.EXPECT().Get(redisKeySync, gomock.AssignableToTypeOf(&SyncInfo{})).Return(cache.ErrCacheMiss)

				mockPolling := suite.mockPollingActing(defaultApiConf, "next_token2", int64(2*60*60))
				suite.mockAppConfiger.EXPECT().CreatePolling(gomock.Any(), "KKTV", "APIConf").Return(mockPolling, nil)

				// assert that the cache is updated
				suite.mockCacheSetAppConfig(cachemeta.AppConfiguration{
					BvIntegration: &cachemeta.BvIntegration{
						PlatformVersions: map[string]string{"ios": "1.0.0"},
					},
					BrowseEntrySupportProtect: &cachemeta.BrowseEntrySupportProtect{
						PlatformVersions: map[string]string{"android": "1.2.0"},
					},
					V4WatchHistoryAPI: &cachemeta.V4WatchHistoryAPI{
						PlatformVersions: map[string]string{"android": "3.48.0"},
					},
					EnablingMembership: &cachemeta.EnablingMembership{
						Condition: cachemeta.TriggerCondition[[]string]{
							TriggerType: "ALL",
						},
					},
				})

				suite.mockCacheWriter.EXPECT().Set(redisKeySync, &SyncInfo{
					NextToken:         "next_token2",
					NextInvokeAfterAt: now.Add(2 * time.Hour).Unix(),
				}, 24*time.Hour).Return(nil)
			},
		},
		{
			name: "fail to poll from aws, then clean up the syncInfo in Redis",
			given: func() {
				suite.mockCacheReader.EXPECT().Get(redisKeySync, gomock.AssignableToTypeOf(&SyncInfo{})).Return(cache.ErrCacheMiss)

				mockPolling := awsssm.NewMockAppConfigPolling(suite.ctrl)
				mockPolling.EXPECT().Poll(gomock.AssignableToTypeOf(&apiConf{})).Return(errors.New("aws error"))
				suite.mockAppConfiger.EXPECT().CreatePolling(gomock.Any(), "KKTV", "APIConf").Return(mockPolling, nil)

				suite.mockCacheWriter.EXPECT().Del(redisKeySync).Return(nil)
			},
			wantErr: errors.New("aws error"),
		},
		{
			name: "AppConfigSyncInfo exists in Redis, just call GetLatestConfiguration but return ErrNoNewData",
			given: func() {
				suite.mockCacheReader.EXPECT().Get(redisKeySync, gomock.AssignableToTypeOf(&SyncInfo{})).DoAndReturn(
					func(key string, v interface{}) error {
						*v.(*SyncInfo) = SyncInfo{
							NextToken:         "next_token",
							NextInvokeAfterAt: now.Add(2 * time.Hour).Unix(),
						}
						return nil
					})

				mockPolling := awsssm.NewMockAppConfigPolling(suite.ctrl)
				mockPolling.EXPECT().Poll(gomock.AssignableToTypeOf(&apiConf{})).Return(myaws.ErrNoNewData)
				// even if there is error of no new data, we still need to call GetNextToken and GetNextPollInterval
				mockPolling.EXPECT().GetNextToken().Return("next_token2")
				mockPolling.EXPECT().GetNextPollInterval().Return(int64(2 * 60 * 60))
				suite.mockAppConfiger.EXPECT().CreatePollingByToken(gomock.Any(), "next_token").Return(mockPolling, nil)

				suite.mockCacheWriter.EXPECT().Set(redisKeySync, &SyncInfo{
					NextToken:         "next_token2",
					NextInvokeAfterAt: now.Add(2 * time.Hour).Unix(),
				}, 24*time.Hour).Return(nil)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()
			err := suite.hdr.Update()
			suite.Equal(err, tc.wantErr)
		})
	}
}

func (suite *HandlerSuite) mockCacheSetAppConfig(ac cachemeta.AppConfiguration) {
	j, _ := json.Marshal(ac)
	suite.mockCacheWriter.EXPECT().HSet(
		key.MetaGetServiceGeneralConfig(), key.MetaServiceGeneralConfigHashKeys.AppConfig, j).Return(nil)
}

func (suite *HandlerSuite) mockPollingActing(af apiConf, nextToken string, nextPollInterval int64) *awsssm.MockAppConfigPolling {
	mockPolling := awsssm.NewMockAppConfigPolling(suite.ctrl)
	mockPolling.EXPECT().Poll(gomock.AssignableToTypeOf(&apiConf{})).DoAndReturn(
		func(val interface{}) error {
			*val.(*apiConf) = af
			return nil
		})
	mockPolling.EXPECT().GetNextToken().Return(nextToken)
	mockPolling.EXPECT().GetNextPollInterval().Return(nextPollInterval) // 2 hours
	return mockPolling
}
