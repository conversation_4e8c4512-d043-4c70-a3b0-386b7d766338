package main

import (
	"context"
	"fmt"
	"os"

	"github.com/KKTV/kktv-api-v3/functions/cronjob/internal/appconfig"
	"github.com/KKTV/kktv-api-v3/functions/cronjob/internal/checkmembership"
	"github.com/KKTV/kktv-api-v3/functions/cronjob/internal/config"
	"github.com/KKTV/kktv-api-v3/functions/cronjob/internal/meta"
	"github.com/KKTV/kktv-api-v3/functions/cronjob/internal/pkg/slack"
	"github.com/KKTV/kktv-api-v3/functions/cronjob/internal/sitemap"
	"github.com/KKTV/kktv-api-v3/functions/cronjob/internal/titleranking"
	userrepo "github.com/KKTV/kktv-api-v3/functions/cronjob/internal/user"
	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	metalib "github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper/meta"
	awsssm "github.com/KKTV/kktv-api-v3/pkg/aws/ssm"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/elasticsearch"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
)

var (
	version = "" // will be replaced when building
)

type Task = string

const (
	TaskUpdateAppConfig      Task = "update_app_config"
	TaskUpdateTitleRanking   Task = "update_title_ranking"
	TaskCheckMembership      Task = "check_membership"
	TaskCacheUnitedTitles    Task = "cache_united_titles"
	TaskGenerateSitemapTitle Task = "generate_sitemap_title"
	// TODO add other tasks
)

type Arg struct {
	Task Task `json:"task"`
}

func init() {
	if err := config.Init(); err != nil {
		log.Fatal("cronjob man: init config fail").Err(err).Send()
	}
	log.Init(config.Env)
	log.Info("init").Str("version", version).
		Str("env", config.Env).Strs("redis_meta", config.RedisMeta).
		Send()
}

type handler struct {
	cacheMetaReader cache.Cacher
	cacheMetaWriter cache.Cacher
	appConfiger     awsssm.AppConfiger
	redisPoolMeta   *datastore.RedisPool
	dbMeta          *datastore.DBPool

	userRepo     userrepo.Repository
	libTitleRepo metalib.TitleRepository
}

func (h *handler) Handle(ctx context.Context, arg Arg) error {
	log.Info("start to handle cronjob").Interface("arg", arg).Send()
	var err error
	defer func() {
		log.Info("finish to handle cronjob").Interface("arg", arg).Err(err).Send()
	}()
	// Dispatch task
	switch arg.Task {
	case TaskUpdateAppConfig:
		ah := appconfig.NewHandler(h.cacheMetaReader, h.cacheMetaWriter, h.appConfiger)
		if err2 := ah.Update(); err2 != nil {
			err = fmt.Errorf("cronjob: update app config fail: %w", err2)
		}
	case TaskUpdateTitleRanking:
		hlr := titleranking.NewHandler(h.redisPoolMeta, h.cacheMetaReader, h.cacheMetaWriter, h.dbMeta)
		if herr := hlr.RefreshRanking(); herr != nil {
			err = fmt.Errorf("cronjob: update title ranking fail: %w", herr)
		}
	case TaskCheckMembership:
		slackClient := slack.New(config.SlackToken)
		checkMembershipHandler := checkmembership.NewHandler(h.userRepo, slackClient, config.Env)
		if hErr := checkMembershipHandler.Check(); hErr != nil {
			err = fmt.Errorf("cronjob: check membership fail: %w", hErr)
		}
	case TaskCacheUnitedTitles:
		metaHandler := meta.NewHandler(h.libTitleRepo, h.cacheMetaWriter, h.cacheMetaReader)
		if hErr := metaHandler.UpdateUnitedTitles(); hErr != nil {
			err = fmt.Errorf("cronjob: cache united titles fail: %w", hErr)
		}
	case TaskGenerateSitemapTitle:
		generateSitemapTitleHandler := sitemap.NewHandler(config.Env, h.dbMeta.Slave())
		if hErr := generateSitemapTitleHandler.GenerateTitles(); hErr != nil {
			err = fmt.Errorf("cronjob: generate sitemap title fail: %w", hErr)
		}
	// TODO case other tasks
	default: // not defined task
		err = fmt.Errorf("not defined task: %s", arg.Task)
	}
	return err
}

func newDBs(dsns []string) *datastore.DBPool {
	dbs := make([]string, len(dsns))
	for i, dbURI := range dsns {
		dbs[i] = fmt.Sprintf("%s?sslmode=disable", dbURI)
	}
	return datastore.NewDBPool(dbs)
}

func main() {
	// dependency aggregation
	redisPoolMeta := datastore.NewRedisPool(config.RedisMeta)
	cacheMetaReader := cache.New(redisPoolMeta.Slave())
	cacheMetaWriter := cache.New(redisPoolMeta.Master())
	dbMeta := newDBs(config.DBMeta)
	dbUser := newDBs(config.DBUser)
	userDBReader := dbUser.Slave().Unsafe()
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String("ap-northeast-1"),
	})
	if err != nil {
		log.Fatal("fail to create aws session").Err(err).Send()
	}
	appConfiger := awsssm.NewAppConfig(sess, config.Env)

	userRepo := userrepo.NewUserRepository(userDBReader)
	libTitleRepo := metalib.NewTitleRepository(dbMeta.Slave(), cacheMetaWriter, elasticsearch.NewClient(config.ElasticSearchHost))
	// dependency injection
	h := &handler{
		redisPoolMeta:   redisPoolMeta,
		dbMeta:          dbMeta,
		cacheMetaReader: cacheMetaReader,
		cacheMetaWriter: cacheMetaWriter,
		appConfiger:     appConfiger,
		userRepo:        userRepo,
		libTitleRepo:    libTitleRepo,
	}
	// AWS lambda environment, run and exit
	if os.Getenv("AWS_EXECUTION_ENV") != "" {
		lambda.Start(h.Handle)
		return
	}

	/* Local test*/
	if err := h.Handle(context.Background(), Arg{Task: TaskGenerateSitemapTitle}); err != nil {
		log.Fatal("fail to handle").Err(err).Send()
	}
}
