package main

import (
	"bytes"
	"fmt"
	"log"
	"math"
	"net/url"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/functions/generate_mediaaction/internal/meta"
	"github.com/KKTV/kktv-api-v3/functions/generate_mediaaction/internal/pkg/model"
	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"

	"github.com/KKTV/kktv-api-v3/pkg/encoding"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
)

type People struct {
	Type   string `json:"@type"`
	ID     string `json:"@id"`
	Name   string `json:"name"`
	SameAs string `json:"sameAs"`
}

type ReviewRating struct {
	Type        string  `json:"@type"`
	BestRating  string  `json:"bestRating"`
	WorstRating string  `json:"worstRating"`
	RatingValue float64 `json:"ratingValue"`
}

type ImageProperty struct {
	Type  string   `json:"@type"`
	Name  string   `json:"name"`
	Value []string `json:"value"`
}

type Image struct {
	Context         string `json:"@context"`
	Type            string `json:"@type"`
	Name            string `json:"name"`
	ContentUrl      string `json:"contentUrl"`
	CopyrightHolder struct {
		Type string `json:"@type"`
		Name string `json:"name"`
	} `json:"copyrightHolder"`
	AdditionalProperty []ImageProperty `json:"additionalProperty"`
}

type Target struct {
	Type           string   `json:"@type"`
	UrlTemplate    string   `json:"urlTemplate"`
	InLanguage     string   `json:"inLanguage"`
	ActionPlatform []string `json:"actionPlatform"`
}

type Subscription struct {
	Type       string `json:"@type"`
	Name       string `json:"name"`
	CommonTier bool   `json:"commonTier"`
	ID         string `json:"@id"`
	SameAs     string `json:"sameAs"`
}

type Requirement struct {
	Type               string `json:"@type"`
	Category           string `json:"category"`
	AvailabilityStarts string `json:"availabilityStarts"`
	AvailabilityEnds   string `json:"availabilityEnds"`
	EligibleRegion     struct {
		Type string `json:"@type"`
		Name string `json:"name"`
	} `json:"eligibleRegion"`
	Subscription *Subscription `json:"requiresSubscription,omitempty"`
}

type PopularityScore struct {
	Type           string `json:"@type"`
	Value          string `json:"value"`
	EligibleRegion struct {
		Type string `json:"@type"`
		Name string `json:"name"`
	} `json:"eligibleRegion"`
}

type Action struct {
	Type        string      `json:"@type"`
	Target      []Target    `json:"target"`
	Requirement Requirement `json:"actionAccessibilityRequirement"`
}

type Review struct {
	Type         string       `json:"@type"`
	ReviewRating ReviewRating `json:"reviewRating"`
}

type MediaActionFeed struct {
	Context         []interface{}    `json:"@context"`
	Type            string           `json:"@type"`
	ID              string           `json:"@id"`
	URL             string           `json:"url"`
	Name            string           `json:"name"`
	SameAs          []string         `json:"sameAs"`
	Description     string           `json:"description"`
	ContentRating   string           `json:"contentRating"`
	PopularityScore PopularityScore  `json:"popularityScore"`
	InLanguage      string           `json:"inLanguage,omitempty"`
	Duration        string           `json:"duration"`
	Director        []People         `json:"director"`
	Creator         []People         `json:"creator"`
	Actor           []People         `json:"actor"`
	Producer        []People         `json:"producer"`
	Genre           []string         `json:"genre"`
	Keywords        []string         `json:"keywords"`
	Image           []Image          `json:"image"`
	PartOfSeries    *MediaActionFeed `json:"partOfSeries,omitempty"`
	SeasonNumber    int              `json:"seasonNumber,omitempty"`
	PartOfSeason    *MediaActionFeed `json:"partOfSeason,omitempty"`
	EpisodeNumber   int              `json:"episodeNumber,omitempty"`
	Action          Action           `json:"potentialAction"`
	Review          *Review          `json:"review"`
}

type Result struct {
	Context         string            `json:"@context"`
	Type            string            `json:"@type"`
	DateModified    string            `json:dateModified`
	DataFeedElement []MediaActionFeed `json:"dataFeedElement"`
}

func init() {
	log.Println("Lambda thumbnail init")
	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}

	kkapp.ContextInit()
}

// Ternary Operation
func ternaryIf(condition bool, trueVal, falseVal interface{}) interface{} {
	if condition {
		return trueVal
	}
	return falseVal
}

func generateRegex(wording string) (regex *regexp.Regexp) {
	regex = regexp.MustCompile(` *\((` + wording + `)\) *`)
	return
}

// Trim Useless String Values
func reName(title string) (name string) {
	name = generateRegex("中|日|英|韓|台|法|粵|數位修復版|無修正版|大賢者版|電視版|真人版").ReplaceAllString(title, "")
	name = regexp.MustCompile(` *(-院線版|-新譯版|劇場版｜：數位修復版) *`).ReplaceAllString(name, "")
	name = regexp.MustCompile(` *(-是希望還是災禍-) *`).ReplaceAllString(name, "")
	name = regexp.MustCompile(` *(S\w*-S\w*) *`).ReplaceAllString(name, "")
	name = strings.ReplaceAll(name, "三觀賣血", "許三觀")
	name = strings.ReplaceAll(name, "愛我嗎？愛你媽！", "一家人三人行")
	name = strings.ReplaceAll(name, "執行者的告白", "執行者")
	name = strings.ReplaceAll(name, "祈願的病歷表", "祈願病歷表～研修醫的解謎診察紀錄～")
	name = strings.ReplaceAll(name, "賽德克巴萊 (上)：太陽旗", "賽德克．巴萊：太陽旗")
	name = strings.ReplaceAll(name, "賽德克巴萊 (下)：彩虹橋", "賽德克．巴萊：彩虹橋")
	name = strings.ReplaceAll(name, "D4DJ Double Mix OVA", "D4DJ: Double Mix")
	return
}

// Generate Image's Url for Another Size
func getResizeImageUrl(origUrl, size string) (newUrl string) {
	regex := regexp.MustCompile(`xs|sm|md|lg`)
	if !regex.MatchString(origUrl) {
		size = "xs"
	}
	if origUrl == "" {
		return ""
	}
	urlMeta, err := url.Parse(origUrl)

	if err != nil {
		panic(err)
	}

	pathname := urlMeta.Path
	tmpArr := strings.Split(pathname, ".")
	if regex.MatchString(tmpArr[len(tmpArr)-2]) {
		tmpArr = append(tmpArr[:1], tmpArr[2:]...)
	}
	tmpArr = append(tmpArr[:2], tmpArr[1:]...)
	tmpArr[1] = size
	newPathname := strings.Join(tmpArr, ".")

	newUrl = fmt.Sprintf("%v://%v%v", urlMeta.Scheme, urlMeta.Host, newPathname)
	return
}

// Handle Description's Length Must Under 300 (Google Policy)
func generateDescription(summary string) (description string) {
	DESCRIPTION_MAXIMUM := 298
	if len([]rune(summary)) > DESCRIPTION_MAXIMUM {
		description = fmt.Sprintf("%v...", string([]rune(summary)[:DESCRIPTION_MAXIMUM]))
	} else {
		description = summary
	}
	return
}

func generateFeedInLanguage(country string, titleName string, trackLanguage string) string {
	if trackLanguage != "" && trackLanguage != "Unknown" {
		return trackLanguage
	}

	countryLanguageMap := map[string]string{
		"Western":  "en-US",
		"Japan":    "ja-JP",
		"Korea":    "ko-KR",
		"Taiwan":   "zh-TW",
		"Hongkong": "zh-HK",
	}

	titleLanguageMap := map[string]string{
		"英": "en-US",
		"日": "ja-JP",
		"韓": "ko-KR",
		"中": "zh-TW",
	}

	for marker, code := range titleLanguageMap {
		if generateRegex(marker).MatchString(titleName) {
			return code
		}
	}

	if lang, exists := countryLanguageMap[country]; exists {
		return lang
	}

	return ""
}

func appendIfMapping(result []string, value string) []string {
	mapping := map[string][]string{
		"奇幻冒險": {"Fantasy", "Action-Adventure"},
		"紀錄片":  {"Documentary", "Docuseries"},
		"動漫":   {"Animation", "Anime"},
		"劇情":   {"Biographical", "Drama"},
		"浪漫愛情": {"Romance"},
		"戀愛":   {"Romance"},
		"運動競技": {"Sports"},
		"體育賽事": {"Sports"},
		"實境遊戲": {"Reality"},
		"音樂偶像": {"Music"},
		"寓教於樂": {"Kids", "Coming of age"},
		"輕鬆喜劇": {"Comedy"},
		"溫馨喜劇": {"Comedy"},
		"靈異驚悚": {"Horror", "Thriller"},
		"恐怖驚悚": {"Horror", "Thriller"},
		"排球少年": {"Haikyu!!"},
	}
	if mapping[value] != nil {
		result = append(result, mapping[value]...)
	}
	return result
}

func generateFeedGenre(genres, themes, tags []string) (result []string) {
	if len(genres) > 0 {
		for _, genre := range genres {
			if genre != "語言學習" {
				result = append(result, genre)
			}
			result = appendIfMapping(result, genre)
		}
	}
	if len(themes) > 0 {
		for _, theme := range themes {
			if theme != "雙字幕" && theme != "免費" {
				result = append(result, theme)
			}
			result = appendIfMapping(result, theme)
		}
	}
	if len(tags) > 0 {
		for _, tag := range tags {
			result = append(result, tag)
			result = appendIfMapping(result, tag)
		}
	}
	return
}

func generateFeedKeyword(title string, titleAliases []string, casts, themes, tags []string) (result []string) {
	result = append(result, title)
	if len(titleAliases) > 0 {
		result = append(result, titleAliases...)
	}
	if len(casts) > 0 {
		result = append(result, casts...)
	}
	if len(themes) > 0 {
		for _, theme := range themes {
			result = append(result, theme)
			result = appendIfMapping(result, theme)
		}
	}
	if len(tags) > 0 {
		for _, tag := range tags {
			result = append(result, tag)
			result = appendIfMapping(result, tag)
		}
	}
	return
}

func generateFeedContentRating(rating int, childLock bool) (contentRating string) {
	if rating == 2 || rating == 5 {
		contentRating = "G"
	} else if childLock {
		contentRating = "R"
	} else {
		contentRating = "P"
	}
	return
}

func appendPeople(result []People, peopleList []string) []People {
	for _, person := range peopleList {
		result = append(result, People{
			Type:   "Person",
			ID:     fmt.Sprintf("https://zh.wikipedia.org/wiki/%v", person),
			Name:   person,
			SameAs: fmt.Sprintf("https://zh.wikipedia.org/wiki/%v", person),
		})
	}
	return result
}

func generateImage(title, copyright, cover string, stills []string) (result []Image) {
	if cover != "" {
		result = append(result, Image{
			Context:    "http://schema.org",
			Type:       "ImageObject",
			Name:       fmt.Sprintf("%v封面圖", title),
			ContentUrl: getResizeImageUrl(cover, "lg"),
			CopyrightHolder: struct {
				Type string `json:"@type"`
				Name string `json:"name"`
			}{
				Type: "Organization",
				Name: copyright,
			},
			AdditionalProperty: []ImageProperty{{
				Type:  "PropertyValue",
				Name:  "contentAttributes",
				Value: []string{"iconic", "poster", "smallFormat", "largeFormat", "hasTitle", "hasLogo", "noMatte"},
			}},
		})
	}
	if len(stills) > 0 {
		for _, still := range stills {
			result = append(result, Image{
				Context:    "http://schema.org",
				Type:       "ImageObject",
				Name:       fmt.Sprintf("%v劇照", title),
				ContentUrl: getResizeImageUrl(still, "lg"),
				CopyrightHolder: struct {
					Type string `json:"@type"`
					Name string `json:"name"`
				}{
					Type: "Organization",
					Name: copyright,
				},
				AdditionalProperty: []ImageProperty{{
					Type:  "PropertyValue",
					Name:  "contentAttributes",
					Value: []string{"iconic", "background", "smallFormat", "largeFormat", "noTitle", "noLogo", "noMatte"},
				}},
			})
		}
	}
	return
}

// Format Duration from seconds to ISO 8601
func generateDuration(seconds int) (duration string) {
	duration = "PT"
	f64Seconds := float64(seconds)
	h := math.Floor(f64Seconds / 3600)
	m := math.Floor((f64Seconds - h*3600) / 60)
	s := math.Floor(f64Seconds - h*3600 - m*60)

	if h > 0 {
		duration = fmt.Sprintf("%v%dH", duration, int(h))
	}
	if m > 0 {
		duration = fmt.Sprintf("%v%dM", duration, int(m))
	}
	if s > 0 {
		duration = fmt.Sprintf("%v%dS", duration, int(s))
	}

	return
}

func generateAction(episode model.Episode, id string, inLanguage string) (action Action) {
	target := Target{
		Type:        "EntryPoint",
		UrlTemplate: fmt.Sprintf("https://www.kktv.me/play/%v", id),
		InLanguage:  inLanguage,
		ActionPlatform: []string{
			"http://schema.org/DesktopWebPlatform",
			"http://schema.org/MobileWebPlatform",
			"http://schema.org/IOSPlatform",
			"http://schema.org/AndroidPlatform",
			"http://schema.org/AndroidTVPlatform",
		},
	}

	action.Type = "WatchAction"
	action.Target = []Target{
		target,
	}

	currentTime := time.Now()
	if currentTime.After(time.Unix(episode.LicenseEnd, 0)) {
		plog.Debug("debug title").
			Interface("ID", episode.ID).Interface("License End", time.Unix(episode.LicenseEnd, 0)).Send()
	}

	action.Requirement = Requirement{
		Type:               "ActionAccessSpecification",
		Category:           ternaryIf(episode.IsAVOD, "nologinrequired", "subscription").(string),
		AvailabilityStarts: time.Unix(episode.LicenseStart, 0).Format(time.RFC3339),
		AvailabilityEnds:   time.Unix(episode.LicenseEnd, 0).Format(time.RFC3339),
		EligibleRegion: struct {
			Type string `json:"@type"`
			Name string `json:"name"`
		}{
			Type: "Country",
			Name: "TW",
		},
	}
	if !episode.IsAVOD {
		action.Requirement.Subscription = &Subscription{
			Type:       "MediaSubscription",
			Name:       "立即升級享受完整服務",
			CommonTier: true,
			ID:         "https://www.kktv.me/package/individual",
			SameAs:     "https://www.kktv.me/package/individual",
		}
	}
	return
}

func newDefaultResult() (result *Result) {
	result = new(Result)
	result.Context = "http://schema.org"
	result.Type = "DataFeed"
	result.DateModified = time.Now().Format(time.RFC3339)
	result.DataFeedElement = make([]MediaActionFeed, 0)
	return
}

func handleRequest() error {
	movies := newDefaultResult()
	tvSeries := newDefaultResult()
	tvSeasons := newDefaultResult()
	tvEpisodes := newDefaultResult()
	titleRepo := meta.NewTitleRepo(kkapp.App.DbMeta.Slave(), kkapp.App.RedisMeta)

	unitedIDs, err := titleRepo.GetAllUnitedIDs()
	if err != nil {
		plog.Debug("id").Interface("err", err).Send()
		return err
	}

	for _, id := range unitedIDs {
		detail, err := titleRepo.GetTitleDetail(id)
		if err != nil {
			fmt.Println(err)
			return err
		}

		if detail == nil {
			continue
		}

		if detail.TotalSeriesCount == 0 {
			continue
		}

		if len(detail.Series) == 0 {
			continue
		}

		currentTime := time.Now()
		if currentTime.After(time.Unix(detail.TotalLicenseEnd, 0)) {
			continue
		}

		plog.Debug("Generate MediaActionFeed").
			Interface("ID - Name", detail.UnitedID+" - "+detail.Title).Send()
		feed := MediaActionFeed{
			Context: []interface{}{
				"http://schema.org",
				map[string]string{"@language": "zh-TW"},
			},
			Type:          ternaryIf(detail.TitleType == "film", "Movie", "TVSeries").(string),
			ID:            fmt.Sprintf("https://www.kktv.me/titles/%v", detail.UnitedID),
			URL:           fmt.Sprintf("https://www.kktv.me/titles/%v", detail.UnitedID),
			Name:          reName(detail.Title),
			Description:   generateDescription(detail.Summary),
			Genre:         generateFeedGenre(detail.Genres, detail.Themes, detail.Tags),
			Keywords:      generateFeedKeyword(detail.Title, detail.TitleAliases, detail.Casts, detail.Themes, detail.Tags),
			ContentRating: generateFeedContentRating(detail.Rating, detail.ChildLock),
			PopularityScore: PopularityScore{
				Type:  "PopularityScoreSpecification",
				Value: strconv.Itoa(detail.RatingUserCount),
				EligibleRegion: struct {
					Type string `json:"@type"`
					Name string `json:"name"`
				}{
					Type: "Country",
					Name: "TW",
				},
			},
		}

		if detail.SameAs != nil {
			if detail.SameAs.IMDB != "" {
				feed.SameAs = append(feed.SameAs, detail.SameAs.IMDB)
			}
			if detail.SameAs.Wiki != "" {
				feed.SameAs = append(feed.SameAs, detail.SameAs.Wiki)
			}
			if detail.SameAs.Other != "" {
				feed.SameAs = append(feed.SameAs, detail.SameAs.Other)
			}
		}

		if detail.WikiOrig != "" {
			feed.SameAs = append(feed.SameAs, detail.WikiOrig)
		}
		if detail.WikiZh != "" {
			feed.SameAs = append(feed.SameAs, detail.WikiZh)
		}

		inLanguage := generateFeedInLanguage(detail.Country, detail.Title, detail.Series[0].AudioTrackLanguage)
		if inLanguage != "" {
			feed.InLanguage = inLanguage
		}

		if len(detail.Directors) > 0 {
			feed.Director = appendPeople(feed.Director, detail.Directors)
		}

		if len(detail.Writers) > 0 {
			feed.Creator = appendPeople(feed.Creator, detail.Writers)
		}

		if len(detail.Casts) > 0 {
			feed.Actor = appendPeople(feed.Actor, detail.Casts)
		}

		if len(detail.Producers) > 0 {
			feed.Producer = appendPeople(feed.Producer, detail.Producers)
		}

		if detail.UserRating != 0 {
			feed.Review = &Review{
				Type: "Review",
				ReviewRating: ReviewRating{
					Type:        "Rating",
					BestRating:  "5",
					WorstRating: "1",
					RatingValue: detail.UserRating,
				},
			}
		}

		if detail.Cover != "" || len(detail.Stills) > 0 {
			feed.Image = generateImage(detail.Title, detail.Copyright, detail.Cover, detail.Stills)
		}

		if detail.TitleType == "film" {
			feed.Duration = generateDuration(detail.Series[0].Episodes[0].Duration)
			feed.Action = generateAction(detail.Series[0].Episodes[0], detail.UnitedID, inLanguage)
			movies.DataFeedElement = append(movies.DataFeedElement, feed)
			continue
		} else if detail.TitleType == "miniseries" || detail.TitleType == "series" {
			feed.Action = generateAction(detail.Series[0].Episodes[0], detail.UnitedID, inLanguage)
			tvSeries.DataFeedElement = append(tvSeries.DataFeedElement, feed)

			for _, series := range detail.Series {
				seasonFeed := feed
				seasonFeed.Type = "TVSeason"
				seasonFeed.Description = generateDescription(series.Summary)
				seasonFeed.ID = fmt.Sprintf("https://www.kktv.me/titles/%v", series.ID)
				seasonFeed.URL = fmt.Sprintf("https://www.kktv.me/titles/%v", series.ID)
				seasonFeed.Name = series.UnitedName

				if series.AudioTrackLanguage != "" {
					seasonFeed.InLanguage = series.AudioTrackLanguage
				}

				if len(series.Directors) > 0 {
					seasonFeed.Director = appendPeople(seasonFeed.Director, series.Directors)
				}

				if len(series.Writers) > 0 {
					seasonFeed.Creator = appendPeople(seasonFeed.Creator, series.Writers)
				}

				if len(series.Casts) > 0 {
					seasonFeed.Actor = appendPeople(seasonFeed.Actor, series.Casts)
				}

				if len(series.Producers) > 0 {
					seasonFeed.Producer = appendPeople(seasonFeed.Producer, series.Producers)
				}
				seriesNumber, _ := strconv.Atoi(series.UnitedID[len(series.UnitedID)-2:])
				seasonFeed.SeasonNumber = seriesNumber
				seasonFeed.PartOfSeries = &feed
				seasonFeed.Action = generateAction(series.Episodes[0], series.ID, series.AudioTrackLanguage)
				tvSeasons.DataFeedElement = append(tvSeasons.DataFeedElement, seasonFeed)

				for _, episode := range series.Episodes {
					episodeFeed := feed
					episodeFeed.Type = "TVEpisode"
					episodeFeed.ID = fmt.Sprintf("https://www.kktv.me/titles/%v", episode.ID)
					episodeFeed.URL = fmt.Sprintf("https://www.kktv.me/titles/%v", episode.ID)
					episodeFeed.Name = episode.Title
					episodeNumber, _ := strconv.Atoi(episode.ID[len(episode.ID)-4:])
					episodeFeed.EpisodeNumber = episodeNumber
					episodeFeed.PartOfSeason = &seasonFeed
					episodeFeed.PartOfSeries = &feed
					episodeFeed.Duration = generateDuration(episode.Duration)
					episodeFeed.Action = generateAction(episode, episode.ID, series.AudioTrackLanguage)
					tvEpisodes.DataFeedElement = append(tvEpisodes.DataFeedElement, episodeFeed)
				}
			}
		}
	}
	uploadResultToS3(movies, "movies.json")
	uploadResultToS3(tvEpisodes, "tvepisode.json")
	uploadResultToS3(tvSeasons, "tvseason.json")
	uploadResultToS3(tvSeries, "tvseries.json")

	return nil
}

func uploadResultToS3(result *Result, key string) {
	jsonData, err := encoding.JsonEncode(result)
	if err != nil {
		log.Fatal(err)
	}
	s3Client := s3.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})
	_, err = s3Client.PutObject(&s3.PutObjectInput{
		Bucket: aws.String("kktv-media-action-feeds"),
		Key:    aws.String(key),
		Body:   bytes.NewReader(jsonData),
	})
	if err != nil {
		log.Fatal(err)
	} else {
		fmt.Printf("%v uploaded to S3 successfully!\n", key)
	}
}

func main() {
	if os.Getenv("LAMBDA_RUNTIME_DIR") != "" {
		lambda.Start(handleRequest)
	} else {
		// local environment
		handleRequest()
	}
}
