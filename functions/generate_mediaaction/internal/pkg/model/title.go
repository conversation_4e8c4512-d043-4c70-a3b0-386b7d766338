package model

type Title struct {
	UnitedID           string         `json:"united_id" copier:"UnitedID"`
	Title              string         `json:"title" copier:"Name"`
	TitleType          string         `json:"title_type"`
	IsContainingAvod   bool           `json:"is_containing_avod"`
	Summary            string         `json:"summary"`
	Genres             []string       `json:"genres"`
	Themes             []string       `json:"themes"`
	Tags               []string       `json:"tags"`
	TitleAliases       []string       `json:"title_aliases"`
	Casts              []string       `json:"casts"`
	ChildLock          bool           `json:"child_lock"`
	SameAs             *SameAs        `json:"same_as"`
	WikiOrig           string         `json:"wiki_orig"`
	WikiZh             string         `json:"wiki_zh"`
	TotalLicenseStart  int64          `json:"total_license_start"`
	TotalLicenseEnd    int64          `json:"total_license_end"`
	Country            string         `json:"country"`
	Directors          []string       `json:"directors"`
	Writers            []string       `json:"writers"`
	Producers          []string       `json:"producers"`
	Rating             int            `json:"rating" copier:"ContentRating"`
	UserRating         float64        `json:"user_rating"`
	RatingUserCount    int            `json:"rating_user_count"`
	Cover              string         `json:"cover"`
	Stills             []string       `json:"stills"`
	Copyright          string         `json:"copyright"`
	TotalEpisodeCounts map[string]int `json:"total_episode_counts" copier:"EpisodeOfSeriesCount"`
	TotalSeriesCount   int            `json:"total_series_count" copier:"SeriesCount"`
	Series             []Series       `json:"series"`
}

type SameAs struct {
	IMDB  string `json:"imdb"`
	Wiki  string `json:"wiki"`
	Other string `json:"other"`
}
