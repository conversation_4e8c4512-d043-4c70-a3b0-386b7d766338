package model

type Series struct {
	ID                 string    `json:"id"`
	UnitedID           string    `json:"united_id"`
	UnitedName         string    `json:"united_name" copier:"Name"`
	IsContainingAvod   bool      `json:"is_containing_avod"`
	Summary            string    `json:"summary"`
	Directors          []string  `json:"directors"`
	Writers            []string  `json:"writers"`
	Producers          []string  `json:"producers"`
	Casts              []string  `json:"casts"`
	ContentAgent       string    `json:"content_agent"`
	Copyright          string    `json:"copyright"`
	AudioTrackLanguage string    `json:"audio_track_language"`
	Episodes           []Episode `json:"episodes"`
}
