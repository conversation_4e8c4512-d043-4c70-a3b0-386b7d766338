// Code generated by MockGen. DO NOT EDIT.
// Source: title_repository.go

// Package meta is a generated GoMock package.
package meta

import (
	reflect "reflect"

	dbmeta "github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	gomock "github.com/golang/mock/gomock"
)

// MockTitleRepo is a mock of TitleRepo interface.
type MockTitleRepo struct {
	ctrl     *gomock.Controller
	recorder *MockTitleRepoMockRecorder
}

// MockTitleRepoMockRecorder is the mock recorder for MockTitleRepo.
type MockTitleRepoMockRecorder struct {
	mock *MockTitleRepo
}

// NewMockTitleRepo creates a new mock instance.
func NewMockTitleRepo(ctrl *gomock.Controller) *MockTitleRepo {
	mock := &MockTitleRepo{ctrl: ctrl}
	mock.recorder = &MockTitleRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTitleRepo) EXPECT() *MockTitleRepoMockRecorder {
	return m.recorder
}

// ListAll mocks base method.
func (m *MockTitleRepo) ListAll() ([]*dbmeta.Title, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAll")
	ret0, _ := ret[0].([]*dbmeta.Title)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAll indicates an expected call of ListAll.
func (mr *MockTitleRepoMockRecorder) ListAll() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAll", reflect.TypeOf((*MockTitleRepo)(nil).ListAll))
}
