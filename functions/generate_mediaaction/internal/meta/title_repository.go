//go:generate mockgen -source title_repository.go -destination title_repository_mock.go -package meta
package meta

import (
	"errors"

	"github.com/KKTV/kktv-api-v3/functions/generate_mediaaction/internal/pkg/model"
	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	cachemeta "github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	"github.com/jinzhu/copier"
	"github.com/lib/pq"
)

type TitleRepo interface {
	ListAll() ([]*dbmeta.Title, error)
	GetAllUnitedIDs() ([]UnitedTitleGroup, error)
	GetTitleDetail(unitedGroup UnitedTitleGroup) (*model.Title, error)
}

type titleRepo struct {
	dbReader    database.DB
	cacheReader cache.Cacher
}

func NewTitleRepo(dbReader database.DB, redisPoolMeta *datastore.RedisPool) TitleRepo {
	return &titleRepo{
		dbReader:    dbReader,
		cacheReader: cache.New(redisPoolMeta.Slave()),
	}
}

func (r *titleRepo) ListAll() ([]*dbmeta.Title, error) {
	records := make([]*dbmeta.Title, 0)

	if err := r.dbReader.Select(&records, `SELECT id, name, editor_comments, meta, same_as FROM meta_title ORDER BY id;`); err != nil {
		return nil, err
	}

	return records, nil
}

type UnitedTitleGroup struct {
	UnitedID       string         `db:"united_id"`
	OriginTitleIDs pq.StringArray `db:"origin_title_ids"`
}

func (r *titleRepo) GetAllUnitedIDs() ([]UnitedTitleGroup, error) {
	var unitedTitleGroups []UnitedTitleGroup
	q := `SELECT united_id, ARRAY_AGG(id ORDER BY id ASC) AS origin_title_ids
			FROM meta_title
			WHERE united_id IS NOT NULL GROUP BY united_id`
	err := r.dbReader.Select(&unitedTitleGroups, q)
	if err != nil {
		return nil, err
	}
	return unitedTitleGroups, nil
}

func (r *titleRepo) GetTitleDetail(unitedGroup UnitedTitleGroup) (*model.Title, error) {
	var cacheTitleDetail *cachemeta.UnitedTitleDetail
	cacheKey := key.MetaDataUnitedTitleDetail(unitedGroup.UnitedID)
	if err := r.cacheReader.HGet(cacheKey, "whole", &cacheTitleDetail); errors.Is(err, cache.ErrCacheMiss) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	titleDetail := &model.Title{}
	if err := copier.Copy(&titleDetail, &cacheTitleDetail); err != nil {
		return nil, err
	}

	return titleDetail, nil
}
