package main

import (
	"log"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/kksearch"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/aws/aws-lambda-go/lambda"
)

func init() {
	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	kkapp.ContextInit()
}

func Handler() {
	log.Println("[INFO] Synchronize all titles to ElasticSearch", kkapp.App.SearchHost)
	err := kksearch.SyncAllTitle()
	if err != nil {
		log.Println("[ERROR]", err)
	}
}

func main() {
	// AWS lambda
	lambda.Start(Handler)
}
