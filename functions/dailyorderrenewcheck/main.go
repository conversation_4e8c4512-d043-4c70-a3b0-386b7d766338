package main

import (
	"bytes"
	"fmt"
	"log"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/slack-go/slack"
)

var (
	slackAPI     = slack.New("*********************************************************")
	slackChannel = "#kktv-log-test-payment-err"

	sqlmap = map[string]string{
		"userlist": `SELECT tt.user_id
						FROM
						(
						SELECT DISTINCT ON (sub.user_id) sub.user_id, sub.start_date, sub.end_date, sub.order_date, sub.status
						FROM orders sub
						WHERE
							(sub.payment_type='credit_card' OR sub.payment_type='iap' OR sub.payment_type='iab')
							AND sub.product_id IN (SELECT id FROM products WHERE auto_renew IS true ORDER BY id)
							AND created_at > DATE(CONCAT(EXTRACT(YEAR FROM NOW()),'-01-01'))
						ORDER BY user_id, order_date DESC
						) AS tt
						JOIN users u ON tt.user_id = u.id
						WHERE tt.status = 'ok' AND u.type='general'
						ORDER BY tt.user_id;`,
	}
)

func init() {
	log.Println("Lambda kktv-api-v3 DailyOrderRenewCheck init")

	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	kkapp.ContextInit()
}

// Handler function of process
func Handler() {

	var userlist, messages []string

	db := kkapp.App.DbUser.Slave()

	err := db.Select(&userlist, sqlmap["userlist"])
	if err != nil {
		log.Println("[ERROR]", err)
	}

	// messages
	messages = append(messages, "疑似未產生續訂單")
	for _, id := range userlist {
		idstr := fmt.Sprintf("https://api.kktv.com.tw/v3/console/#/user/order?q=%s", id)
		messages = append(messages, idstr)
	}

	sendSlackMessage(messages)

}

func sendSlackMessage(msgBlock []string) {
	var buffer bytes.Buffer
	for _, item := range msgBlock {
		buffer.WriteString(fmt.Sprintf("%s\n", item))
	}

	slackAPI.PostMessage(slackChannel, slack.MsgOptionText(buffer.String(), false))
	log.Printf("Message successfully sent to channel %s at %s", slackChannel, time.Now())
}

func main() {
	// AWS lambda
	lambda.Start(Handler)
}
