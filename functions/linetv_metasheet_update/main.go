package main

import (
	"context"
	"fmt"
	"os"

	"github.com/KKTV/kktv-api-v3/functions/linetv_metasheet_update/internal/config"
	"github.com/KKTV/kktv-api-v3/functions/linetv_metasheet_update/internal/episode"
	"github.com/KKTV/kktv-api-v3/functions/linetv_metasheet_update/internal/sheets"
	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/secret"
	"github.com/aws/aws-lambda-go/lambda"
)

func newDBs(dsns []string) *datastore.DBPool {
	dbs := make([]string, len(dsns))
	for i, dbURI := range dsns {
		dbs[i] = fmt.Sprintf("%s?sslmode=disable", dbURI)
	}
	return datastore.NewDBPool(dbs)
}

type Input struct {
	Mode string `json:"mode"`
}

func Handler(ctx context.Context, input Input) error {
	cfg, err := config.New()
	if err != nil {
		log.Error("Failed to load config").Err(err).Send()
		return fmt.Errorf("failed to load config: %w", err)
	}

	secret.Init(cfg.Env)
	if secret.Values.GoogleGCPAPICredential == "" {
		log.Error("Google GCP API credential is not set").Send()
		return fmt.Errorf("google gcp api credential is not set")
	}

	dbMeta := newDBs([]string{cfg.DBMeta})
	dbMetaReader := dbMeta.Slave().Unsafe()
	if err := dbMetaReader.Ping(); err != nil {
		return  fmt.Errorf("failed to ping DB: %w", err)
	}

	sheetsService, err := sheets.NewService(ctx, []byte(secret.Values.GoogleGCPAPICredential))
	if err != nil {
		log.Error("Failed to create sheets service").Err(err).Send()
		return fmt.Errorf("failed to create sheets service: %w", err)
	}

	episodeRepo := episode.NewRepository(dbMetaReader)
	processor := NewProcessor(sheetsService, episodeRepo, cfg)

	var processSeries, processEpisodes bool

	switch input.Mode {
	case "s":
		processSeries = true
	case "e":
		processEpisodes = true
	case "a":
		processSeries = true
		processEpisodes = true
	default:
		return fmt.Errorf("invalid mode: %s, supported modes are 's' (series), 'e' (episodes), 'a' (all)", input.Mode)
	}

	if processSeries {
		if err := processor.ProcessSeries(ctx); err != nil {
			log.Error("Failed to process series").Err(err).Send()
			return fmt.Errorf("failed to process series: %w", err)
		}
	}

	if processEpisodes {
		if err := processor.ProcessEpisodes(ctx); err != nil {
			log.Error("Failed to process episodes").Err(err).Send()
			return fmt.Errorf("failed to process episodes: %w", err)
		}
	}

	log.Info("Process completed successfully").Str("mode", input.Mode).Send()
	return nil
}

func main() {
	if os.Getenv("AWS_EXECUTION_ENV") != "" {
		lambda.Start(Handler)
		return
	}

	// Local test - default to episodes only
	ctx := context.Background()
	if err := Handler(ctx, Input{Mode: "e"}); err != nil {
		log.Fatal("Failed to process").Err(err).Send()
		os.Exit(1)
	}
}

