.PHONY: build run test clean

# Build the CLI tool
build:
	go build -o linetv_metasheet_update main.go

# Run the CLI tool
run: build
	./linetv_metasheet_update

# Run tests
test:
	go test ./...

# Clean build artifacts
clean:
	rm -f linetv_metasheet_update

# Install dependencies
deps:
	go mod tidy

# Check if credentials file exists
check-credentials:
	@if [ ! -f credentials.json ]; then \
		echo "Error: credentials.json not found. Please copy credentials.json.example to credentials.json and fill in your Google Service Account credentials."; \
		exit 1; \
	fi

# Run with credentials check
run-safe: check-credentials run
