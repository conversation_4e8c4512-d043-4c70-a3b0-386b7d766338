package main

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/functions/linetv_metasheet_update/internal/config"
	"github.com/KKTV/kktv-api-v3/functions/linetv_metasheet_update/internal/episode"
	"github.com/KKTV/kktv-api-v3/functions/linetv_metasheet_update/internal/sheets"
	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	"github.com/KKTV/kktv-api-v3/pkg/log"
)

var (
	resolutionOrder = map[string]int{
		"1080p": 0,
		"720p":  1,
		"480p":  2,
		"360p":  3,
		"240p":  4,
	}
)

type Processor struct {
	sheetsService sheets.Service
	episodeRepo   episode.Repository
	config        *config.Config
}

func NewProcessor(sheetsService sheets.Service, episodeRepo episode.Repository, cfg *config.Config) *Processor {
	return &Processor{
		sheetsService: sheetsService,
		episodeRepo:   episodeRepo,
		config:        cfg,
	}
}

func (p *Processor) ProcessSeries(ctx context.Context) error {
	panic("implement me")
}

func (p *Processor) ProcessEpisodes(ctx context.Context) error {
	log.Info("Starting to process episode data").Send()

	// Step 1: Read title series data from upload sheet
	titleSeriesData, err := p.sheetsService.ReadUploadSheet(ctx, p.config.SpreadsheetID, p.config.UploadSheetGID)
	if err != nil {
		return fmt.Errorf("failed to read upload sheet: %w", err)
	}

	if err := p.sheetsService.ClearEpisodeSheet(ctx, p.config.SpreadsheetID, p.config.EpisodeSheetGID); err != nil {
		return fmt.Errorf("failed to clear episode sheet: %w", err)
	}
	log.Info("Cleared episode sheet").Send()

	if len(titleSeriesData) == 0 {
		log.Info("No title series data found, clearing episode sheet").Send()
		return nil
	}
	log.Info("Found title series combinations").Int("count", len(titleSeriesData)).Send()

	// Step 2: Process title series data in batches to avoid Google Sheets API 2MB limit
	const batchSize = 10
	var totalEpisodesWritten int
	currentRow := 2 // Start from row 2 (after header)

	for i := 0; i < len(titleSeriesData); i += batchSize {
		end := i + batchSize
		if end > len(titleSeriesData) {
			end = len(titleSeriesData)
		}

		batch := titleSeriesData[i:end]
		batchNum := (i / batchSize) + 1
		log.Info("Processing batch").Int("batch_number", batchNum).Int("batch_size", len(batch)).Send()

		// Collect episode data for this batch
		var batchEpisodeData [][]interface{}

		for _, titleSeries := range batch {
			titleSeriesID := titleSeries.GetFullSeriesID()
			log.Info("Processing title series ID").Str("title_series_id", titleSeriesID).Int("batch_number", batchNum).Send()

			episodes, err := p.episodeRepo.GetTranscodedEpisodesBySeriesID(titleSeriesID)
			if err != nil {
				log.Warn("Failed to get episodes for series").Str("series_id", titleSeriesID).Err(err).Send()
				continue
			}

			if len(episodes) == 0 {
				log.Warn("No episodes found for series").Str("series_id", titleSeriesID).Send()
				continue
			}

			log.Info("Found episodes for series").Int("count", len(episodes)).Str("series_id", titleSeriesID).Send()

			// Convert episodes to sheet data format
			for _, ep := range episodes {
				rowData := p.toEpisodeSheetRows(ep)
				batchEpisodeData = append(batchEpisodeData, rowData)
			}
		}

		// Skip writing if no episode data in this batch
		if len(batchEpisodeData) == 0 {
			log.Info("No episode data in batch, skipping").Int("batch_number", batchNum).Send()
			continue
		}

		log.Info("Collected episode data for batch").Int("batch_number", batchNum).Int("count", len(batchEpisodeData)).Send()

		// Write batch data starting from current row
		if err := p.sheetsService.WriteEpisodeData(ctx, p.config.SpreadsheetID, p.config.EpisodeSheetGID, batchEpisodeData, currentRow); err != nil {
			return fmt.Errorf("failed to write episode data for batch %d: %w", batchNum, err)
		}
		log.Info("Successfully wrote batch episode records to sheet").Int("batch_number", batchNum).Int("count", len(batchEpisodeData)).Int("start_row", currentRow).Send()

		totalEpisodesWritten += len(batchEpisodeData)
		currentRow += len(batchEpisodeData) // Update current row for next batch
	}

	if totalEpisodesWritten == 0 {
		log.Info("No episode data collected from database, skipping sheet update").Send()
		return nil
	}

	log.Info("Successfully processed all episode data").Int("total_episodes_written", totalEpisodesWritten).Send()

	return nil
}

func timeToFormatStr(t time.Time) string {
	return t.In(datetimer.LocationTaipei).Format("2006-01-02 15:04:05-0700")
}

func (p *Processor) toEpisodeSheetRows(ep episode.Info) []interface{} {
	var pubAtStr, unpubAtStr string

	if ep.Pub.Valid {
		pubAtStr = timeToFormatStr(ep.Pub.Time)
	} else if ep.LicenseStart.Valid {
		pubAtStr = timeToFormatStr(time.Unix(ep.LicenseStart.Int64, 0))
	}

	if ep.Unpub.Valid {
		unpubAtStr = timeToFormatStr(ep.Unpub.Time)
	} else if ep.LicenseEnd.Valid {
		unpubAtStr = timeToFormatStr(time.Unix(ep.LicenseEnd.Int64, 0))
	}

	var resolutions []string
	for res := range ep.SizeMap {
		resolutions = append(resolutions, res)
	}
	sortResolutions(resolutions)

	rowData := []interface{}{
		ep.EpisodeID,
		ep.SeriesID,
		ep.EpisodeName,
		pubAtStr,
		unpubAtStr,
		ep.StillURL.String,
		ep.Duration.String,
		strings.ToUpper(ep.IsAVOD.String),
		strings.Join(resolutions, ","),
	}
	return rowData
}

func sortResolutions(resolutions []string) {
	// sort by custom order: "1080p", "720p", "480p", "360p", "240p"
	sort.Slice(resolutions, func(i, j int) bool {
		orderI, okI := resolutionOrder[resolutions[i]]
		if !okI {
			orderI = len(resolutionOrder) // Place unknown resolutions at the end
		}
		orderJ, okJ := resolutionOrder[resolutions[j]]
		if !okJ {
			orderJ = len(resolutionOrder) // Place unknown resolutions at the end
		}
		return orderI < orderJ
	})
}
