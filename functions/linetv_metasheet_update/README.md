# Fill Title Meta LineTV

這個 CLI 工具會自動化於 [LINETV上架meta中繼表](https://docs.google.com/spreadsheets/d/1ATA5XEpqxPQZqM4amka5ynnnMtUFkin-FsRWb2mFZzg/) 中填入所需要的欄位。

## 功能

### 1. series 資訊填補

TODO philip

### 2. episode 資料插入

針對 [(系統用)episodes](https://docs.google.com/spreadsheets/d/1ATA5XEpqxPQZqM4amka5ynnnMtUFkin-FsRWb2mFZzg/edit?gid=*********#gid=*********)
1. 從「上架作表」中讀取 full series_id
2. 將組合轉換為 full series_id（例如：01000341 + 01 = 0100034101）
3. 清空「episode sheet」的資料（保留 header）
4. 查詢資料庫取得對應的 episode 資料
5. 將 episode 資料寫入「episode sheet」

## 設定

### 環境變數設定

1. 複製 `.env.example` 為 `.env`：
   ```bash
   cp .env.example .env
   ```

2. 編輯 `.env` 檔案，設定必要的環境變數：
   ```bash
   # 必要設定
   ENV=test
   DBMETA=postgres://username:password@host:port/database_name
   # 可選設定（有預設值）
   SPREADSHEET_ID=1ATA5XEpqxPQZqM4amka5ynnnMtUFkin-FsRWb2mFZzg
   UPLOAD_SHEET_GID=**********
   EPISODE_SHEET_GID=*********
   ```

### Google Sheets API 設定

1. 從 GCP 中下載含有 Google sheet API 啟用權限的 `credentials.json`
2. 確保 Service Account 有存取目標 Google Sheets 的權限


## 使用方式

### 方法一：使用 Makefile（推薦）

```bash
cd functions/linetv_metasheet_update

# 安裝依賴
make deps

# 檢查並運行（會自動檢查 credentials.json 是否存在）
make run-safe

```

### 方法二：直接使用 Go

```bash
cd functions/linetv_metasheet_update
# 安裝依賴
go mod tidy
# 編譯並運行
go build -o linetv_metasheet_update main.go
./linetv_metasheet_update
# 或直接運行
go run main.go
```
