package episode

import (
	"database/sql"
	"errors"
	"fmt"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/datatype"
	"gopkg.in/guregu/null.v3"
)

type Repository interface {
	GetTranscodedEpisodesBySeriesID(seriesID string) ([]Info, error)
}

type repository struct {
	db database.DB
}

type Info struct {
	EpisodeID   string
	SeriesID    string
	EpisodeName string
	Pub         time.Time
	Unpub       time.Time
	StillURL    null.String
	Duration    datatype.RoundedFloat
	IsAVOD      bool
	Resolutions []string
}

func NewRepository(dbMeta *datastore.DBPool) Repository {
	return &repository{
		db: dbMeta.Slave().Unsafe(),
	}
}

func (r *repository) GetTranscodedEpisodesBySeriesID(seriesID string) ([]Info, error) {
	query := `SELECT * FROM meta_series WHERE id = $1`
	var seriesRow dbmeta.SeriesRow
	err := r.db.Get(&seriesRow, query, seriesID)
	if errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, fmt.Errorf("failed to query series: %w", err)
	}

	seriesRow.Parse()
	seriesItem, err := seriesRow.ToSeriesCMS()
	if err != nil {
		return nil, fmt.Errorf("failed to process series data: %w", err)
	}

	result := make([]Info, 0)
	for _, ep := range seriesItem.Episodes {
		resolutions := make([]string, 0, 5)
		if hls := ep.Mezzanines.Hls; hls == nil {
			continue
		} else {
			if hls.Sizes.One080p != 0 {
				resolutions = append(resolutions, "1080p")
			}
			if hls.Sizes.Seven20p != 0 {
				resolutions = append(resolutions, "720p")
			}
			if hls.Sizes.Four80p != 0 {
				resolutions = append(resolutions, "480p")
			}
			if hls.Sizes.Three60p != 0 {
				resolutions = append(resolutions, "360p")
			}
			if hls.Sizes.Two40p != 0 {
				resolutions = append(resolutions, "240p")
			}
		}

		info := Info{
			EpisodeID:   ep.ID,
			SeriesID:    seriesID,
			EpisodeName: ep.Title,
			Pub:         time.Unix(ep.Pub, 0),
			Unpub:       time.Unix(ep.UnPub, 0),
			StillURL:    null.StringFrom(ep.Still),
			Duration:    ep.Duration,
			IsAVOD:      ep.IsAvod,
			Resolutions: resolutions,
		}
		result = append(result, info)
	}

	return result, nil
}
