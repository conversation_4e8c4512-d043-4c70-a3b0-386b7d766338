package config

import (
	"github.com/caarlos0/env/v7"
	"github.com/joho/godotenv"
)

type Config struct {
	SpreadsheetID   string `env:"SPREADSHEET_ID" envDefault:"1ATA5XEpqxPQZqM4amka5ynnnMtUFkin-FsRWb2mFZzg"`
	UploadSheetGID  string `env:"UPLOAD_SHEET_GID" envDefault:"2138895539"`
	EpisodeSheetGID string `env:"EPISODE_SHEET_GID" envDefault:"759779796"`
	DBMeta          string `env:"DBMETA,notEmpty"`
	Env             string `env:"ENV,notEmpty"`
}

func New() (*Config, error) {
	_ = godotenv.Load()

	var cfg Config
	if err := env.Parse(&cfg); err != nil {
		return nil, err
	}

	return &cfg, nil
}
