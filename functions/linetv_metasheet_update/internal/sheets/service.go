//go:generate mockgen -source=service.go -destination=service_mock.go -package=sheets
package sheets

import (
	"context"
	"fmt"
	"strconv"

	"google.golang.org/api/option"
	"google.golang.org/api/sheets/v4"
)

type Service interface {
	ReadUploadSheet(ctx context.Context, spreadsheetID, sheetGID string) ([]TitleSeriesData, error)
	ClearEpisodeSheet(ctx context.Context, spreadsheetID, sheetGID string) error
	WriteEpisodeData(ctx context.Context, spreadsheetID, sheetGID string, data [][]interface{}, startRow int) error
}

type service struct {
	sheetsService *sheets.Service
}

type TitleSeriesData struct {
	SheetTitleID  string
	SheetSeriesID string
}

// GetFullSeriesID returns the full series ID like `0100034101`
func (t TitleSeriesData) GetFullSeriesID() string {
	return t.SheetTitleID + t.SheetSeriesID
}

func NewService(ctx context.Context, credentialsJSON []byte) (Service, error) {
	sheetsService, err := sheets.NewService(ctx, option.WithCredentialsJSON(credentialsJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to create sheets service: %w", err)
	}

	return &service{
		sheetsService: sheetsService,
	}, nil
}

func (s *service) ReadUploadSheet(ctx context.Context, spreadsheetID, sheetGID string) ([]TitleSeriesData, error) {
	sheetName, err := s.getSheetNameByGID(ctx, spreadsheetID, sheetGID)
	if err != nil {
		return nil, fmt.Errorf("failed to get sheet name: %w", err)
	}

	// Read only the first two columns (title_id and series_id)
	readRange := fmt.Sprintf("%s!A:B", sheetName)
	resp, err := s.sheetsService.Spreadsheets.Values.Get(spreadsheetID, readRange).Do()
	if err != nil {
		return nil, fmt.Errorf("failed to read sheet data: %w", err)
	}

	if len(resp.Values) == 0 {
		return nil, fmt.Errorf("no data found in upload sheet")
	}

	// Validate header format - expect title_id at column 0 and series_id at column 1
	header := resp.Values[0]
	if len(header) < 2 {
		return nil, fmt.Errorf("header must have at least 2 columns")
	}

	titleIDHeader := fmt.Sprintf("%v", header[0])
	seriesIDHeader := fmt.Sprintf("%v", header[1])

	if titleIDHeader != "title_id" {
		return nil, fmt.Errorf("expected 'title_id' at column 0, got '%s'", titleIDHeader)
	}
	if seriesIDHeader != "series_id" {
		return nil, fmt.Errorf("expected 'series_id' at column 1, got '%s'", seriesIDHeader)
	}

	const titleIDCol = 0
	const seriesIDCol = 1

	var result []TitleSeriesData
	seen := make(map[string]bool)

	// Process data rows (skip header)
	for i := 1; i < len(resp.Values); i++ {
		row := resp.Values[i]

		if len(row) <= seriesIDCol {
			continue
		}

		titleID := fmt.Sprintf("%v", row[titleIDCol])
		seriesID := fmt.Sprintf("%v", row[seriesIDCol])

		if titleID == "" || seriesID == "" {
			continue
		}

		// Create unique key to avoid duplicates
		key := titleID + seriesID
		if seen[key] {
			continue
		}
		seen[key] = true
		result = append(result, TitleSeriesData{
			SheetTitleID:  titleID,
			SheetSeriesID: seriesID,
		})
	}

	return result, nil
}

func (s *service) ClearEpisodeSheet(ctx context.Context, spreadsheetID, sheetGID string) error {
	sheetName, err := s.getSheetNameByGID(ctx, spreadsheetID, sheetGID)
	if err != nil {
		return fmt.Errorf("failed to get sheet name: %w", err)
	}

	// Clear all data except header (row 1)
	clearRange := fmt.Sprintf("%s!A2:Z", sheetName)

	clearReq := &sheets.ClearValuesRequest{}
	_, err = s.sheetsService.Spreadsheets.Values.Clear(spreadsheetID, clearRange, clearReq).Do()
	if err != nil {
		return fmt.Errorf("failed to clear sheet: %w", err)
	}

	return nil
}

func (s *service) WriteEpisodeData(ctx context.Context, spreadsheetID, sheetGID string, data [][]interface{}, startRow int) error {
	if len(data) == 0 {
		return nil
	}

	sheetName, err := s.getSheetNameByGID(ctx, spreadsheetID, sheetGID)
	if err != nil {
		return fmt.Errorf("failed to get sheet name: %w", err)
	}

	// Write data starting from specified row
	writeRange := fmt.Sprintf("%s!A%d:I", sheetName, startRow)

	valueRange := &sheets.ValueRange{
		Values: data,
	}

	_, err = s.sheetsService.Spreadsheets.Values.Update(spreadsheetID, writeRange, valueRange).
		ValueInputOption("RAW").Do()
	if err != nil {
		return fmt.Errorf("failed to write data to sheet: %w", err)
	}

	return nil
}

func (s *service) getSheetNameByGID(ctx context.Context, spreadsheetID, gid string) (string, error) {
	spreadsheet, err := s.sheetsService.Spreadsheets.Get(spreadsheetID).Do()
	if err != nil {
		return "", fmt.Errorf("failed to get spreadsheet: %w", err)
	}

	gidInt, err := strconv.Atoi(gid)
	if err != nil {
		return "", fmt.Errorf("invalid GID: %w", err)
	}

	for _, sheet := range spreadsheet.Sheets {
		if sheet.Properties.SheetId == int64(gidInt) {
			return sheet.Properties.Title, nil
		}
	}

	return "", fmt.Errorf("sheet with GID %s not found", gid)
}
