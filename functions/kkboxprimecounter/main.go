package main

import (
	"log"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/cloudwatch"
)

var (
	sql = map[string]string{
		"counter": `SELECT COUNT(1)::integer AS count FROM users WHERE role = 'premium' AND type = 'prime';`,
	}
	// Create S3 service client
	sess, err = session.NewSession(&aws.Config{
		Region: aws.String("ap-northeast-1")},
	)
	svc = cloudwatch.New(sess)
)

func init() {
	log.Println("Local init")
	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	kkapp.ContextInit()
}

// Handler for lambda
func Handler() {

	//get counter from DB
	var counter float64
	db := kkapp.App.DbUser.Slave()
	err := db.Get(&counter, sql["counter"])
	if err != nil {
		log.Println("[ERROR] Get DB connection failed!", err)
	}

	//get timestamp
	now := time.Now().Truncate(time.Second)

	//put metric data
	var data *cloudwatch.MetricDatum
	data = new(cloudwatch.MetricDatum)
	data.SetMetricName("KKBOX_PRIME_2_KKTV")
	data.SetValue(counter)
	data.SetUnit("Count")
	data.SetTimestamp(now)

	var metricdata []*cloudwatch.MetricDatum
	metricdata = []*cloudwatch.MetricDatum{}
	metricdata = append(metricdata, data)

	var input *cloudwatch.PutMetricDataInput
	input = new(cloudwatch.PutMetricDataInput)
	input.SetMetricData(metricdata)
	input.SetNamespace("KKBOX_PRIME")

	_, err = svc.PutMetricData(input)
	if err != nil {
		log.Println("[ERROR] Put Metric data failed!", err)
	}

}

func main() {
	// AWS lambda
	lambda.Start(Handler)
}
