package main

import (
	"context"
	"errors"
	"fmt"
	"log"
	"regexp"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/bv"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-lambda-go/lambdacontext"
	"github.com/nlopes/slack"
)

var (
	reVTT           = regexp.MustCompile(`t[0-9]{8}_s[0-9]{2}_e[0-9]{4}_me\.(zh|en|ja|ko)\.vtt`)
	slackAPI        = slack.New("*********************************************************")
	slackChannelFmt = "#kktv-log-%s-encode"
)

func init() {
	log.Println("Lambda init")

	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	kkapp.ContextInit()
}

// SlackMessage send message to slack
func SlackMessage(slackChannel, msg string) {
	var color string
	if strings.HasPrefix(msg, "[ERROR]") {
		color = "#A30200"
	} else {
		color = "#2EB886"
	}
	attach := slack.Attachment{
		Color: color,
		Title: "",
		Text:  msg,
	}

	slackAPI.PostMessage(slackChannel, slack.MsgOptionText("", false), slack.MsgOptionAttachments(attach))

	log.Printf("Message sent to channel %s at %s", slackChannel, time.Now())
}

// Handler for subtitle vtt file upload to generate new manifest
func Handler(ctx context.Context, s3Event events.S3Event) (err error) {

	lc, ok := lambdacontext.FromContext(ctx)
	if !ok {
		return errors.New("Cannot find Lambda Function name")
	}
	arn := lc.InvokedFunctionArn
	arnSlice := strings.Split(arn, ":function:")
	if len(arnSlice) != 2 && len(strings.Split(arnSlice[1], ":")) != 2 {
		return errors.New("Cannot find Lambda Function and Alias name")
	}

	for _, record := range s3Event.Records {
		var slackChannel, env, msg string
		s3Obj := record.S3
		bucketName := s3Obj.Bucket.Name
		s3ObjKey := s3Obj.Object.Key
		log.Println(bucketName, s3ObjKey)
		vttFN := reVTT.FindString(s3ObjKey)
		if strings.HasSuffix(s3ObjKey, ".vtt") && vttFN != "" && len(vttFN) > 20 {
			// it's a blendvision encoder job file
			log.Println("[INFO] found subtitle vtt", s3ObjKey)

			TitleID := vttFN[1:9]
			SeriesID := vttFN[11:13]
			epID := vttFN[15:19]
			EpisodeID := fmt.Sprintf("%s%s%s", TitleID, SeriesID, epID)

			bvJob, err := bv.NewBVFromEpisodeID(EpisodeID)

			if err != nil {
				// the blendvision job not ready yet, just ignore it
				log.Println("[INFO] blendvision encoder job not done yet", err)
				continue
			}

			env = bvJob.State.Env

			if bvJob.State.Env == "prod" {
				slackChannel = fmt.Sprintf(slackChannelFmt, "prod")
				env = "prod"
			} else {
				slackChannel = fmt.Sprintf(slackChannelFmt, "test")
				env = "test"
			}

			if err != nil {
				msg = fmt.Sprintf("[ERROR] BlendVision Encoder Bot SyncState Env: %s %s", env, err.Error())
				log.Println(msg)
				SlackMessage(slackChannel, msg)
				continue
			}

			if bvJob.State.Status != bv.StatusSuccess {
				// the encoder not done yet, do nothing
				log.Println("[INFO] blendvision encoder job not done yet", err)
				continue
			}

			_, err = bvJob.Download()
			if err != nil {
				msg = fmt.Sprintf("[ERROR] BlendVision subtitle Download Env: %s %s", env, err.Error())
				log.Println(msg)
				SlackMessage(slackChannel, msg)
				continue
			}

			_, err = bvJob.Generate()
			if err != nil {
				msg = fmt.Sprintf("[ERROR] BlendVision subtitle Generate Env: %s %s", env, err.Error())
				log.Println(msg)
				SlackMessage(slackChannel, msg)
				continue
			}

			_, err = bvJob.Cleanup()
			if err != nil {
				msg = fmt.Sprintf("[ERROR] BlendVision subtitle Cleanup Env: %s %s", env, err.Error())
				log.Println(msg)
				SlackMessage(slackChannel, msg)
				continue
			}

			// success inject manifest to manifest
			msg = fmt.Sprintf("BlendVision subtitle manifest update Env: %s %s", env, s3ObjKey)
			log.Println(msg)
			SlackMessage(slackChannel, msg)
		}
	}

	return err

}

func main() {
	// AWS lambda
	lambda.Start(Handler)
}
