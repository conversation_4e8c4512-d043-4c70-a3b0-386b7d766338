package main

import (
	"log"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/aws/aws-lambda-go/lambda"
)

var (
	sqlclear = map[string]string{
		"token":         `DELETE from tokens WHERE expired_at BETWEEN $1 AND $2;`,
		"refresh_token": `DELETE from refresh_tokens WHERE expired_at BETWEEN $1 AND $2;`,
	}
)

func init() {
	log.Println("Lambda init")

	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	kkapp.ContextInit()
}

// Handler (ctx context.Context, args Input) (err error) {
func Handler() {
	//only clear token and refresh_token 180 days ago.
	daysBeforeNow := 180

	var err error

	start := time.Now().AddDate(0, 0, -daysBeforeNow)
	db := kkapp.App.DbUser.Master()
	clearDayStart := start
	clearDayEnd := start.AddDate(0, 0, 1)

	log.Println(clearDayStart.Format("2006-01-02"), clearDayEnd.Format("2006-01-02"), "token clear")
	_, err = db.Exec(sqlclear["token"], clearDayStart.Format("2006-01-02"), clearDayEnd.Format("2006-01-02"))
	if err != nil {
		log.Println("[ERROR] clear token", err)
	}

	time.Sleep(6 * time.Second)

	log.Println(clearDayStart.Format("2006-01-02"), clearDayEnd.Format("2006-01-02"), "refresh_token clear")
	_, err = db.Exec(sqlclear["refresh_token"], clearDayStart.Format("2006-01-02"), clearDayEnd.Format("2006-01-02"))
	if err != nil {
		log.Println("[ERROR] clear refresh_token", err)
	}

}

func main() {
	// AWS lambda
	lambda.Start(Handler)
}
