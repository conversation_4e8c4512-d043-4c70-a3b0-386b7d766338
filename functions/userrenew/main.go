package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/kkpayment"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/auditing"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper"
	"github.com/KKTV/kktv-api-v3/pkg/broadcast"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/model/events"
	"github.com/KKTV/kktv-api-v3/pkg/secret"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-lambda-go/lambdacontext"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	lambdasvc "github.com/aws/aws-sdk-go/service/lambda"
	"github.com/google/uuid"
)

var (
	sql = map[string]string{
		// kkbox duplicate pay user
		"duplicateKKBOX": `SELECT u.id, u.email, u.phone, u.avatar_url, u.name, u.birthday, u.gender, date_part('epoch', u.expired_at)::int as expired_at,
u.created_at,u.role, u.media_source, u.auto_renew, u.type, created_by, media_source #>> '{kkbox,sub}' as kkboxsub, 'kkbox' as payment_type FROM users u
INNER JOIN orders o ON u.id = o.user_id WHERE o.order_date > Date(NOW()) - interval '1 day'
AND o.status IS NULL
AND o.price > 0::money
AND u.type = 'prime'`,

		// kkbox payment user
		"expireKKBOX": `SELECT id, email, phone, avatar_url, name, birthday, gender, date_part('epoch', expired_at)::int as expired_at,
 created_at,role, media_source, auto_renew, type, created_by, media_source #>> '{kkbox,sub}' as kkboxsub, 'kkbox' as payment_type FROM users
 WHERE type = 'prime' AND expired_at BETWEEN $1 AND $2 ORDER BY expired_at;`,

		// telecom payment user (sonet)
		"expireTELECOM": `SELECT o.user_id as id, 'telecom' as payment_type FROM orders o INNER JOIN
 payment_info p ON o.user_id = p.user_id WHERE o.payment_type = 'telecom' AND o.status is NULL AND
 o.order_date BETWEEN $1 AND $2 AND p.telecom_mp_id <> 'CYC_TSTAR';`,

		// iab (Android) payment user
		"expireIAB": `SELECT o.user_id as id, 'iab' as payment_type FROM orders o INNER JOIN
 payment_info p ON o.user_id = p.user_id WHERE o.payment_type = 'iab' AND o.status is NULL AND
 o.order_date BETWEEN $1 AND $2;`,

		// iap (Apple) payment user
		"expireIAP": `SELECT o.user_id as id, 'iap' as payment_type FROM orders o INNER JOIN
 payment_info p ON o.user_id = p.user_id WHERE o.payment_type = 'iap' AND o.status is NULL AND
 o.order_date BETWEEN $1 AND $2;`,

		// mod payment user
		"expireMOD": `SELECT user_id as id, 'mod' as payment_type FROM orders
WHERE payment_type = 'mod' AND status IS NULL AND DATE(order_date) BETWEEN $1 AND $2;`,

		// credit card user
		"expireCreditCard": `SELECT o.user_id as id, 'credit_card' as payment_type FROM orders o INNER JOIN
 payment_info p ON o.user_id = p.user_id WHERE o.payment_type = 'credit_card' AND o.status is NULL AND
 o.order_date BETWEEN $1 AND $2;`,
	}
)

func init() {
	log.Println("Lambda init")
	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	plog.Init(config.Env)
	secret.Init(config.Env)
	kkapp.ContextInit()
}

func newBroadcaster() broadcast.EventManager {
	userCachePool := kkapp.App.RedisUser
	userCacheReader := cache.New(userCachePool.Slave())
	userCacheWriter := cache.New(userCachePool.Master())
	userRepo := wrapper.NewUserService(kkapp.App.DbUser.Slave().Unsafe())
	auditRepo := auditing.NewRepository(kkapp.App.DbUser.Master())
	userUpdatedLogger := auditing.NewUserUpdatedLogger(auditRepo, userRepo, userCacheReader, userCacheWriter)
	userWillBeUpdatedListener := auditing.NewUserWillBeUpdatedListener(userRepo, userCacheWriter)

	broadcaster := broadcast.NewEventManager()
	broadcaster.Register(events.SignalUserUpdated, userUpdatedLogger)
	broadcaster.Register(events.SignalUserWillBeUpdated, userWillBeUpdatedListener)
	return broadcaster
}

// Input for lambda
type Input struct {
	Args []model.UserInfo
}

// Handler to process
func Handler(ctx context.Context, args Input) (err error) {

	lc, ok := lambdacontext.FromContext(ctx)
	if !ok {
		return errors.New("Cannot find Lambda Function name")
	}

	arn := lc.InvokedFunctionArn
	arnSlice := strings.Split(arn, ":function:")
	if len(arnSlice) != 2 && len(strings.Split(arnSlice[1], ":")) != 2 {
		return errors.New("Cannot find Lambda Function and Alias name")
	}

	fnSlice := strings.Split(arnSlice[1], ":")
	funcName := fnSlice[0]
	funcAlias := fnSlice[1]

	db := kkapp.App.DbUser.Slave()

	log.Println(funcName, funcAlias)
	////////////////////////////////////////////////////////////////////////////////
	// Dispatch mode
	if args.Args == nil {
		log.Println("[INFO] Running in Dispatch Mode, start to dispatch jobs")

		const batchSize = 30
		todayStr := time.Now().UTC().Format("2006-01-02")
		tomorrowStr := time.Now().Add(24 * time.Hour).UTC().Format("2006-01-02")

		// kkbox
		{
			// scope variable
			users := []model.UserInfo{}
			batches := [][]model.UserInfo{}
			svc := lambdasvc.New(session.New())

			log.Println("[INFO] renew kkbox user will expire between", todayStr, tomorrowStr)
			sqlquery := fmt.Sprintf("%s UNION %s", sql["duplicateKKBOX"], sql["expireKKBOX"])
			err = db.Select(&users, sqlquery, todayStr, tomorrowStr)
			if err != nil {
				log.Println("[ERROR]", err)
				return
			}

			for batchSize < len(users) {
				users, batches = users[batchSize:], append(batches, users[0:batchSize:batchSize])
			}
			batches = append(batches, users)

			// start to dispatch
			for _, uids := range batches {
				if len(uids) > 0 {
					newArgs := Input{Args: uids}
					payLoad, _ := json.Marshal(newArgs)

					_, err := svc.Invoke(
						&lambdasvc.InvokeInput{
							FunctionName:   aws.String(funcName),
							InvocationType: aws.String("Event"),
							Payload:        payLoad,
							Qualifier:      aws.String(funcAlias),
						})
					log.Println("[INFO] dispath users", uids, err)
				}
			}
		}

		// telecom
		time.Sleep(3 * time.Second)
		{
			// scope variable
			users := []model.UserInfo{}
			batches := [][]model.UserInfo{}
			svc := lambdasvc.New(session.New())

			// we add payment_type via sql select
			log.Println("[INFO] renew telecom user will expire between", todayStr, tomorrowStr)
			err = db.Select(&users, sql["expireTELECOM"], todayStr, tomorrowStr)
			if err != nil {
				log.Println("[ERROR]", err)
				return
			}

			for batchSize < len(users) {
				users, batches = users[batchSize:], append(batches, users[0:batchSize:batchSize])
			}
			batches = append(batches, users)

			// start to dispatch
			for _, uids := range batches {
				if len(uids) > 0 {
					newArgs := Input{Args: uids}
					payLoad, _ := json.Marshal(newArgs)

					_, err := svc.Invoke(
						&lambdasvc.InvokeInput{
							FunctionName:   aws.String(funcName),
							InvocationType: aws.String("Event"),
							Payload:        payLoad,
							Qualifier:      aws.String(funcAlias),
						})
					log.Println("[INFO] dispath users", uids, err)
				}
			}
		}

		// iab
		time.Sleep(3 * time.Second)
		{
			// scope variable
			users := []model.UserInfo{}
			batches := [][]model.UserInfo{}
			svc := lambdasvc.New(session.New())

			plog.Info("userRenew: IAB").Str("expired from", todayStr).Str("expired to", tomorrowStr).Send()
			err = db.Select(&users, sql["expireIAB"], todayStr, tomorrowStr)
			if err != nil {
				plog.Error("userRenew: IAB: retrieve expired users failed").Err(err).Send()
				return
			}

			for batchSize < len(users) {
				users, batches = users[batchSize:], append(batches, users[0:batchSize:batchSize])
			}
			batches = append(batches, users)

			// start to dispatch
			for _, uids := range batches {
				if len(uids) > 0 {
					newArgs := Input{Args: uids}
					payLoad, _ := json.Marshal(newArgs)

					_, err := svc.Invoke(
						&lambdasvc.InvokeInput{
							FunctionName:   aws.String(funcName),
							InvocationType: aws.String("Event"),
							Payload:        payLoad,
							Qualifier:      aws.String(funcAlias),
						})
					plog.Info("userRenew: IAB: invoke lambda").Interface("users", uids).Err(err).Send()
				}
			}
		}

		// iap
		time.Sleep(3 * time.Second)
		{
			// scope variable
			users := []model.UserInfo{}
			batches := [][]model.UserInfo{}
			svc := lambdasvc.New(session.New())

			plog.Info("userRenew: IAP").Str("expired from", todayStr).Str("expired to", tomorrowStr).Send()
			err = db.Select(&users, sql["expireIAP"], todayStr, tomorrowStr)
			if err != nil {
				plog.Error("userRenew: IAP: retrieve expired users failed").Err(err).Send()
				return
			}

			for batchSize < len(users) {
				users, batches = users[batchSize:], append(batches, users[0:batchSize:batchSize])
			}
			batches = append(batches, users)

			// start to dispatch
			for _, uids := range batches {
				if len(uids) > 0 {
					newArgs := Input{Args: uids}
					payLoad, _ := json.Marshal(newArgs)

					_, err := svc.Invoke(
						&lambdasvc.InvokeInput{
							FunctionName:   aws.String(funcName),
							InvocationType: aws.String("Event"),
							Payload:        payLoad,
							Qualifier:      aws.String(funcAlias),
						})
					plog.Info("userRenew: IAP: invoke lambda").Interface("users", uids).Err(err).Send()
				}
			}
		}

		// MOD
		time.Sleep(3 * time.Second)
		{
			// scope variable
			users := []model.UserInfo{}
			batches := [][]model.UserInfo{}
			svc := lambdasvc.New(session.New())

			plog.Info("userRenew: MOD").Str("expired from", todayStr).Str("expired to", tomorrowStr).Send()
			err = db.Select(&users, sql["expireMOD"], todayStr, tomorrowStr)
			if err != nil {
				plog.Error("userRenew: MOD: retrieve expired users failed").Err(err).Send()
				return
			}

			for batchSize < len(users) {
				users, batches = users[batchSize:], append(batches, users[0:batchSize:batchSize])
			}
			batches = append(batches, users)

			// start to dispatch
			for _, uids := range batches {
				plog.Info("userRenew: MOD: dispatch user start").Interface("users", uids).Send()

				if len(uids) > 0 {
					newArgs := Input{Args: uids}
					payLoad, _ := json.Marshal(newArgs)

					_, err := svc.Invoke(
						&lambdasvc.InvokeInput{
							FunctionName:   aws.String(funcName),
							InvocationType: aws.String("Event"),
							Payload:        payLoad,
							Qualifier:      aws.String(funcAlias),
						})
					plog.Info("userRenew: MOD: invoke lambda").Interface("users", uids).Err(err).Send()
				}
			}
		}

		// credit_card
		time.Sleep(3 * time.Second)
		{
			// scope variable
			users := []model.UserInfo{}
			batches := [][]model.UserInfo{}
			svc := lambdasvc.New(session.New())

			log.Println("[INFO] renew credit_card user will expire between", todayStr, tomorrowStr)
			err = db.Select(&users, sql["expireCreditCard"], todayStr, tomorrowStr)
			if err != nil {
				log.Println("[ERROR]", err)
				return
			}

			for batchSize < len(users) {
				users, batches = users[batchSize:], append(batches, users[0:batchSize:batchSize])
			}
			batches = append(batches, users)

			// start to dispatch
			for _, uids := range batches {
				if len(uids) > 0 {
					newArgs := Input{Args: uids}
					payLoad, _ := json.Marshal(newArgs)

					_, err := svc.Invoke(
						&lambdasvc.InvokeInput{
							FunctionName:   aws.String(funcName),
							InvocationType: aws.String("Event"),
							Payload:        payLoad,
							Qualifier:      aws.String(funcAlias),
						})
					log.Println("[INFO] dispatch users", uids, err)
				}
			}
		}
	}

	////////////////////////////////////////////////////////////////////////////////
	// Worker mode
	if args.Args != nil && len(args.Args) > 0 {
		broadcaster := newBroadcaster()
		rid := getRequestID(lc)

		plog.Info("userrenew: running in worker mode").Interface("users", args.Args).Str("request_id", rid).Send()
		for _, user := range args.Args {
			broadcaster.SyncEmit(events.SignalUserWillBeUpdated, &events.UserWillBeUpdatedEvent{
				RequestID: rid, UserID: user.Id,
			})

			switch user.PaymentType {
			case "kkbox":
				plog.Warn("userRenew: kkbox prime user should not be here, because she is expired").Str("user_id", user.Id).Send()
			case "telecom":
				kkpayment.TelecomRenew(user)
			case "iab":
				kkpayment.IABRenew(user)
			case "iap":
				kkpayment.IAPRenew(user)
			case "mod":
				todayStr := time.Now().UTC().Format("2006-01-02")
				kkpayment.RenewMOD(user, todayStr)
			case "credit_card":
				kkpayment.CreditCardRenew(user)
			}

			broadcaster.Emit(events.SignalUserUpdated, &events.UserUpdatedEvent{
				RequestID: rid, UserID: user.Id,
				UpdateReason: "user renew", ModifiedBy: dbuser.AuditModifierTypeSystem.String(),
			})
		}
	}
	return
}

func getRequestID(lc *lambdacontext.LambdaContext) string {
	rid := lc.AwsRequestID
	if rid == "" {
		rid = uuid.New().String()
	}
	return rid
}

func main() {
	// AWS lambda
	lambda.Start(Handler)
}
