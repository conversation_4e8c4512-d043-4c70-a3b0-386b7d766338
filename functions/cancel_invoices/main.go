package main

import (
	"log"
	"os"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/kkpayment"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/secret"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/lib/pq"
	"gopkg.in/guregu/null.v3"
)

type CancelInvoicesEvent struct {
	OrderNumbers []string `json:"order_numbers"`
}

type OrderInvoiceNumber struct {
	OrderNumber       string    `db:"order_number" json:"order_number"`
	InvoiceNumber     string    `db:"invoice_number" json:"invoice_number"`
	InvoiceCanceledAt null.Time `db:"invoice_canceled_at" json:"invoice_canceled_at"`
}

func init() {
	log.Println("Lambda thumbnail init")
	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	secret.Init(config.Env)
	kkapp.ContextInit()
}

// ** Sample Event:
// {
// 	"order_numbers": [
// 		"KT0020221108030437",
// 		"KT0020221110171809"
// 	]
// }
func main() {
	// lambda runtime environment
	if os.Getenv("LAMBDA_RUNTIME_DIR") != "" {
		lambda.Start(Handler)
	} else {
		// local environment
		event := CancelInvoicesEvent{
			OrderNumbers: []string{
				"KT0020160830122847",
			},
		}
		Handler(event)
	}
}

func Handler(event CancelInvoicesEvent) {

	orderNumbers := event.OrderNumbers

	plog.Info("cancel invoices event").
		Strs("order_numbers", orderNumbers).
		Send()

	db := kkapp.App.DbUser.Slave()

	var orderInvoiceNumbers []OrderInvoiceNumber
	// 目前先限制僅有訂單狀態為 refund 時才可作廢發票
	sql := `SELECT
			o.id AS order_number,
			i.invoice_number,
			i.canceled_at AS invoice_canceled_at
		FROM orders o
		LEFT JOIN invoices i ON i.order_id = o.id
		WHERE o.status = 'refund'
		AND o.id = ANY($1);`
	err := db.Select(&orderInvoiceNumbers, sql, pq.Array(orderNumbers))
	if err != nil {
		plog.Error("select invoices failed").Err(err).Send()
		return
	}

	queriedOrderNumbers := []string{}
	for _, oi := range orderInvoiceNumbers {
		queriedOrderNumbers = append(queriedOrderNumbers, oi.OrderNumber)
		plog.Info("cancel invoice").
			Str("order_number", oi.OrderNumber).
			Str("invoice_number", oi.InvoiceNumber).
			Time("invoice_canceled_at", oi.InvoiceCanceledAt.Time).
			Send()

		// 如果發票尚未作廢，則進行作廢
		if !oi.InvoiceCanceledAt.Valid {
			err := kkpayment.CancelInvoice(oi.InvoiceNumber)
			if err != nil {
				plog.Error("cancel invoice failed").Err(err).Send()
			}
		} else {
			plog.Info("invoice already canceled").Send()
		}
	}

	// 如果 input 的 order number 不在查詢出的訂單列表內
	// 表示 查無改訂單編號 或 該訂單狀態不為 refund
	diff := slice.Difference(orderNumbers, queriedOrderNumbers)
	plog.Info("order not found or status is not refund").
		Strs("order_numbers", diff).
		Send()
}
