package main

import (
	"log"
	"os"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	meta "github.com/KKTV/kktv-api-v3/pkg/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/pkg/secret"
	"github.com/aws/aws-lambda-go/lambda"
)

func init() {
	log.Println("Lambda sitemap_videos init")
	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	secret.Init(config.Env)
	kkapp.ContextInit()
}

func main() {
	// lambda runtime environment
	if os.Getenv("LAMBDA_RUNTIME_DIR") != "" {
		lambda.Start(Handler)
	} else {
		// local environment
		Handler()
	}
}

func getViewableEpisodes() ([]*meta.Episode, error) {
	log.Println("get viewable episodes start")
	var records []*meta.Episode
	db := kkapp.App.DbMeta.Slave()

	if err := db.Select(&records,
		`SELECT * FROM meta_episode
			WHERE (meta->>'play_zone')::boolean = true
				AND to_timestamp((meta->>'license_end')::numeric) > now();
		`); err != nil {
		return nil, err
	}

	return records, nil
}

func getTitleIdsFromEpisodes(episodes []*meta.Episode) []string {
	var (
		titleIds      []string
		titleIdExists = make(map[string]bool)
	)
	log.Println("get titleids from episode start")
	for _, episode := range episodes {
		if !titleIdExists[episode.Meta["title_id"].(string)] {
			titleIdExists[episode.Meta["title_id"].(string)] = true
			titleIds = append(titleIds, episode.Meta["title_id"].(string))
		}
	}
	return titleIds
}

func setTitleIdsToRedis(titleIds []string) error {
	log.Println("set title ids start")
	var (
		command = "SADD"
	)
	pool := kkapp.App.RedisMeta.Master()
	if conn, err := pool.Get(); err != nil {
		plog.Error("can not get redis connection").Err(err).Send()
		return err
	} else {
		for _, titleId := range titleIds {
			if err := conn.Cmd(command, key.GetSEOSitemapVideos(), titleId).Err; err != nil {
				plog.Error("set cache failed").Str("titleId", titleId).Err(err).Send()
			}
		}

		defer pool.Empty()
	}
	return nil
}

func clearCache() error {
	log.Println("clear cache")
	pool := kkapp.App.RedisMeta.Master()
	if conn, err := pool.Get(); err != nil {
		plog.Error("can not get redis connection").Err(err).Send()
		return err
	} else {
		if err := conn.Cmd("DEL", key.GetSEOSitemapVideos()).Err; err != nil {
			plog.Error("DEL cache failed").Err(err).Send()
			return err
		}
	}

	return nil
}

func Handler() {
	if episodes, err := getViewableEpisodes(); err != nil {
		plog.Warn("get episode error").Err(err).Send()
	} else if len(episodes) == 0 {
		plog.Warn("get zero episode").Err(err).Send()
	} else {
		titleIds := getTitleIdsFromEpisodes(episodes)
		clearCache()
		setTitleIdsToRedis(titleIds)
	}
}
