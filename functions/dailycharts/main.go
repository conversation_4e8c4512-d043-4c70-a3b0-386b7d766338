package main

import (
	"errors"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/endpoints"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/athena"
	"github.com/slack-go/slack"
)

var (
	awscfg = &aws.Config{
		Region: aws.String(endpoints.ApNortheast1RegionID),
	}

	athenaDatabase   = "kktv_prod_amplitude_logs"
	s3OutputLocation = "s3://kktv-query-kkbox-rdc-amplitude-result/Unsaved"

	// Create the session that the service will use.
	sess = session.Must(session.NewSession(awscfg))
	svc  = athena.New(sess, awscfg)

	slackAPI = slack.New("*********************************************************")

	slackChannel = ""
	prodChannel  = "#kktv-charts"
	testChannel  = "#kktv-test-charts"

	rankingCount = "10"

	start, end                  time.Time
	strStart, strEnd, startDate string

	sqlsf = map[string]string{
		"newSignupPlatform": `SELECT PLATFORM, COUNT(DISTINCT user_id)
			FROM kktv_prod_raw_log
			WHERE dt BETWEEN '%s' AND '%s'
				AND EVENT_TYPE='Account Signed Up'
			GROUP BY 1`,

		"svod_daa_platform": `SELECT PLATFORM, COUNT(DISTINCT AMPLITUDE_ID)
			FROM kktv_prod_raw_log
			WHERE dt BETWEEN '%s' AND '%s'
				AND EVENT_TYPE='Video Playing Stopped'
				AND CAST(json_extract_scalar(event_properties, '$["video played percentage"]') AS REAL) > 0.1
				AND json_extract_scalar(event_properties, '$["service zone"]')='svod'
			GROUP BY 1`,

		"svod_daa_memberships": `SELECT json_extract_scalar(user_properties, '$["account current membership"]') AS membership, COUNT(DISTINCT AMPLITUDE_ID)
			FROM kktv_prod_raw_log
			WHERE dt BETWEEN '%s' AND '%s'
				AND EVENT_TYPE='Video Playing Stopped'
				AND CAST(json_extract_scalar(event_properties, '$["video played percentage"]') AS REAL) > 0.1
				AND json_extract_scalar(event_properties, '$["service zone"]')='svod'
			GROUP BY 1`,

		"svod_ranking_all_users": `SELECT json_extract_scalar(event_properties, '$["title name in zh"]') AS title_name, COUNT(DISTINCT AMPLITUDE_ID)
			FROM kktv_prod_raw_log
			WHERE dt BETWEEN '%s' AND '%s'
				AND EVENT_TYPE='Video Playing Stopped'
				AND CAST(json_extract_scalar(event_properties, '$["video played percentage"]') AS REAL) > 0.1
				AND json_extract_scalar(event_properties, '$["service zone"]')='svod'
				AND json_extract_scalar(event_properties, '$["title name in zh"]') IS NOT NULL
			GROUP BY 1
			ORDER BY 2 DESC
			LIMIT %s`,

		"svod_ranking_freetrial_users": `SELECT json_extract_scalar(event_properties, '$["title name in zh"]') AS title_name, COUNT(DISTINCT AMPLITUDE_ID)
			FROM kktv_prod_raw_log
			WHERE dt BETWEEN '%s' AND '%s'
				AND EVENT_TYPE='Video Playing Stopped'
				AND CAST(json_extract_scalar(event_properties, '$["video played percentage"]') AS REAL) > 0.1
				AND json_extract_scalar(event_properties, '$["service zone"]')='svod'
				AND json_extract_scalar(user_properties, '$["account current membership"]')='free trial'
				AND json_extract_scalar(event_properties, '$["title name in zh"]') IS NOT NULL
			GROUP BY 1
			ORDER BY 2 DESC
			LIMIT %s`,

		"svod_ranking_premium_users": `SELECT json_extract_scalar(event_properties, '$["title name in zh"]') AS title_name, COUNT(DISTINCT AMPLITUDE_ID)
			FROM kktv_prod_raw_log
			WHERE dt BETWEEN '%s' AND '%s'
				AND EVENT_TYPE='Video Playing Stopped'
				AND CAST(json_extract_scalar(event_properties, '$["video played percentage"]') AS REAL) > 0.1
				AND json_extract_scalar(event_properties, '$["service zone"]')='svod'
				AND json_extract_scalar(user_properties, '$["account current membership"]')='premium'
				AND json_extract_scalar(event_properties, '$["title name in zh"]') IS NOT NULL
			GROUP BY 1
			ORDER BY 2 DESC
			LIMIT %s`,

		"svod_ranking_guest_users": `SELECT json_extract_scalar(event_properties, '$["title name in zh"]') AS title_name, COUNT(DISTINCT AMPLITUDE_ID)
			FROM kktv_prod_raw_log
			WHERE dt BETWEEN '%s' AND '%s'
				AND EVENT_TYPE='Video Playing Stopped'
				AND CAST(json_extract_scalar(event_properties, '$["video played percentage"]') AS REAL) > 0.1
				AND json_extract_scalar(event_properties, '$["service zone"]')='svod'
				AND json_extract_scalar(user_properties, '$["account current membership"]') = 'guest'
				AND json_extract_scalar(event_properties, '$["title name in zh"]') IS NOT NULL
			GROUP BY 1
			ORDER BY 2 DESC
			LIMIT %s`,

		"svod_ranking_expired_users": `SELECT json_extract_scalar(event_properties, '$["title name in zh"]') AS title_name, COUNT(DISTINCT AMPLITUDE_ID)
			FROM kktv_prod_raw_log
			WHERE dt BETWEEN '%s' AND '%s'
				AND EVENT_TYPE='Video Playing Stopped'
				AND CAST(json_extract_scalar(event_properties, '$["video played percentage"]') AS REAL) > 0.1
				AND json_extract_scalar(event_properties, '$["service zone"]')='svod'
				AND json_extract_scalar(user_properties, '$["account current membership"]') = 'expired'
				AND json_extract_scalar(event_properties, '$["title name in zh"]') IS NOT NULL
			GROUP BY 1
			ORDER BY 2 DESC
			LIMIT %s`,

		"svod_ranking_kkboxprime_users": `SELECT json_extract_scalar(event_properties, '$["title name in zh"]') AS title_name, COUNT(DISTINCT AMPLITUDE_ID)
			FROM kktv_prod_raw_log
			WHERE dt BETWEEN '%s' AND '%s'
				AND EVENT_TYPE='Video Playing Stopped'
				AND CAST(json_extract_scalar(event_properties, '$["video played percentage"]') AS REAL) > 0.1
				AND json_extract_scalar(event_properties, '$["service zone"]')='svod'
				AND json_extract_scalar(user_properties, '$["account current membership"]') in ('kkbox prime','premium x kkbox prime')
				AND json_extract_scalar(event_properties, '$["title name in zh"]') IS NOT NULL
			GROUP BY 1
			ORDER BY 2 DESC
			LIMIT %s`,

		"svod_ranking_country_taiwan": `SELECT json_extract_scalar(event_properties, '$["title name in zh"]') AS title_name, COUNT(DISTINCT AMPLITUDE_ID)
			FROM kktv_prod_raw_log
			WHERE dt BETWEEN '%s' AND '%s'
				AND EVENT_TYPE='Video Playing Stopped'
				AND json_extract_scalar(event_properties, '$["title from country"]')='Taiwan'
				AND json_extract_scalar(event_properties, '$["service zone"]')='svod'
				AND CAST(json_extract_scalar(event_properties, '$["video played percentage"]') AS REAL) > 0.1
				AND json_extract_scalar(event_properties, '$["title name in zh"]') IS NOT NULL
			GROUP BY 1
			ORDER BY 2 DESC
			LIMIT %s`,

		"svod_ranking_country_korea": `SELECT json_extract_scalar(event_properties, '$["title name in zh"]') AS title_name, COUNT(DISTINCT AMPLITUDE_ID)
			FROM kktv_prod_raw_log
			WHERE dt BETWEEN '%s' AND '%s'
				AND EVENT_TYPE='Video Playing Stopped'
				AND json_extract_scalar(event_properties, '$["title from country"]')='Korea'
				AND json_extract_scalar(event_properties, '$["service zone"]')='svod'
				AND CAST(json_extract_scalar(event_properties, '$["video played percentage"]') AS REAL) > 0.1
				AND json_extract_scalar(event_properties, '$["title name in zh"]') IS NOT NULL
			GROUP BY 1
			ORDER BY 2 DESC
			LIMIT %s`,

		"svod_ranking_country_japan": `SELECT json_extract_scalar(event_properties, '$["title name in zh"]') AS title_name, COUNT(DISTINCT AMPLITUDE_ID)
			FROM kktv_prod_raw_log
			WHERE dt BETWEEN '%s' AND '%s'
				AND EVENT_TYPE='Video Playing Stopped'
				AND json_extract_scalar(event_properties, '$["title from country"]')='Japan'
				AND json_extract_scalar(event_properties, '$["service zone"]')='svod'
				AND CAST(json_extract_scalar(event_properties, '$["video played percentage"]') AS REAL) > 0.1
				AND json_extract_scalar(event_properties, '$["title name in zh"]') IS NOT NULL
			GROUP BY 1
			ORDER BY 2 DESC
			LIMIT %s`,

		"svod_ranking_country_china": `SELECT json_extract_scalar(event_properties, '$["title name in zh"]') AS title_name, COUNT(DISTINCT AMPLITUDE_ID)
			FROM kktv_prod_raw_log
			WHERE dt BETWEEN '%s' AND '%s'
				AND EVENT_TYPE='Video Playing Stopped'
				AND json_extract_scalar(event_properties, '$["title from country"]')='China'
				AND json_extract_scalar(event_properties, '$["service zone"]')='svod'
				AND CAST(json_extract_scalar(event_properties, '$["video played percentage"]') AS REAL) > 0.1
				AND json_extract_scalar(event_properties, '$["title name in zh"]') IS NOT NULL
			GROUP BY 1
			ORDER BY 2 DESC
			LIMIT %s`,
	}
)

func sendSlackMessage(msg string, attach slack.Attachment) {

	channelID, timestamp, err := slackAPI.PostMessage(slackChannel, slack.MsgOptionText(msg, false), slack.MsgOptionAttachments(attach))
	if err != nil {
		log.Printf("%s\n", err)
		return
	}
	log.Printf("Message successfully sent to channel %s at %s", channelID, timestamp)
}

func queryAthena(query string) (rc [][]interface{}, err error) {
	var q athena.QueryExecutionContext
	//athenaDatabase declare in Global var
	q.SetDatabase(athenaDatabase)

	var r athena.ResultConfiguration
	//s3OutputLocation declare in Global var
	r.SetOutputLocation(s3OutputLocation)

	var s athena.StartQueryExecutionInput
	s.SetQueryString(query)
	s.SetQueryExecutionContext(&q)
	s.SetResultConfiguration(&r)

	result, err := svc.StartQueryExecution(&s)
	if err != nil {
		log.Println(err)
		return rc, err
	}
	log.Println("StartQueryExecution result:")
	log.Println(result.GoString())

	var qri athena.GetQueryExecutionInput
	qri.SetQueryExecutionId(*result.QueryExecutionId)

	var qrop *athena.GetQueryExecutionOutput
	duration := time.Duration(2) * time.Second // Pause for 2 seconds

	for {
		qrop, err = svc.GetQueryExecution(&qri)
		if err != nil {
			log.Println(err)
			return rc, err
		}
		if *qrop.QueryExecution.Status.State != "RUNNING" && *qrop.QueryExecution.Status.State != "QUEUED" {
			break
		}
		log.Println("waiting.")
		time.Sleep(duration)

	}

	if *qrop.QueryExecution.Status.State == "SUCCEEDED" {
		var ip athena.GetQueryResultsInput
		ip.SetQueryExecutionId(*result.QueryExecutionId)
		op, err := svc.GetQueryResults(&ip)
		if err != nil {
			log.Println(err)
			return rc, err
		}
		for i := range op.ResultSet.Rows {
			if i == 0 {
				continue
			}
			var temp []interface{}
			for _, v := range op.ResultSet.Rows[i].Data {
				if *v != (athena.Datum{}) {
					temp = append(temp, *v.VarCharValue)
				}
			}
			rc = append(rc, temp)
		}
	} else {
		return rc, errors.New(*qrop.QueryExecution.Status.State)
	}
	return rc, err
}

func toInt(n string) int {
	v, err := strconv.Atoi(n)
	if err != nil {
		return 0
	}
	return v
}

func getByPlatform(query string) (t string, r string) {
	querystr := fmt.Sprintf(query, strStart, strEnd)
	result, err := queryAthena(querystr)
	if err != nil {
		log.Printf("failed to run a query. %v, err: %v", querystr, err)
	}
	total := 0
	for _, v := range result {
		total = total + toInt(v[1].(string))
		r += " \t" + v[0].(string) + ": `" + v[1].(string) + "`\n"
	}
	t = strconv.Itoa(total)
	return t, r
}

func getByRanking(query string) (r string) {
	querystr := fmt.Sprintf(query, strStart, strEnd, rankingCount)
	result, err := queryAthena(querystr)
	if err != nil {
		log.Printf("failed to run a query. %v, err: %v", query, err)
	}
	i := 1
	for _, v := range result {
		r += " " + strconv.Itoa(i) + ". " + v[0].(string) + ": `" + v[1].(string) + "`\n"
		i++
	}
	return r
}

func GenDailyChart() {

	if kkapp.App.Debug {
		slackChannel = testChannel
	} else {
		slackChannel = prodChannel
	}

	addPartition := fmt.Sprintf("ALTER TABLE kktv_prod_raw_log ADD PARTITION (dt='%s')", startDate)
	log.Println("Add Partition:", addPartition)
	queryAthena(addPartition)

	// signups
	total, new_signup_platform := getByPlatform(sqlsf["newSignupPlatform"])
	summary_signups := slack.Attachment{
		Color: "#36a64f",
		Title: "New Sign-Ups on " + startDate,
		Text:  "Total: `" + total + "`\n Platforms:\n" + new_signup_platform + "\n",
	}

	// SVOD DAA
	svod_daa_total, svod_daa_platform := getByPlatform(sqlsf["svod_daa_platform"])
	_, svod_daa_memberships := getByPlatform(sqlsf["svod_daa_memberships"])
	summary_svod_daa := slack.Attachment{
		Color: "#36a64f",
		Title: "SVOD DAA on " + startDate,
		Text:  "Total: `" + svod_daa_total + "`\n Platforms:\n" + svod_daa_platform + "\n Memberships:\n" + svod_daa_memberships,
	}

	// SVOD Ranking
	// Title valid ranking in all users
	svod_ranking_all_users := getByRanking(sqlsf["svod_ranking_all_users"])
	summary_svod_ranking_all_users := slack.Attachment{
		Color: "#36a64f",
		Title: "SVOD Title valid ranking in all users on " + startDate,
		Text:  " \t" + svod_ranking_all_users,
	}

	// Title valid ranking in free Trial users
	svod_ranking_freeTrial_users := getByRanking(sqlsf["svod_ranking_freetrial_users"])
	summary_svod_ranking_freeTrial_users := slack.Attachment{
		Color: "#36a64f",
		Title: "SVOD Title valid ranking in freeTrial users on " + startDate,
		Text:  " \t" + svod_ranking_freeTrial_users,
	}

	// Title valid ranking in premium users
	svod_ranking_premium_users := getByRanking(sqlsf["svod_ranking_premium_users"])
	summary_svod_ranking_premium_users := slack.Attachment{
		Color: "#36a64f",
		Title: "SVOD Title valid ranking in premium users on " + startDate,
		Text:  " \t" + svod_ranking_premium_users,
	}

	// Title valid ranking in kkboxprime users
	svod_ranking_kkboxprime_users := getByRanking(sqlsf["svod_ranking_kkboxprime_users"])
	summary_svod_ranking_kkboxprime_users := slack.Attachment{
		Color: "#36a64f",
		Title: "SVOD Title valid ranking in kkboxprime users on " + startDate,
		Text:  " \t" + svod_ranking_kkboxprime_users,
	}

	// Title valid ranking in Taiwan country
	svod_ranking_country_taiwan := getByRanking(sqlsf["svod_ranking_country_taiwan"])
	summary_svod_ranking_country_taiwan := slack.Attachment{
		Color: "#36a64f",
		Title: "SVOD Title valid ranking in Taiwan country on " + startDate,
		Text:  " \t" + svod_ranking_country_taiwan,
	}

	// Title valid ranking in Korea country
	svod_ranking_country_korea := getByRanking(sqlsf["svod_ranking_country_korea"])
	summary_svod_ranking_country_korea := slack.Attachment{
		Color: "#36a64f",
		Title: "SVOD Title valid ranking in Korea country on " + startDate,
		Text:  " \t" + svod_ranking_country_korea,
	}

	// Title valid ranking in Japan country
	svod_ranking_country_japan := getByRanking(sqlsf["svod_ranking_country_japan"])
	summary_svod_ranking_country_japan := slack.Attachment{
		Color: "#36a64f",
		Title: "SVOD Title valid ranking in Japan country on " + startDate,
		Text:  " \t" + svod_ranking_country_japan,
	}

	// Title valid ranking in China country
	svod_ranking_country_china := getByRanking(sqlsf["svod_ranking_country_china"])
	summary_svod_ranking_country_china := slack.Attachment{
		Color: "#36a64f",
		Title: "SVOD Title valid ranking in China country on " + startDate,
		Text:  " \t" + svod_ranking_country_china,
	}

	sendSlackMessage("=== Overall User Metrics ===", summary_signups)
	sendSlackMessage("", summary_svod_daa)

	sendSlackMessage("=== SVOD Ranking ===", summary_svod_ranking_all_users)
	sendSlackMessage("", summary_svod_ranking_freeTrial_users)
	sendSlackMessage("", summary_svod_ranking_premium_users)
	sendSlackMessage("", summary_svod_ranking_kkboxprime_users)
	sendSlackMessage("", summary_svod_ranking_country_taiwan)
	sendSlackMessage("", summary_svod_ranking_country_korea)
	sendSlackMessage("", summary_svod_ranking_country_japan)
	sendSlackMessage("", summary_svod_ranking_country_china)

}

func init() {
	log.Println("Lambda kktv-api-v3 DailyCharts init")

	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	kkapp.ContextInit()

	now := time.Now()
	zone, err := time.LoadLocation("Asia/Taipei")
	if err != nil {
		log.Println("Load timezone error:", err)
	}

	end = now.In(zone)
	start = now.In(zone).AddDate(0, 0, -1)
	strStart = fmt.Sprintf("%d-%02d-%02d", start.Year(), start.Month(), start.Day())
	strEnd = fmt.Sprintf("%d-%02d-%02d", end.Year(), end.Month(), end.Day())
	startDate = fmt.Sprintf("%d-%02d-%02d", start.Year(), start.Month(), start.Day())
}

func Handler() {

	GenDailyChart()
	log.Println("Dump daily chart to slack")
}

func main() {
	// AWS lambda
	lambda.Start(Handler)
}
