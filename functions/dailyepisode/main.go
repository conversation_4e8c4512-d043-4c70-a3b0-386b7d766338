package main

import (
	"fmt"
	"log"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/slack-go/slack"
)

var (
	slackAPI = slack.New("*********************************************************")

	//slackChannel = "#kktv-log-prod-encode"
	slackChannel = "#kktv-update-schedule"
)

func init() {
	log.Println("Lambda kktv-api-v3 dailyepisode init")

	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	kkapp.ContextInit()

}

func sendSlackMessage(msgs []string) {

	color := "#2EB886"
	now := time.Now()
	zone, err := time.LoadLocation("Asia/Taipei")
	if err != nil {
		log.Println("Load timezone error:", err)
	}

	var attachments []slack.Attachment

	taipeiNow := now.In(zone)

	for idx, item := range msgs {
		var attach slack.Attachment
		if idx == len(msgs)-1 {
			attach = slack.Attachment{
				Color:  color,
				Title:  "",
				Text:   item,
				Footer: fmt.Sprintf("publish for %s ", taipeiNow.Format("2006-01-02")),
			}
		} else {
			attach = slack.Attachment{
				Color: color,
				Title: "",
				Text:  item,
			}

		}
		attachments = append(attachments, attach)
	}

	channelID, timestamp, err := slackAPI.PostMessage(slackChannel, slack.MsgOptionText("", false), slack.MsgOptionAttachments(attachments...))
	if err != nil {
		log.Printf("%s\n", err)
		return
	}
	log.Printf("Message successfully sent to channel %s at %s", channelID, timestamp)
}

func Handler() {
	// compose slack message for episode publish today
	var items []*dbmeta.Episode
	var msgs []string
	var err error

	//now, _ := time.Parse("2006-01-2", "2020-12-16")
	now := time.Now()
	zone, _ := time.LoadLocation("Asia/Taipei")

	taipeiStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, zone)
	taipeiEnd := taipeiStart.AddDate(0, 0, 7) // notice from today to a week

	log.Println(items, now, zone)
	log.Println(taipeiStart.UTC(), taipeiEnd.UTC())
	db := kkapp.App.DbMeta.Slave()

	err = db.Select(&items,
		`SELECT id, name, series_id, meta, pub, unpub from meta_episode WHERE pub IS NOT NULL AND pub BETWEEN $1 AND $2 ORDER BY id;`,
		taipeiStart.UTC(),
		taipeiEnd.UTC(),
	)

	log.Println(items, err)
	//err = db.Select(&items, sqlQuery, taipeiNow.Format("2006-01-02"), taipeiNow.AddDate(0, 0, 1).Format("2006-01-02"))

	//if err != nil {
	//log.Println("[ERROR]", err)
	//}
	for idx := range items {
		items[idx].Parse()
		msg := fmt.Sprintf("%s %s publish at %s", items[idx].Meta.TitleName, items[idx].ID, items[idx].Pub.Time.In(zone).Format("2006-01-02 15:04:05"))
		log.Println(msg)
		msgs = append(msgs, msg)
	}
	if len(msgs) > 0 {
		sendSlackMessage(msgs)
	}
	log.Println(msgs)
}

func main() {
	// AWS lambda
	//Handler()
	lambda.Start(Handler)
}
