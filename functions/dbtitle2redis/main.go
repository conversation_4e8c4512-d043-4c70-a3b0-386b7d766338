package main

import (
	"context"
	"encoding/json"
	"errors"
	"log"
	"strings"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-lambda-go/lambdacontext"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	lambdasvc "github.com/aws/aws-sdk-go/service/lambda"
)

func init() {
	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	kkapp.ContextInit()
}

type Input struct {
	Args []string
}

func Handler(ctx context.Context, args Input) (err error) {

	lc, ok := lambdacontext.FromContext(ctx)
	if !ok {
		return errors.New("Cannot find Lambda Function name")
	}

	arn := lc.InvokedFunctionArn
	arnSlice := strings.Split(arn, ":function:")
	if len(arnSlice) != 2 && len(strings.Split(arnSlice[1], ":")) != 2 {
		return errors.New("Cannot find Lambda Function and Alias name")
	}

	fnSlice := strings.Split(arnSlice[1], ":")
	funcName := fnSlice[0]
	funcAlias := fnSlice[1]
	log.Println(funcName, funcAlias)

	// Dispatch mode
	if args.Args == nil {
		log.Println("[INFO] Running in Dispatch Mode, start to dispatch jobs")

		batchSize := 50
		titleIDs := []string{}
		batches := [][]string{}
		svc := lambdasvc.New(session.New())
		db := kkapp.App.DbMeta.Slave()

		err = db.Select(&titleIDs, "SELECT id from meta_title")
		if err != nil {
			log.Println("[ERROR]", err)
			return
		}

		for batchSize < len(titleIDs) {
			titleIDs, batches = titleIDs[batchSize:], append(batches, titleIDs[0:batchSize:batchSize])
		}
		batches = append(batches, titleIDs)

		// start to dispatch
		for _, titles := range batches {
			newArgs := Input{Args: titles}
			payLoad, _ := json.Marshal(newArgs)

			_, err := svc.Invoke(
				&lambdasvc.InvokeInput{
					FunctionName:   aws.String(funcName),
					InvocationType: aws.String("Event"),
					Payload:        payLoad,
					Qualifier:      aws.String(funcAlias),
				})
			log.Println("[INFO] dispath titles", titles, err)
		}
	}

	// Worker mode
	if args.Args != nil && len(args.Args) > 0 {
		log.Println("[INFO] Running in Worker Mode, start to process", args.Args)
		for _, titleID := range args.Args {
			log.Println(titleID)
			dbmeta.Title2Redis(titleID)
		}
	}

	return
}

func main() {
	// AWS lambda
	lambda.Start(Handler)
}
