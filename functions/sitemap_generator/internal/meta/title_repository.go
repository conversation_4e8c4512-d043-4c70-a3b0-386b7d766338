package meta

import (
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/pkg/database"
)

type TitleRepo interface {
	ListAllTitleID() ([]string, error)
	ListAllTitleList() ([]string, error)
	ListAllTitleMeta() ([]*dbmeta.TitleMeta, error)
}

type titleRepo struct {
	dbReader database.DB
}

func NewTitleRepo(dbReader database.DB) TitleRepo {
	return &titleRepo{
		dbReader: dbReader,
	}
}

func (r *titleRepo) ListAllTitleID() ([]string, error) {
	records := make([]string, 0)

	if err := r.dbReader.Select(&records, `select distinct id from meta_title where meta #>> '{available}' = 'true';`); err != nil {
		return nil, err
	}

	return records, nil
}

func (r *titleRepo) ListAllTitleList() ([]string, error) {
	records := make([]string, 0)

	if err := r.dbReader.Select(&records, `select meta->>'share_id' as share_id
	from meta_titlelist mt
	where list_type in ('choice', 'highlight')
		and jsonb_array_length(meta->'title_id') >= 4
		and meta->>'share_id' is not null;`); err != nil {
		return nil, err
	}

	return records, nil
}

func (r *titleRepo) ListAllTitleMeta() (items []*dbmeta.TitleMeta, err error) {
	records := make([]*dbmeta.Title, 0)

	if err := r.dbReader.Select(&records, `SELECT id, name, editor_comments, meta FROM meta_title ORDER BY id;`); err != nil {
		return nil, err
	}

	for idx := range records {
		if records[idx].MetaStr.Valid {
			item, err := records[idx].Parse()
			if err == nil {
				items = append(items, item)
			}
		}
	}

	return
}
