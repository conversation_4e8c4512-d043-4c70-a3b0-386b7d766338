package main

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/dynamodb"
	"github.com/aws/aws-sdk-go/service/dynamodb/dynamodbattribute"
	"github.com/davecgh/go-spew/spew"
	"github.com/guregu/dynamo"
)

var (
	sqlsync = map[string]string{
		"title":   `INSERT INTO meta_title (id, name, editor_comments, meta) VALUES ($1, $2, $3, $4) ON CONFLICT (id) DO UPDATE SET meta=$4`,
		"episode": `INSERT INTO meta_episode (id, name, series_id, meta) VALUES ($1, $2, $3, $4) ON CONFLICT (id) DO UPDATE SET meta=$4`,
		"extra":   `INSERT INTO meta_extra (id, name, title_id, series_id, meta) VALUES ($1, $2, $3, $4, $5) ON CONFLICT (id) DO UPDATE SET meta=$5`,
		"key": `INSERT INTO meta_key (id, content_id, content_type, key_value, meta) VALUES ($1, $2, $3, $4, $5) ON CONFLICT (id) DO UPDATE SET 
 content_id=$2, content_type=$3, key_value=$4, meta=$5;`,
		"series": `INSERT INTO meta_series (id, name, title_id, meta) VALUES ($1, $2, $3, $4) ON CONFLICT (id) DO UPDATE SET meta=$4`,
	}
)

func init() {
	log.Println("Lambda kktv-api-v3 init")
	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	kkapp.ContextInit()
}

// refer https://stackoverflow.com/questions/49129534/unmarshal-mapstringdynamodbattributevalue-into-a-struct
// UnmarshalStreamImage converts events.DynamoDBAttributeValue to struct
func UnmarshalStreamImage(attribute map[string]events.DynamoDBAttributeValue, out interface{}) error {

	dbAttrMap := make(map[string]*dynamodb.AttributeValue)

	for k, v := range attribute {

		var dbAttr dynamodb.AttributeValue

		bytes, marshalErr := v.MarshalJSON()
		if marshalErr != nil {
			return marshalErr
		}

		json.Unmarshal(bytes, &dbAttr)
		dbAttrMap[k] = &dbAttr
	}

	return dynamodbattribute.UnmarshalMap(dbAttrMap, out)
	// return dynamo.UnmarshalItem(dbAttrMap, out)

}

// data-keys
func HandlerKey() {
	//  meta_key
	// id character varying(14) NOT NULL,
	var err error
	var results []map[string]interface{}
	var table dynamo.Table

	db := dynamo.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})

	if kkapp.App.Debug {
		table = db.Table("test-data-keys")
	} else {
		table = db.Table("prod-data-keys")
	}
	err = table.Scan().All(&results)
	if err != nil {
		log.Println(err)
		return
	}
	log.Println("Count:", len(results))

	for _, item := range results {
		log.Println(item)
		var key_id, content_id, content_type, key_value string

		if _, ok := item["key_id"]; ok {
			key_id = item["key_id"].(string)
		}

		if _, ok := item["key_value"]; ok {
			key_value = item["key_value"].(string)
		}

		if _, ok := item["content_id"]; ok {
			content_id = item["content_id"].(string)
		}

		if _, ok := item["content_type"]; ok {
			content_type = item["content_type"].(string)
		}

		if key_id == "" || item == nil {
			log.Println("[ERROR] empty key_id", item)
			continue
		}

		// spew.Dump(item)
		jsonBytes, err := json.Marshal(item)
		if err != nil {
			log.Println("[ERROR] json.Marshal", err)
			spew.Dump(item)
			continue
		}

		db := kkapp.App.DbMeta.Master()
		_, err = db.Exec(sqlsync["key"], key_id, content_id, content_type, key_value, jsonBytes)

		if err != nil {
			log.Println("[ERROR]", err)
		}

	}

}

// data-extras
func HandlerExtra() {
	//  meta_extra
	// id        | character varying(14) |           | not null |
	var err error
	var results []map[string]interface{}
	var table dynamo.Table

	db := dynamo.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})

	if kkapp.App.Debug {
		table = db.Table("test-data-extras")
	} else {
		table = db.Table("prod-data-extras")
	}
	err = table.Scan().All(&results)
	if err != nil {
		log.Println(err)
		return
	}
	log.Println("Count:", len(results))
	for _, item := range results {
		var episode_id, series_id, title_id, episode_name string

		if _, ok := item["episode_id"]; ok {
			episode_id = item["episode_id"].(string)
		}

		if _, ok := item["series_id"]; ok {
			series_id = item["series_id"].(string)
		}

		if _, ok := item["title_id"]; ok {

			title_id = item["title_id"].(string)
		}

		if _, ok := item["episode_name"]; ok {

			episode_name = item["episode_name"].(string)
		}

		if episode_id == "" || series_id == "" {
			log.Println("[ERROR] empty episode_id or series_id", item)
			spew.Dump(item)
			continue
		}

		jsonBytes, err := json.Marshal(item)
		if err != nil {
			log.Println("[ERROR] json.Marshal", err)
			spew.Dump(item)
			continue
		}

		db := kkapp.App.DbMeta.Master()
		_, err = db.Exec(sqlsync["extra"], episode_id, episode_name, title_id, series_id, jsonBytes)

		if err != nil {
			log.Println("[ERROR]", err)
		}

	}
}

func HandlerTitle() {
	//  meta_title
	// id              | character varying(8)  |           | not null |
	// get all items
	var err error
	var results []map[string]interface{}
	var table dynamo.Table

	db := dynamo.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})

	if kkapp.App.Debug {
		table = db.Table("test-data-titles")
	} else {
		table = db.Table("prod-data-titles")
	}
	err = table.Scan().All(&results)

	if err != nil {
		log.Println(err)
		return
	}
	log.Println("Count:", len(results))
	for _, item := range results {
		var title_id, title_name string
		title_id = item["title_id"].(string)
		title_name = item["title_name"].(string)

		if title_id == "" || title_name == "" {
			log.Println("[ERROR] empty title_id or title_name")
			continue
		}
		log.Println(title_id, title_name)

		jsonBytes, err := json.Marshal(item)
		if err != nil {
			log.Println("[ERROR] json.Marshal", err)
			// spew.Dump(newOne)
			return
		}

		log.Println(string(jsonBytes))

		db := kkapp.App.DbMeta.Master()
		_, err = db.Exec(sqlsync["title"], title_id, title_name, "", jsonBytes)

		if err != nil {
			log.Println("[ERROR]", err)
		}

	}
}

func HandlerEpisode() {
	//  meta_episode
	// id        | character varying(14) |           | not null |
	var err error
	var results []map[string]interface{}
	var table dynamo.Table

	db := dynamo.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})

	if kkapp.App.Debug {
		table = db.Table("test-data-episodes")
	} else {
		table = db.Table("prod-data-episodes")
	}
	err = table.Scan().All(&results)
	if err != nil {
		log.Println(err)
		return
	}
	log.Println("Count:", len(results))

	for _, item := range results {
		var episode_id, series_id, episode_name string

		if _, ok := item["episode_id"]; ok {

			episode_id = item["episode_id"].(string)
		}

		if _, ok := item["series_id"]; ok {

			series_id = item["series_id"].(string)
		}

		if _, ok := item["episode_name"]; ok {
			episode_name = item["episode_name"].(string)
		}

		if episode_id == "" || series_id == "" {
			log.Println("[ERROR] empty episode_id or series_id", item)
			continue
		}

		jsonBytes, err := json.Marshal(item)
		if err != nil {
			log.Println("[ERROR] json.Marshal", err)
			spew.Dump(item)
			continue
		}

		db := kkapp.App.DbMeta.Master()
		_, err = db.Exec(sqlsync["episode"], episode_id, episode_name, series_id, jsonBytes)

		if err != nil {
			log.Println("[ERROR]", err)
			spew.Dump(item)
		}

	}

}

func HandlerSeries() {
	//  meta_series
	// id     | name  | title_id | meta
	var err error
	var results []map[string]interface{}
	var table dynamo.Table

	db := dynamo.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})

	if kkapp.App.Debug {
		table = db.Table("test-meta-series")
	} else {
		table = db.Table("prod-meta-series")
	}
	err = table.Scan().All(&results)
	if err != nil {
		log.Println(err)
		return
	}
	log.Println("Count:", len(results))
	seriesNameFmt := "第%d季"

	for _, item := range results {
		var title_id, series_id, seriesName string

		if _, ok := item["title_id"]; ok {

			title_id = item["title_id"].(string)
		}

		if _, ok := item["series_id"]; ok {

			series_id = item["series_id"].(string)
		}

		if title_id == "" || series_id == "" {
			log.Println("[ERROR] empty episode_id or series_id", item)
			continue
		}
		seriesNumber, err := strconv.Atoi(series_id)
		if err != nil {
			log.Println("[ERROR] wrong series_id", series_id)
			continue

		}

		jsonBytes, err := json.Marshal(item)
		if err != nil {
			log.Println("[ERROR] json.Marshal", err)
			spew.Dump(item)
			continue
		}
		dbSeriesID := title_id + series_id
		seriesName = fmt.Sprintf(seriesNameFmt, seriesNumber)

		db := kkapp.App.DbMeta.Master()
		// log.Println(dbSeriesID, seriesName, title_id)
		// log.Println(string(jsonBytes))
		_, err = db.Exec(sqlsync["series"], dbSeriesID, seriesName, title_id, jsonBytes)

		if err != nil {
			log.Println("[ERROR]", err)
			spew.Dump(item)
		}

	}

}

type input struct {
	Name string `json:"name"`
}

func Handler(in *input) (string, error) {
	var sourceTable string

	switch {

	case strings.Contains(in.Name, "data-episodes"):
		sourceTable = "data-episodes"
		HandlerEpisode()

	case strings.Contains(in.Name, "data-titles"):
		sourceTable = "data-titles"
		HandlerTitle()

	case strings.Contains(in.Name, "data-extras"):
		sourceTable = "data-extras"
		HandlerExtra()

	case strings.Contains(in.Name, "data-keys"):
		sourceTable = "data-keys"
		HandlerKey()

	case strings.Contains(in.Name, "meta-series"):
		sourceTable = "meta-series"
		HandlerSeries()
	}

	return fmt.Sprintf("Dump from DynamoDB %s to RDS", sourceTable), nil
}

func main() {
	// AWS lambda
	// local to invoke lambda ex:
	// echo '{"name": "meta-series"}' |  apex invoke metadump --alias test
	lambda.Start(Handler)
}
