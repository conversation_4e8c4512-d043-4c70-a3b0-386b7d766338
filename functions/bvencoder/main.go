package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"path"
	"regexp"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/bv"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-lambda-go/lambdacontext"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/ecs"
	lambdasvc "github.com/aws/aws-sdk-go/service/lambda"
	"github.com/nlopes/slack"
)

const (
	bvBucketUploadURL = "https://kktv-blendvision-upload.s3-ap-northeast-1.amazonaws.com"
)

var (
	reEp    = regexp.MustCompile(`t[0-9]{8}_s[0-9]{2}_e[0-9]{4}_me\.(mp4|mov|ts|mpg)`)
	reExtra = regexp.MustCompile(`t[0-9]{8}_[tr|ot|iv|bs]{2}[0-9]{2}.(mp4|mov|ts|mpg)`)
	reUUID  = regexp.MustCompile(`[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}`)

	slackAPI        = slack.New("*********************************************************")
	slackChannelFmt = "#kktv-log-%s-encode"
)

func init() {
	log.Println("Lambda init")

	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	kkapp.ContextInit()
}

// SlackMessage send message to slack
func SlackMessage(slackChannel, msg string) {
	var color string
	if strings.HasPrefix(msg, "[ERROR]") {
		color = "#A30200"
	} else {
		color = "#2EB886"
	}
	attach := slack.Attachment{
		Color: color,
		Title: "",
		Text:  msg,
	}

	slackAPI.PostMessage(slackChannel, slack.MsgOptionText("", false), slack.MsgOptionAttachments(attach))

	log.Printf("Message sent to channel %s at %s", slackChannel, time.Now())
}

// HandlerVideo handle video.mp4 from blendvision
func HandlerVideo(ctx context.Context, record events.S3EventRecord) (err error) {
	var slackChannel, env, msg string
	lc, ok := lambdacontext.FromContext(ctx)
	if !ok {
		return errors.New("Cannot find Lambda Function name")
	}

	arn := lc.InvokedFunctionArn
	arnSlice := strings.Split(arn, ":function:")
	if len(arnSlice) != 2 && len(strings.Split(arnSlice[1], ":")) != 2 {
		return errors.New("Cannot find Lambda Function and Alias name")
	}

	fnSlice := strings.Split(arnSlice[1], ":")
	funcName := fnSlice[0]
	funcAlias := fnSlice[1]

	//// trigger next batch
	svc := lambdasvc.New(session.New())
	log.Println("[INFO] trigger next batch", funcName, funcAlias, svc)

	s3Obj := record.S3
	bucketName := s3Obj.Bucket.Name
	s3ObjKey := s3Obj.Object.Key
	log.Println(bucketName, s3ObjKey)

	jobID := reUUID.FindString(s3ObjKey)
	bvJob, err := bv.NewBVFromID(jobID)

	if err != nil {
		log.Println("[ERROR] BlendVision Encoder Bot", err)
	}

	nextJob := events.S3Event{Records: []events.S3EventRecord{}}
	nextJob.Records = append(nextJob.Records, record)
	payLoad, _ := json.Marshal(nextJob)
	log.Println("[INFO] next natch payLoad", string(payLoad))

	err = bvJob.SyncState()

	if bvJob.State.Env == "prod" {
		slackChannel = fmt.Sprintf(slackChannelFmt, "prod")
		env = "prod"
	} else {
		slackChannel = fmt.Sprintf(slackChannelFmt, "test")
		env = "test"
	}

	if err != nil {
		msg = fmt.Sprintf("[ERROR] BlendVision Encoder Bot SyncState %s", err.Error())
		log.Println(msg)
		SlackMessage(slackChannel, msg)
		return
	}

	switch bvJob.State.Status {

	case bv.StatusJobCreate:
		// bv.StatusJobCreate  => bv.StatusStart
		bvJob.State.Status = bv.StatusStart
		bvJob.WriteState()
		// pass around next state, env define via bvJob.State.Env
		_, err = svc.Invoke(
			&lambdasvc.InvokeInput{
				FunctionName:   aws.String(funcName),
				InvocationType: aws.String("Event"),
				Payload:        payLoad,
				Qualifier:      aws.String(env),
			})

	case bv.StatusStart:
		// bv.StatusStart  => bv.StatusManifest
		// try 3 times
		tryCount := 0

		for tryCount < 3 {
			time.Sleep(10 * time.Second)
			log.Println("[INFO] try download", tryCount)
			_, err = bvJob.Download()
			if err == nil {
				break
			}
			tryCount++
		}

		if err != nil {
			msg = fmt.Sprintf("[ERROR] BlendVision Encoder Bot Download %s", err.Error())
			log.Println(msg)
			SlackMessage(slackChannel, msg)
			bvJob.State.Status = bv.StatusError
			bvJob.WriteState()
			return
		}
		_, err = bvJob.Generate()
		if err != nil {
			msg = fmt.Sprintf("[ERROR] BlendVision Encoder Bot Generate %s", err.Error())
			log.Println(msg)
			SlackMessage(slackChannel, msg)
			bvJob.State.Status = bv.StatusError
			bvJob.WriteState()
			return
		}
		// update state
		bvJob.State.Status = bv.StatusManifest
		bvJob.WriteState()

		// pass around next state
		_, err = svc.Invoke(
			&lambdasvc.InvokeInput{
				FunctionName:   aws.String(funcName),
				InvocationType: aws.String("Event"),
				Payload:        payLoad,
				Qualifier:      aws.String(env),
			})
	case bv.StatusManifest:
		// bv.StatusManifest  => bv.StatusCopyDash
		_, err = bvJob.CopyDash()
		if err != nil {
			msg = fmt.Sprintf("[ERROR] BlendVision Encoder Bot CopyDash %s", err.Error())
			log.Println(msg)
			SlackMessage(slackChannel, msg)
			bvJob.State.Status = bv.StatusError
			bvJob.WriteState()
			return
		}

		// pass around next state
		_, err = svc.Invoke(
			&lambdasvc.InvokeInput{
				FunctionName:   aws.String(funcName),
				InvocationType: aws.String("Event"),
				Payload:        payLoad,
				Qualifier:      aws.String(env),
			})

	case bv.StatusCopyDash:
		// bv.StatusCopyDash => bv.StatusCopyHls
		_, err = bvJob.CopyHls()
		if err != nil {
			msg = fmt.Sprintf("[ERROR] BlendVision Encoder Bot CopyHls %s", err.Error())
			log.Println(msg)
			SlackMessage(slackChannel, msg)
			bvJob.State.Status = bv.StatusError
			bvJob.WriteState()
			return
		}

		// pass around next state
		_, err = svc.Invoke(
			&lambdasvc.InvokeInput{
				FunctionName:   aws.String(funcName),
				InvocationType: aws.String("Event"),
				Payload:        payLoad,
				Qualifier:      aws.String(env),
			})
	case bv.StatusCopyHls:
		// bv.StatusCopyHls => bv.StatusThumbnail
		_, err = bvJob.Thumbnail()
		if err != nil {
			msg = fmt.Sprintf("[ERROR] BlendVision Encoder Bot Thumbnail %s", err.Error())
			log.Println(msg)
			SlackMessage(slackChannel, msg)
			bvJob.State.Status = bv.StatusError
			bvJob.WriteState()
			return
		}

		// pass around next state
		_, err = svc.Invoke(
			&lambdasvc.InvokeInput{
				FunctionName:   aws.String(funcName),
				InvocationType: aws.String("Event"),
				Payload:        payLoad,
				Qualifier:      aws.String(env),
			})
	case bv.StatusThumbnail:
		// bv.StatusThumbnail => bv.StatusCleanup => bv.StatusSuccess
		_, err = bvJob.Cleanup()
		if err != nil {
			msg = fmt.Sprintf("[ERROR] BlendVision Encoder Bot Cleanup %s", err.Error())
			log.Println(msg)
			SlackMessage(slackChannel, msg)
			bvJob.State.Status = bv.StatusError
			bvJob.WriteState()
			return
		}

		// done thumbnail, all job down
		bvJob.State.Status = bv.StatusSuccess
		bvJob.WriteState()
		msg = fmt.Sprintf("[INFO] BlendVision Encoder Bot Job Done, EpisodeID %s, JobID %s", bvJob.EpisodeID, bvJob.JobID)
		log.Println(msg)
		SlackMessage(slackChannel, msg)
	}
	return err
}

// Handler for main
// s3://kktv-blendvision-upload/{title, test-title} for upload video mp4|mov|ts|mpg]{2,3}
// => check filename create new encoder job
//
// s3://kktv-blendvision/uuid-v4/{files} blendvision result files
// use hls.m3u8 file for S3 key
// pass around the encoder state until finished all jobs
func Handler(ctx context.Context, s3Event events.S3Event) (err error) {

	lc, ok := lambdacontext.FromContext(ctx)
	if !ok {
		return errors.New("Cannot find Lambda Function name")
	}
	arn := lc.InvokedFunctionArn
	arnSlice := strings.Split(arn, ":function:")
	if len(arnSlice) != 2 && len(strings.Split(arnSlice[1], ":")) != 2 {
		return errors.New("Cannot find Lambda Function and Alias name")
	}

	//fnSlice := strings.Split(arnSlice[1], ":")
	//funcName := fnSlice[0]
	//funcAlias := fnSlice[1]
	//// trigger next batch
	//svc := lambdasvc.New(session.New())
	//log.Println("[INFO] trigger next batch", funcName, funcAlias, svc)

	for _, record := range s3Event.Records {
		var slackChannel, env, msg string
		s3Obj := record.S3
		bucketName := s3Obj.Bucket.Name
		s3ObjKey := s3Obj.Object.Key
		log.Println(bucketName, s3ObjKey)

		////////////////////////////////////////////////////////////////////////////////
		// BlendVision use one encoder result file to catch bv callback
		if strings.HasSuffix(s3ObjKey, "video.mp4") && reUUID.FindString(s3ObjKey) != "" {
			HandlerVideo(ctx, record)
		}

		////////////////////////////////////////////////////////////////////////////////
		// translator mp4
		if strings.HasSuffix(s3ObjKey, "/translator.mp4") && reUUID.FindString(s3ObjKey) != "" {
			log.Println("[INFO] found translator.mp4", s3ObjKey)

			jobID := reUUID.FindString(s3ObjKey)
			bvJob, _ := bv.NewBVFromID(jobID)

			if bvJob == nil {
				log.Println("[ERROR] BlendVision translator file:", s3ObjKey)
				continue
			}

			bvJob.SyncState()
			env = bvJob.State.Env

			if bvJob.State.Env == "prod" {
				slackChannel = fmt.Sprintf(slackChannelFmt, "prod")
				env = "prod"
			} else {
				slackChannel = fmt.Sprintf(slackChannelFmt, "test")
				env = "test"
			}

			_, err = bvJob.CopyRush(s3ObjKey)
			if err != nil {
				msg = fmt.Sprintf("[ERROR] BlendVision Encoder Bot CopyRush Env: %s %s", env, err.Error())
				log.Println(msg)
				SlackMessage(slackChannel, msg)
				continue
			}
			msg = fmt.Sprintf(`"The rushes of *%s* is encoded (%s). Please download from <%s|here>."`, path.Base(bvJob.State.SourceURL), env, bvJob.RushURL)
			log.Println(msg)
			SlackMessage("#kktv-rushes", msg)

		}

		////////////////////////////////////////////////////////////////////////////////
		// Content Team upload video for encoder
		epStr := reEp.FindString(s3ObjKey)
		extraEpStr := reExtra.FindString(s3ObjKey)
		// video file under title folder, fire new blendvision job
		if (epStr != "" || extraEpStr != "") && strings.Contains(s3ObjKey, "title/") {
			var env string
			if strings.Contains(s3ObjKey, "test-title/") {
				env = "test"
				slackChannel = fmt.Sprintf(slackChannelFmt, env)
			} else {
				env = "prod"
				slackChannel = fmt.Sprintf(slackChannelFmt, env)
			}

			url := fmt.Sprintf("%s/%s", bvBucketUploadURL, s3ObjKey)
			bvJob, err := bv.NewBVJob(url)
			if err != nil {
				msg = fmt.Sprintf("[ERROR] BlendVision Encoder Bot New Job %s for %s", err.Error(), url)
				log.Println(msg)
				SlackMessage(slackChannel, msg)
				continue
			}

			// priority queue
			if !bvJob.IsEnding {
				// IsEnding is false, submit in priority queue
				bvJob.SetQueue("pq")
			}
			_, err = bvJob.SubmitJob()

			if err != nil {
				msg = fmt.Sprintf("[ERROR] BlendVision Encoder Bot SubmitJob %s for %s", err.Error(), url)
				log.Println(msg)
				SlackMessage(slackChannel, msg)
				continue
			}

			msg = fmt.Sprintf("[INFO] BlendVision Encoder Submit %s JobID %s, source file %s", env, bvJob.JobID, url)
			SlackMessage(slackChannel, msg)

			// handle translator video
			// invoke ecs worker if needed, the ecs vpc subnet under kktv-test-encoder
			if bvJob.HasSubtitles && epStr != "" {
				s3videoPath, err := bvJob.CopyForTranslator(bucketName, s3ObjKey)

				if err == nil {
					svc := ecs.New(session.New())
					taskDefine := aws.String("kktv-worker")
					input := &ecs.RunTaskInput{
						Cluster:        aws.String("kktv-worker"),
						TaskDefinition: taskDefine,
					}

					input.LaunchType = aws.String("FARGATE")
					input.NetworkConfiguration = &ecs.NetworkConfiguration{
						AwsvpcConfiguration: &ecs.AwsVpcConfiguration{
							Subnets:        aws.StringSlice([]string{"subnet-dce183aa", "subnet-dbe44e83"}),
							AssignPublicIp: aws.String("DISABLED"),
						},
					}

					override := &ecs.ContainerOverride{}
					override.SetEnvironment([]*ecs.KeyValuePair{
						&ecs.KeyValuePair{Name: aws.String("S3_VIDEO"), Value: aws.String(s3videoPath)}},
					)

					override.SetName("kktv-video-worker")
					input.Overrides = &ecs.TaskOverride{
						ContainerOverrides: []*ecs.ContainerOverride{override},
					}
					_, err = svc.RunTask(input)
				}

				if err != nil {
					msg = fmt.Sprintf("[ERROR] Start translator ECS task for %s JobID %s, %s", env, bvJob.JobID, err)
				} else {
					msg = fmt.Sprintf("[INFO] Start translator ECS task for %s JobID %s", env, bvJob.JobID)
				}

				SlackMessage(slackChannel, msg)

			}
		}
		// End Content Team upload video for encoder
		////////////////////////////////////////////////////////////////////////////////
	}

	return err

}

func main() {
	// AWS lambda
	lambda.Start(Handler)
	// ctx := context.TODO()
	// lc := new(lambdacontext.LambdaContext)
	// lc.InvokedFunctionArn = "aws:lambda:ap-northeast-1:312530604583:function:kktv-api-v3_bvencoder:test"
	// Handler(lambdacontext.NewContext(ctx, lc))
}
