package main

import (
	"fmt"
	"log"

	"github.com/KKTV/kktv-api-v3/kktvapi/cmd"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/apex/gateway"
)

func main() {
	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	// AWS lambda
	server := cmd.NewHttpServer()
	host := fmt.Sprintf(":%d", config.Port)
	fmt.Println("Listening on", config.Port)
	if config.Debug {
		fmt.Println("Running in DEBUG mode")
	}
	log.Fatal(gateway.ListenAndServe(host, server))
}
