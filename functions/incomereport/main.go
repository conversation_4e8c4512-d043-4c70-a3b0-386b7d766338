// income report
//
// ### 彙整表 ###
//
// 表單內容中的 "當月交易金額", "當月手續費金額(未稅)" 都是過濾交易時間是報表相同月份的加總
// tstar 彙整表的每一個數字加總前，要增加過濾，必須當月至少有登入進去過的使用者訂單才列入
// 計算 (也就是有使用等於 1 的使用者)

package main

import (
	"bytes"
	"fmt"
	"log"
	"math"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbuser"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/secret"

	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/jmoiron/sqlx/types"
	"github.com/lib/pq"
	"github.com/tealeg/xlsx"
	"gopkg.in/guregu/null.v3"
)

var (
	// share columns
	columns = `orders.id, orders.user_id, product_id, realized_at, start_date, end_date, price::numeric::int, price_no_tax::numeric::int, status,
 orders.payment_type, info, fee, external_order_id,
 info->>'TradeNo' as tradeno, info->>'CVSCode' as cvscode, info->>'redeemCode' as redeemcode, info->>'description' as description`
	columnUserID                           = "User ID"
	columnOrderID                          = "商店訂單編號"
	columnPriceNoTax                       = "交易金額"
	columnTradeNo                          = "智付通交易序號"
	targetMonth                            = ""
	inTargetMonth                          = ""
	daysThisMonth                          = 0
	dateFormat                             = "01/02/2006"          // mm/dd/yyyy
	dateFormatYMD                          = "2006-01-02 15:04:05" // yyyy-mm-dd
	taipeiZone, _                          = time.LoadLocation("Asia/Taipei")
	appleZone                              = time.FixedZone("UTC-8", -8*60*60)
	month, monthStart, monthEnd, nextMonth time.Time
	xlsxFile                               *xlsx.File
	xlsxFileName                           = ""
	reTitle                                = regexp.MustCompile(`\((.*)\)`)
	reportBucket                           = "kktv-report-income"

	summary = []Row{}

	sumChannel, sumOther, sumOrganic, sumMultiPlan Row

	// map for product_id to product
	products = map[int64]dbuser.Product{}
)

type Order struct {
	ID              string         `db:"id" json:"id"`
	UserID          string         `db:"user_id" json:"user_id"`
	ProductID       int64          `db:"product_id" json:"product_id"`
	Price           int64          `db:"price" json:"price"`
	PaymentType     string         `db:"payment_type" json:"payment_type"`
	StartDate       time.Time      `db:"start_date" json:"start_date"`
	EndDate         time.Time      `db:"end_date" json:"end_date"`
	OrderDate       time.Time      `db:"order_date" json:"order_date"`
	Status          null.String    `db:"status" json:"status"`
	CreatedAt       null.Time      `db:"created_at" json:"created_at"`
	RealizedAt      null.Time      `db:"realized_at" json:"realized_at"`
	CanceledAt      null.Time      `db:"canceled_at" json:"canceled_at"`
	PriceNoTax      int            `db:"price_no_tax" json:"price_no_tax"`
	TaxRate         int64          `db:"tax_rate" json:"tax_rate"`
	Invoice         null.Int       `db:"invoice" json:"invoice"`
	Fee             int            `db:"fee" json:"fee"`
	ExternalOrderID null.String    `db:"external_order_id" json:"external_order_id"`
	InfoJSON        types.JSONText `db:"info"`
	Info            null.String    `db:"null" json:"info"`
	PaidSubscribers int            `db:"paid_user"`

	// extra field
	TradeNo       null.String `db:"tradeno"`
	CVSCode       null.String `db:"cvscode"`
	RedeemCode    null.String `db:"redeemcode"`
	Description   null.String `db:"description"`
	TelecomMpID   null.String `db:"telecom_mp_id" json:"-"`
	TstarOrderID  null.String `db:"tstar_order_id" json:"-"`
	UsedThisMonth int
}

type Row struct {
	Col                map[string]interface{}
	Name               string
	PriceNoTax         int
	PriceThisMonth     int
	PriceInFuture      int
	Fee                int
	FeeThisMonth       int
	FeeInFuture        int
	OrderDaysThisMonth int
	Persons            int
	ARPU               float64
	ARPUnoFee          float64
	UsedThisMonth      int
	ReportMonthString  string
	Category           string
	PaidSubscribers    int
}

func init() {
	log.Println("Lambda kktv-api-v3 init")
	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	secret.Init(config.Env)
	kkapp.ContextInit()

	// prepare products map
	queryResult := []dbuser.Product{}
	db := kkapp.App.DbUser.Slave()
	queryStr := `SELECT
		id,
		name,
		country,
		price,
		duration,
		created_at,
		updated_at,
		deleted_at,
		payment_type,
		currency,
		price_no_tax,
		tax_rate,
		item_name,
		item_unit,
		auto_renew,
		active,
		free_duration,
		as_subscribe,
		fee,
		fee_rate,
		payment_type_code,
		purchase_upper_limit_count,
		external_product_id,
		discount_duration,
		discount_price,
		bundle,
		sort,
		category
	FROM products
	ORDER BY payment_type, id;`
	err := db.Select(&queryResult, queryStr)
	if err != nil {
		log.Println("Select products failed: ", err)
	}

	for _, item := range queryResult {
		idInt64, _ := strconv.ParseInt(item.ID, 10, 64)
		products[idInt64] = item
	}
}

// //////////////////////////////////////////////////////////////////////////////
// help func
func printHead(a ...interface{}) {
	fmt.Println("##########", a, "##########")
}

func printHint(a ...interface{}) {
	fmt.Println("----------------------------------------------------------------------------------------------------")
	fmt.Println(a...)
	fmt.Println("----------------------------------------------------------------------------------------------------")
}

func days(start, end time.Time) int {
	durationHour := end.Sub(start).Hours()
	switch {
	case durationHour <= 0:
		return 0
	case durationHour < 24:
		return 1
	}
	return int(durationHour / 24)
}

func max(a, b time.Time) time.Time {
	if a.After(b) {
		return a
	}
	return b
}

func min(a, b time.Time) time.Time {
	if a.Before(b) {
		return a
	}
	return b
}

func orderDays(o Order) int {
	return days(o.StartDate, o.EndDate)
}

func orderDaysUntilLastMonth(o Order) int {
	return days(o.StartDate, min(o.EndDate, monthStart))
}

func orderDaysThisMonth(o Order) int {
	return days(max(o.StartDate, monthStart), min(o.EndDate, nextMonth))
}

func orderDaysUntilThisMonth(o Order) int {
	return days(o.StartDate, min(o.EndDate, nextMonth))
}

func orderDaysInFuture(o Order) int {
	return days(max(o.StartDate, nextMonth), o.EndDate)
}

func priceUntilThisMonth(o Order, price int) int {
	if orderDays(o) == 0 || price == 0 {
		return 0
	}
	floatResult := float64(orderDaysUntilThisMonth(o)) / float64(orderDays(o)) * float64(price)
	return int(math.Round(floatResult))
}

func priceUntilLastMonth(o Order, price int) int {
	if orderDaysUntilThisMonth(o) == 0 || price == 0 {
		return 0
	}
	floatResult := float64(orderDaysUntilLastMonth(o)) / float64(orderDaysUntilThisMonth(o)) * float64(priceUntilThisMonth(o, price))
	return int(math.Round(floatResult))
}

func priceThisMonth(o Order, price int) int {
	return priceUntilThisMonth(o, price) - priceUntilLastMonth(o, price)
}

func priceInFuture(o Order, price int) int {
	return price - priceUntilThisMonth(o, price)
}

func getOrders(status []string, payment_type string) (orders []Order, err error) {
	var sqlstmt string
	sqlstmt = fmt.Sprintf(`SELECT %s FROM orders
 WHERE status = ANY($1)
 AND payment_type = $2 
 AND %s ORDER BY orders.id`, columns, inTargetMonth)

	db := kkapp.App.DbUser.Slave()
	fmt.Println("SQL Statement for", payment_type)
	printHint(sqlstmt, status, payment_type)
	err = db.Select(&orders, sqlstmt, pq.Array(status), payment_type)
	return
}

func getOrdersNonZeroPrice(status []string, payment_type string) (orders []Order, err error) {
	var sqlstmt string
	sqlstmt = fmt.Sprintf(`SELECT %s FROM orders
 WHERE status = ANY($1)
 AND payment_type = $2 
 AND %s AND orders.price::numeric > 0 ORDER BY orders.id`, columns, inTargetMonth)

	db := kkapp.App.DbUser.Slave()
	fmt.Println("SQL Statement for", payment_type)
	printHint(sqlstmt, status, payment_type)
	err = db.Select(&orders, sqlstmt, pq.Array(status), payment_type)
	return
}

func getCreditCardNonZeroPrice(status []string, payment_type string) (orders []Order, err error) {
	var sqlstmt string
	sqlstmt = fmt.Sprintf(`SELECT %s, 
		case when jsonb_array_length(u.media_source->'family') is null
		then 1
		else jsonb_array_length(u.media_source->'family')
		end as paid_user
 FROM orders
 left join users u on orders.user_id = u.id
 WHERE status = ANY($1)
 AND payment_type = $2
 AND %s AND orders.price::numeric > 0 ORDER BY orders.id`, columns, inTargetMonth)

	db := kkapp.App.DbUser.Slave()
	fmt.Println("SQL Statement for", payment_type)
	printHint(sqlstmt, status, payment_type)
	err = db.Select(&orders, sqlstmt, pq.Array(status), payment_type)
	return
}

func getSonetOrders(status []string, payment_type string) (orders []Order, err error) {
	var sqlstmt string
	sqlstmt = fmt.Sprintf(`SELECT %s FROM orders
 INNER JOIN payment_info ON orders.user_id = payment_info.user_id
 WHERE status = ANY($1)
 AND orders.payment_type = $2 
 AND %s AND orders.price::numeric > 0 
 AND telecom_mp_id != 'CYC_TSTAR' ORDER BY orders.id`, columns, inTargetMonth)

	db := kkapp.App.DbUser.Slave()
	fmt.Println("SQL Statement for", payment_type)
	printHint(sqlstmt, status, payment_type)
	err = db.Select(&orders, sqlstmt, pq.Array(status), payment_type)
	return
}

func getTstarOrders(status []string, payment_type string) (orders []Order, err error) {
	var sqlstmt string
	sqlstmt = fmt.Sprintf(`SELECT %s, tstar_order_id  FROM orders
 INNER JOIN payment_info ON orders.user_id = payment_info.user_id
 WHERE status = ANY($1)
 AND orders.payment_type = $2 
 AND %s AND orders.price::numeric > 0 
 AND telecom_mp_id = 'CYC_TSTAR' ORDER BY orders.id`, columns, inTargetMonth)

	db := kkapp.App.DbUser.Slave()
	fmt.Println("SQL Statement for", payment_type)
	printHint(sqlstmt, status, payment_type)
	err = db.Select(&orders, sqlstmt, pq.Array(status), payment_type)
	return
}

func checkUsed(usedMap map[string]int) (err error) {
	var sqlstmt string
	var userIDs []string
	mapIDs := []string{}
	for key, _ := range usedMap {
		mapIDs = append(mapIDs, key)
	}
	sqlstmt = fmt.Sprintf(`SELECT DISTINCT user_id FROM tokens WHERE 
 created_at >= '%s'  AND created_at < '%s'
 AND user_id = ANY($1);`, monthStart.Format(dateFormatYMD), monthEnd.Format(dateFormatYMD))
	db := kkapp.App.DbUser.Slave()
	fmt.Println("SQL Statement for userd userIDs")
	printHint(sqlstmt)
	err = db.Select(&userIDs, sqlstmt, pq.Array(mapIDs))
	for _, uid := range userIDs {
		usedMap[uid] = 1
	}
	return
}

func fillRow(o Order) (row Row) {

	row.Col = make(map[string]interface{})

	// user, order id
	row.Col["User ID"] = o.UserID
	row.Col["商店訂單編號"] = o.ID
	row.Col["交易金額"] = o.PriceNoTax

	row.PriceNoTax = o.PriceNoTax
	row.PaidSubscribers = o.PaidSubscribers

	// date
	row.Col["啟用日期"] = o.StartDate.In(taipeiZone).Format(dateFormat)
	//if end_date is 2017-06-01, user can only use the service until 2017-05-31, hence we minus 1 day here for report's requirement
	row.Col["到期日期"] = o.EndDate.AddDate(0, 0, -1).In(taipeiZone).Format(dateFormat)
	row.Col["上月最後一天日期"] = monthStart.AddDate(0, 0, -1).In(taipeiZone).Format(dateFormat)
	row.Col["當月最後一天日期"] = monthEnd.In(taipeiZone).Format(dateFormat)

	// days
	_orderDaysThisMonth := orderDaysThisMonth(o)
	row.Col["總攤銷天數"] = orderDays(o)
	row.Col["累計至上月攤銷天數"] = orderDaysUntilLastMonth(o)
	row.Col["當月攤銷天數"] = _orderDaysThisMonth
	row.Col["累計攤銷天數"] = orderDaysUntilThisMonth(o)
	row.Col["未攤銷天數"] = orderDaysInFuture(o)

	// count OrderDaysThisMonth only when the price > 0
	if o.Price > 0 {
		row.OrderDaysThisMonth = _orderDaysThisMonth
	}

	// price (money)
	_priceThisMonth := priceThisMonth(o, o.PriceNoTax)
	_priceInFuture := priceInFuture(o, o.PriceNoTax)
	row.Col["累計至上月已實現收入"] = priceUntilLastMonth(o, o.PriceNoTax)
	row.Col["當月已實現收入"] = _priceThisMonth
	row.Col["累計已實現收入"] = priceUntilThisMonth(o, o.PriceNoTax)
	row.Col["未實現收入"] = _priceInFuture

	row.PriceThisMonth = _priceThisMonth
	row.PriceInFuture = _priceInFuture

	// fee
	_feeThisMonth := priceThisMonth(o, o.Fee)
	_feeInFuture := priceInFuture(o, o.Fee)
	row.Col["手續費金額"] = o.Fee
	row.Col["累計至上月已實現手續費"] = priceUntilLastMonth(o, o.Fee)
	row.Col["當月已實現手續費"] = _feeThisMonth
	row.Col["累計已實現手續費"] = priceUntilThisMonth(o, o.Fee)
	row.Col["未實現手續費"] = _feeInFuture

	row.Fee = o.Fee
	row.FeeThisMonth = _feeThisMonth
	row.FeeInFuture = _feeInFuture

	// easypay
	if o.TradeNo.Valid {
		row.Col["智付通交易序號"] = o.TradeNo.String
	}

	// realized_at
	if o.RealizedAt.Valid {
		realizedDate := o.RealizedAt.Time.In(taipeiZone).Format(dateFormat)
		// credict card
		row.Col["信用卡授權日期"] = realizedDate
		// cvs_code
		row.Col["超商繳費日"] = realizedDate
		// iap
		row.Col["交易日期(Apple)"] = o.RealizedAt.Time.In(appleZone).Format(dateFormat)
		// other
		row.Col["用戶交易日期"] = realizedDate

		if o.PaymentType == "iap" {
			row.ReportMonthString = o.RealizedAt.Time.In(appleZone).Format("2006-01")
		} else {
			row.ReportMonthString = o.RealizedAt.Time.In(taipeiZone).Format("2006-01")
		}
	}

	// cvs_code
	if o.CVSCode.Valid {
		row.Col["超商代碼"] = o.CVSCode.String
	}

	// description
	if o.Description.Valid {
		// famiport
		row.Col["企業通路類別"] = o.Description.String
		//
		row.Col["方案"] = o.Description.String
	}

	if o.RedeemCode.Valid {
		row.Col["全家序號"] = o.RedeemCode.String
		row.Col["序號卡"] = o.RedeemCode.String
	}

	// tstar
	if o.TstarOrderID.Valid {
		row.Col["TSTAR訂單編號"] = o.TstarOrderID.String
	}
	if o.PaymentType == "tstar" {
		if o.UsedThisMonth == 1 {
			row.UsedThisMonth = 1
			row.Col["本月有無使用"] = 1
		} else {
			row.Col["本月有無使用"] = 0
		}
	}

	// bandott external_order_id
	if o.ExternalOrderID.Valid {
		row.Col["Bandott訂單編號"] = o.ExternalOrderID.String
	}

	if _product, ok := products[o.ProductID]; ok && _product.Category.Valid {
		row.Category = _product.Category.String
		row.Col["產品名稱"] = _product.ItemName
		row.Col["Category"] = _product.Category.String
	}

	return
}

func upload() (err error) {
	svc := s3.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})
	fp, err := os.Open(filepath.Join("/tmp", xlsxFileName))

	if err != nil {
		log.Println("[ERROR]", err)
		return err
	}

	fileInfo, _ := fp.Stat()
	size := fileInfo.Size()
	buffer := make([]byte, size)
	fp.Read(buffer)

	_, err = svc.PutObject(&s3.PutObjectInput{
		Bucket:        aws.String(reportBucket),
		Key:           aws.String(xlsxFileName),
		ACL:           aws.String("private"),
		Body:          bytes.NewReader(buffer),
		ContentLength: aws.Int64(size),
		ContentType:   aws.String("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
	})

	return err
}

// end of help func
////////////////////////////////////////////////////////////////////////////////

// //////////////////////////////////////////////////////////////////////////////
// sheet
func makeSheet(orders []Row, name string, cols []string) {

	var sheet *xlsx.Sheet
	var row *xlsx.Row
	// var cell *xlsx.Cell
	var err error
	var sum Row
	sheet, err = xlsxFile.AddSheet(name)
	if err != nil {
		fmt.Printf(err.Error())
	}
	row = sheet.AddRow()
	for _, i := range cols {
		cell := row.AddCell()
		cell.Value = i
	}
	if len(reTitle.FindStringSubmatch(name)) > 1 {
		sum.Name = reTitle.FindStringSubmatch(name)[1]
	} else {
		sum.Name = name
	}
	for _, order := range orders {
		row = sheet.AddRow()
		for _, col := range cols {
			cell := row.AddCell()
			cell.SetValue(order.Col[col])
		}

		if order.ReportMonthString == targetMonth {
			sum.PriceNoTax += order.PriceNoTax
			sum.Fee += order.Fee
			// category
			switch order.Category {
			case "Channel":
				sumChannel.PriceNoTax += order.PriceNoTax
				sumChannel.Fee += order.Fee
			case "Organic":
				sumOrganic.PriceNoTax += order.PriceNoTax
				sumOrganic.Fee += order.Fee
			case "MultiPlan":
				sumMultiPlan.PriceNoTax += order.PriceNoTax
				sumMultiPlan.Fee += order.Fee
			case "Other", "":
				sumOther.PriceNoTax += order.PriceNoTax
				sumOther.Fee += order.Fee
			}
		}
		sum.PriceThisMonth += order.PriceThisMonth
		sum.PriceInFuture += order.PriceInFuture
		sum.FeeThisMonth += order.FeeThisMonth
		sum.FeeInFuture += order.FeeInFuture
		sum.OrderDaysThisMonth += order.OrderDaysThisMonth

		// category
		switch order.Category {
		case "Channel":
			sumChannel.PriceThisMonth += order.PriceThisMonth
			sumChannel.PriceInFuture += order.PriceInFuture
			sumChannel.FeeThisMonth += order.FeeThisMonth
			sumChannel.FeeInFuture += order.FeeInFuture
			sumChannel.OrderDaysThisMonth += order.OrderDaysThisMonth
		case "Organic":
			sumOrganic.PriceThisMonth += order.PriceThisMonth
			sumOrganic.PriceInFuture += order.PriceInFuture
			sumOrganic.FeeThisMonth += order.FeeThisMonth
			sumOrganic.FeeInFuture += order.FeeInFuture
			sumOrganic.OrderDaysThisMonth += order.OrderDaysThisMonth
		case "MultiPlan":
			sumMultiPlan.PriceThisMonth += order.PriceThisMonth
			sumMultiPlan.PriceInFuture += order.PriceInFuture
			sumMultiPlan.FeeThisMonth += order.FeeThisMonth
			sumMultiPlan.FeeInFuture += order.FeeInFuture
			sumMultiPlan.OrderDaysThisMonth = (order.OrderDaysThisMonth * order.PaidSubscribers) + sumMultiPlan.OrderDaysThisMonth
		case "Other", "":
			sumOther.PriceThisMonth += order.PriceThisMonth
			sumOther.PriceInFuture += order.PriceInFuture
			sumOther.FeeThisMonth += order.FeeThisMonth
			sumOther.FeeInFuture += order.FeeInFuture
			sumOther.OrderDaysThisMonth += order.OrderDaysThisMonth
		}

	}

	sum.Persons = int(math.Round(float64(sum.OrderDaysThisMonth) / float64(daysThisMonth)))

	if sum.Persons > 0 {
		sum.ARPU = float64(sum.PriceThisMonth-sum.FeeThisMonth) / float64(sum.Persons)
		sum.ARPUnoFee = float64(sum.PriceThisMonth) / float64(sum.Persons)
	}

	summary = append(summary, sum)
}

func makeSummary() {

	var sheet *xlsx.Sheet
	var row *xlsx.Row
	var err error
	cols := []string{"", "當月交易金額", "當月已實現收入", "當月未實現收入",
		"當月手續費金額(未稅)", "當月已實現手續費", "當月未實現手續費", "人數 (當月攤銷天數/當月天數)", "ARPU", "ARPU (without fee)"}

	sheet, err = xlsxFile.AddSheet("彙整表")
	if err != nil {
		fmt.Printf(err.Error())
	}

	// help func for fill One Row
	fillOneRow := func(one *xlsx.Row, item Row) {
		var cell *xlsx.Cell
		cell = one.AddCell()
		cell.SetValue(item.Name)

		cell = one.AddCell()
		cell.SetValue(item.PriceNoTax)

		cell = one.AddCell()
		cell.SetValue(item.PriceThisMonth)

		cell = one.AddCell()
		cell.SetValue(item.PriceInFuture)

		cell = one.AddCell()
		cell.SetValue(item.Fee)

		cell = one.AddCell()
		cell.SetValue(item.FeeThisMonth)

		cell = one.AddCell()
		cell.SetValue(item.FeeInFuture)

		cell = one.AddCell()
		cell.SetValue(item.Persons)

		cell = one.AddCell()
		arpu, err := strconv.ParseFloat(fmt.Sprintf("%.1f", item.ARPU), 64)
		if err == nil {
			cell.SetValue(arpu)
		}

		cell = one.AddCell()
		arpunoFee, err := strconv.ParseFloat(fmt.Sprintf("%.1f", item.ARPUnoFee), 64)
		if err == nil {
			cell.SetValue(arpunoFee)
		}
	}

	row = sheet.AddRow()
	for _, i := range cols {
		cell := row.AddCell()
		cell.Value = i
	}
	var sumMonthIncome, sumMonthFee, sumPersons int
	var sumARPU, sumARPUnoFee float64
	for _, item := range summary {
		row = sheet.AddRow()
		fillOneRow(row, item)

		sumMonthIncome += item.PriceThisMonth
		sumMonthFee += item.FeeThisMonth
		sumPersons += item.Persons
	}

	if sumPersons > 0 {
		sumARPU = float64(sumMonthIncome-sumMonthFee) / float64(sumPersons)
		sumARPUnoFee = float64(sumMonthIncome) / float64(sumPersons)
		row = sheet.AddRow()
		var cell *xlsx.Cell
		row.AddCell()
		row.AddCell()
		row.AddCell()
		row.AddCell()
		row.AddCell()
		row.AddCell()
		row.AddCell()
		row.AddCell()
		cell = row.AddCell()
		arpu, err := strconv.ParseFloat(fmt.Sprintf("%.1f", sumARPU), 64)
		if err == nil {
			cell.SetValue(arpu)
		}

		cell = row.AddCell()
		arpunoFee, err := strconv.ParseFloat(fmt.Sprintf("%.1f", sumARPUnoFee), 64)
		if err == nil {
			cell.SetValue(arpunoFee)
		}
	}

	// category summary
	// Channel, Other, Organic, MultiPlan
	if daysThisMonth != 0 {
		sumChannel.Name = "Channel"
		sumChannel.Persons = int(math.Round(float64(sumChannel.OrderDaysThisMonth) / float64(daysThisMonth)))
		if sumChannel.Persons > 0 {
			sumChannel.ARPU = float64(sumChannel.PriceThisMonth-sumChannel.FeeThisMonth) / float64(sumChannel.Persons)
			sumChannel.ARPUnoFee = float64(sumChannel.PriceThisMonth) / float64(sumChannel.Persons)

		}

		row = sheet.AddRow()
		fillOneRow(row, sumChannel)

		sumOrganic.Name = "Organic"
		sumOrganic.Persons = int(math.Round(float64(sumOrganic.OrderDaysThisMonth) / float64(daysThisMonth)))
		if sumOrganic.Persons > 0 {
			sumOrganic.ARPU = float64(sumOrganic.PriceThisMonth-sumOrganic.FeeThisMonth) / float64(sumOrganic.Persons)
			sumOrganic.ARPUnoFee = float64(sumOrganic.PriceThisMonth) / float64(sumOrganic.Persons)

		}
		row = sheet.AddRow()
		fillOneRow(row, sumOrganic)

		sumMultiPlan.Name = "MultiPlan"
		sumMultiPlan.Persons = int(math.Round(float64(sumMultiPlan.OrderDaysThisMonth) / float64(daysThisMonth)))
		if sumMultiPlan.Persons > 0 {
			sumMultiPlan.ARPU = float64(sumMultiPlan.PriceThisMonth-sumMultiPlan.FeeThisMonth) / float64(sumMultiPlan.Persons)
			sumMultiPlan.ARPUnoFee = float64(sumMultiPlan.PriceThisMonth) / float64(sumMultiPlan.Persons)

		}
		row = sheet.AddRow()
		fillOneRow(row, sumMultiPlan)

		sumOther.Name = "Other"
		sumOther.Persons = int(math.Round(float64(sumOther.OrderDaysThisMonth) / float64(daysThisMonth)))
		if sumOther.Persons > 0 {
			sumOther.ARPU = float64(sumOther.PriceThisMonth-sumOther.FeeThisMonth) / float64(sumOther.Persons)
			sumOther.ARPUnoFee = float64(sumOther.PriceThisMonth) / float64(sumOther.Persons)

		}
		row = sheet.AddRow()
		fillOneRow(row, sumOther)
	}

}

// end of sheet
////////////////////////////////////////////////////////////////////////////////

func Handler() {
	//target month
	now := time.Now()
	if len(os.Args) > 1 {
		// format should be 2019-10
		targetMonth = os.Args[1]
		if !strings.Contains(targetMonth, "-") {
			log.Println("[ERROR] not correct year month format should be: 2019-10")
			return
		}

		targetSlice := strings.Split(targetMonth, "-")
		yearInt, _ := strconv.Atoi(targetSlice[0])
		monthInt, _ := strconv.Atoi(targetSlice[1])

		month = time.Date(yearInt, time.Month(monthInt), 1, 0, 0, 0, 0, time.Local)
		monthStart = time.Date(month.Year(), month.Month(), 1, 0, 0, 0, 0, taipeiZone)
		monthEnd = monthStart.AddDate(0, 1, -1)
		nextMonth = monthStart.AddDate(0, 1, 0)
		monthStart, monthEnd = monthStart.UTC(), monthEnd.UTC()

	} else {
		// getting previous month
		firstDayThisMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
		lastDayLastMonth := firstDayThisMonth.AddDate(0, 0, -1)

		month = time.Date(lastDayLastMonth.Year(), lastDayLastMonth.Month(), 1, 0, 0, 0, 0, time.Local)
		targetMonth = month.Format("2006-01")
		monthStart = time.Date(lastDayLastMonth.Year(), lastDayLastMonth.Month(), 1, 0, 0, 0, 0, taipeiZone)
		monthEnd = monthStart.AddDate(0, 1, -1)
		nextMonth = firstDayThisMonth.UTC()
		monthStart, monthEnd = monthStart.UTC(), monthEnd.UTC()
		log.Println("MonthStart", monthStart, "MonthEnd", monthEnd)
	}

	// excel
	xlsx.SetDefaultFont(11, "新細明體")
	xlsxFile = xlsx.NewFile()
	xlsxFileName = fmt.Sprintf("income_report_%s.xlsx", targetMonth)

	// `((start_date <= ${monthEnd} AND end_date > ${monthStart}) OR (realized_at >= ${monthStart} AND realized_at < ${monthEnd.plusDays(1)}))`,
	inTargetMonth = fmt.Sprintf(`((start_date <= '%s' AND end_date > '%s') OR (realized_at >= '%s' AND realized_at < '%s'))`,
		monthEnd.Format(dateFormatYMD), monthStart.Format(dateFormatYMD), monthStart.Format(dateFormatYMD), monthEnd.AddDate(0, 0, 1).Format(dateFormatYMD))

	log.Println(month, nextMonth)
	daysThisMonth = time.Date(month.Year(), month.Month()+1, 0, 0, 0, 0, 0, time.UTC).Day()
	printHead("Start to process income report from", monthStart, "to", monthEnd)

	commonColumns := []string{"啟用日期", "到期日期", "上月最後一天日期", "當月最後一天日期",
		"總攤銷天數", "累計至上月攤銷天數", "當月攤銷天數", "累計攤銷天數", "未攤銷天數",
		"累計至上月已實現收入", "當月已實現收入", "累計已實現收入", "未實現收入",
		"手續費金額", "累計至上月已實現手續費", "當月已實現手續費", "累計已實現手續費", "未實現手續費", "Category",
	}

	// coupon
	{
		columnDesc := "企業通路類別"
		columnRedeem := "序號卡"
		cols := append([]string{columnUserID, columnOrderID, columnDesc, columnRedeem, columnPriceNoTax}, commonColumns...)
		var orders []Order
		rows := []Row{}
		log.Println(cols)
		allOrders, _ := getOrders([]string{"ok"}, "coupon")
		for _, o := range allOrders {
			if o.Description.Valid {
				if !strings.Contains(o.Description.String, "全家 FamiPort 販售序號代碼") {
					o.Fee = 0
					orders = append(orders, o)
				}
			}
		}
		log.Println(len(orders))
		for _, o := range orders {
			rows = append(rows, fillRow(o))
		}
		makeSheet(rows, "收入明細(企業通路)", cols)
	}

	// credit card
	{
		columnRelaizedAt := "信用卡授權日期"
		columnItemName := "產品名稱"
		cols := append([]string{columnUserID, columnItemName, columnOrderID, columnTradeNo, columnPriceNoTax, columnRelaizedAt}, commonColumns...)

		var orders []Order
		rows := []Row{}
		log.Println(cols)
		orders, _ = getCreditCardNonZeroPrice([]string{"ok", "error"}, "credit_card")
		for idx, o := range orders {
			if _product, ok := products[o.ProductID]; ok {
				orders[idx].Fee = int(_product.Fee)
			}
		}
		log.Println(len(orders))
		for _, o := range orders {
			rows = append(rows, fillRow(o))
		}
		makeSheet(rows, "收入明細(信用卡)", cols)
	}

	// cvs_code
	{
		columnRelaizedAt := "超商繳費日"
		columnCVS := "超商代碼"
		cols := append([]string{columnUserID, columnOrderID, columnTradeNo, columnCVS, columnPriceNoTax, columnRelaizedAt}, commonColumns...)
		var orders []Order
		rows := []Row{}
		log.Println(cols)
		orders, _ = getOrdersNonZeroPrice([]string{"ok", "error"}, "cvs_code")
		for idx, _ := range orders {
			orders[idx].Fee = 23
		}
		log.Println(len(orders))
		for _, o := range orders {
			rows = append(rows, fillRow(o))
		}
		makeSheet(rows, "收入明細(超商代碼)", cols)
	}

	// iap
	{
		columnRelaizedAt := "交易日期(Apple)"
		cols := append([]string{columnUserID, columnOrderID, columnPriceNoTax, columnRelaizedAt}, commonColumns...)
		var orders []Order
		rows := []Row{}
		log.Println(cols)
		orders, _ = getOrdersNonZeroPrice([]string{"ok", "error"}, "iap")
		for idx, o := range orders {
			if _product, ok := products[o.ProductID]; ok {
				orders[idx].Fee = int(_product.Fee)
			}
		}
		log.Println(len(orders))
		for _, o := range orders {
			rows = append(rows, fillRow(o))
		}
		makeSheet(rows, "收入明細(IAP)", cols)
	}

	// famiport
	{
		columnDesc := "方案"
		columnRedeem := "全家序號"
		cols := append([]string{columnUserID, columnOrderID, columnDesc, columnRedeem, columnPriceNoTax}, commonColumns...)
		var orders []Order
		rows := []Row{}
		log.Println(cols)
		allOrders, _ := getOrders([]string{"ok"}, "coupon")
		for _, o := range allOrders {
			if o.Description.Valid {
				if strings.Contains(o.Description.String, "全家 FamiPort 販售序號代碼") {
					switch {
					case strings.Contains(o.Description.String, "全家 FamiPort 販售序號代碼（1 個月）"):
						o.Fee = 21
					case strings.Contains(o.Description.String, "全家 FamiPort 販售序號代碼（3 個月 + 7 天）"):
						o.Fee = 64
					case strings.Contains(o.Description.String, "全家 FamiPort 販售序號代碼（6 個月 + 14 天）"):
						o.Fee = 128

					case strings.Contains(o.Description.String, "全家 FamiPort 販售序號代碼（12 個月 + 1 個月）"):
						o.Fee = 255
					}
					orders = append(orders, o)
				}
			}
		}
		log.Println(len(orders))
		for _, o := range orders {
			rows = append(rows, fillRow(o))
		}
		makeSheet(rows, "收入明細(全家序號)", cols)
	}

	// sonet
	{
		columnRelaizedAt := "用戶交易日期"
		cols := append([]string{columnUserID, columnOrderID, columnPriceNoTax, columnRelaizedAt}, commonColumns...)
		var orders []Order
		rows := []Row{}
		log.Println(cols)
		orders, _ = getSonetOrders([]string{"ok", "error"}, "telecom")
		for idx, _ := range orders {
			orders[idx].Fee = 18
		}
		log.Println(len(orders))
		for _, o := range orders {
			rows = append(rows, fillRow(o))
		}
		makeSheet(rows, "收入明細(so-net)", cols)
	}

	// tstar
	{
		columnRelaizedAt := "用戶交易日期"
		columnTstarOrderID := "TSTAR訂單編號"
		cols := append([]string{columnUserID, columnOrderID, columnTstarOrderID, columnPriceNoTax, columnRelaizedAt}, commonColumns...)
		// cols = append(cols, "本月有無使用")
		var orders []Order
		// if the user had signed in KKTV this month is 1 else 0
		usedUID := make(map[string]int)
		rows := []Row{}
		log.Println(cols)
		orders, _ = getTstarOrders([]string{"ok", "error"}, "tstar")
		for idx, o := range orders {
			orders[idx].Fee = 17
			usedUID[o.UserID] = 0 // default 0
		}
		//
		// checkUsed(usedUID)
		log.Println(len(orders))
		for _, o := range orders {
			// o.UsedThisMonth = usedUID[o.UserID]
			rows = append(rows, fillRow(o))
		}

		makeSheet(rows, "收入明細(tstar)", cols)
	}

	// mod
	{
		columnRelaizedAt := "用戶交易日期"
		cols := append([]string{columnUserID, columnOrderID, columnPriceNoTax, columnRelaizedAt}, commonColumns...)

		var orders []Order
		rows := []Row{}
		log.Println(cols)
		orders, _ = getOrdersNonZeroPrice([]string{"ok", "error"}, "mod")
		// for idx, _ := range orders {
		// orders[idx].Fee = 0
		// }
		log.Println(len(orders))
		for _, o := range orders {
			rows = append(rows, fillRow(o))
		}
		makeSheet(rows, "收入明細(MOD)", cols)
	}

	// iab
	{
		columnRelaizedAt := "用戶交易日期"
		cols := append([]string{columnUserID, columnOrderID, columnPriceNoTax, columnRelaizedAt}, commonColumns...)

		var orders []Order
		rows := []Row{}
		log.Println(cols)
		orders, _ = getOrdersNonZeroPrice([]string{"ok", "error"}, "iab")
		log.Println(len(orders))
		for _, o := range orders {
			rows = append(rows, fillRow(o))
		}
		makeSheet(rows, "收入明細(IAB)", cols)
	}

	// bandott
	{
		columnRelaizedAt := "用戶交易日期"
		columnBandottOrderID := "Bandott訂單編號"
		cols := append([]string{columnUserID, columnOrderID, columnPriceNoTax, columnRelaizedAt, columnBandottOrderID}, commonColumns...)

		var orders []Order
		rows := []Row{}
		log.Println(cols)
		orders, _ = getOrdersNonZeroPrice([]string{"ok", "error"}, "bandott")
		log.Println(len(orders))
		for _, o := range orders {
			rows = append(rows, fillRow(o))
		}
		makeSheet(rows, "收入明細(Bandott)", cols)
	}

	makeSummary()

	// write xlsx
	var err error
	err = xlsxFile.Save(filepath.Join("/tmp", xlsxFileName))
	if err != nil {
		fmt.Printf(err.Error())
		return
	}

	upload()

}

func main() {
	// AWS lambda
	lambda.Start(Handler)
}
