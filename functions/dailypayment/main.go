package main

import (
	"bufio"
	"bytes"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/slack-go/slack"
	"gopkg.in/guregu/null.v3"
)

var (
	csvFileName string
	end         time.Time

	slackAPI     = slack.New("*********************************************************")
	slackChannel = "#kktv-report-test"
	s3Bucket     = "kktv-prod-report/dailypayment_test/"

	memberships = map[string]string{
		"general":   "KKTV VIP",
		"prime":     "KKBOX Prime",
		"kkbox":     "KKBOX Billing",
		"pr":        "Public Relation",
		"classmate": "Classmate",
		"test":      "Test",
	}

	sqlmap = map[string]string{

		"total_user_count": `SELECT COUNT(1)::integer AS count FROM users;`,

		"free_trial_user_count": `SELECT COUNT(1)::integer AS count FROM users WHERE role = 'freetrial';`,

		"expired_user_count": `SELECT COUNT(1)::integer AS count FROM users WHERE role = 'expired';`,

		"premium_user_count": `SELECT type, COUNT(id)::integer AS count FROM users WHERE role = 'premium' GROUP BY type ORDER BY count DESC;`,

		"paid_user_count": `
select sum(paid_data.paid_user) from (
	select o.user_id as user_id,
		case when jsonb_array_length(u.media_source->'family') is null
		then 1
		else jsonb_array_length(u.media_source->'family')
		end as paid_user ,
		(SELECT COUNT(1) FROM server_notifications s WHERE s.user_id = o.user_id AND (DATE(s.grace_period_expires_date)=DATE(o.order_date) OR (s.order_id=o.id AND s.is_in_billing_retry_period=true))) AS in_grace,
		o.status as status
	from orders o
		left join users u on o.user_id = u.id
		left join products p on o.product_id = p.id 
		where (o.status = 'ok' or o.status is null)
			AND o.user_id IN (SELECT id FROM users WHERE role = 'premium' AND type='general')
			and p.category in ('Channel', 'Organic', 'MultiPlan')
			and date(now()) between date(o.start_date) and date(o.end_date)
	) as paid_data
	where (paid_data.in_grace > 0 or paid_data.status = 'ok');`,

		"user_count_organic_channel": `
		SELECT
			category, COUNT(1)
		FROM products p
		JOIN orders o ON o.product_id = p.id
		WHERE
			( DATE(NOW()) BETWEEN DATE(o.start_date) AND DATE(o.end_date) )
			AND o.user_id IN (SELECT id FROM users WHERE role = 'premium' AND type='general')
			AND o.status = 'ok'
		GROUP BY category ;`,

		"user_count_category_grace": `
		select category, count(user_id) from (
			select distinct p.category ,o.user_id from orders o
			left join server_notifications s on s.user_id = o.user_id
			left join products p on o.product_id = p.id
			where (
				date(s.grace_period_expires_date) = date(o.order_date) or
				(s.order_id = o.id and s.is_in_billing_retry_period = true)
			) and o.order_date > date(NOW())
			and o.status is null
		) as grace_data group by category ;`,

		"report": `SELECT
					date, category, payment_type, name, item_name, period_name, genera, item_unit, firstbuy, discount, discount_price, price, SUM(paid) AS paid, SUM(paid_free) AS paid_free
				FROM
				(
				SELECT
					DATE(NOW()) AS date,
					category,
					p.payment_type,
					p.name,
					p.item_name,
					CASE WHEN p.auto_renew = true THEN '訂閱' ELSE '計次' END AS period_name,
					CASE WHEN p.bundle->'url_name' IS NOT NULL
							THEN 'campaign'
							ELSE 'product' END AS genera,
					p.item_unit,
					CASE WHEN p.as_subscribe = true AND EXTRACT(epoch FROM p.free_duration) > 0
							THEN p.duration
							END AS firstbuy ,
					CASE WHEN EXTRACT(epoch FROM p.discount_duration) > 0
							THEN p.discount_duration
							END AS discount,
					p.discount_price::NUMERIC::int AS discount_price,
					p.price::NUMERIC::int,
					CASE WHEN o.price::NUMERIC::int > 0 THEN 1 ELSE 0 END AS paid,
					CASE WHEN o.price::NUMERIC::int = 0 THEN 1 ELSE 0 END AS paid_free
				FROM products p
					JOIN orders o ON o.product_id = p.id
				WHERE
					( DATE(NOW()) BETWEEN DATE(o.start_date) AND DATE(o.end_date) )
					AND o.user_id IN (SELECT id FROM users WHERE role = 'premium' and type = 'general')
					AND o.status = 'ok'
				) AS sdata
				GROUP BY
				date, category, payment_type, name, item_name, period_name, genera, item_unit, firstbuy, discount, discount_price, price`,
		"grace_report": `
			select name, count(user_id) from (
				select distinct p."name" ,o.user_id from orders o
				left join server_notifications s on s.user_id = o.user_id
				left join products p on o.product_id = p.id
				where (
					date(s.grace_period_expires_date) = date(o.order_date) or
					(s.order_id = o.id and s.is_in_billing_retry_period = true)
				) and o.order_date > date(NOW())
				and o.status is null
			) as grace_product group by name;`,
	}
)

// UserTypeCount struct for counters
type UserTypeCount struct {
	Type  string `db:"type"`
	Count int64  `db:"count"`
}

// UserCateCount struct for counters
type UserCateCount struct {
	Category string `db:"category"`
	Count    int64  `db:"count"`
}

// Report struct for report data
type Report struct {
	Date          string      `db:"date"`
	Category      null.String `db:"category"`
	PaymentType   string      `db:"payment_type"`
	Name          string      `db:"name"`
	ItemName      string      `db:"item_name"`
	PeriodName    string      `db:"period_name"`
	Genera        string      `db:"genera"`
	ItemUnit      string      `db:"item_unit"`
	Firstbuy      null.String `db:"firstbuy"`
	Discount      null.String `db:"discount"`
	DiscountPrice string      `db:"discount_price"`
	Price         string      `db:"price"`
	Paid          string      `db:"paid"`
	PaidFree      string      `db:"paid_free"`
}

type GraceReport struct {
	Name  string `db:"name"`
	Count int64  `db:"count"`
}

// CountFormat https://play.golang.org/p/TjtIWs4E0ki
func CountFormat(n int64) string {
	in := strconv.FormatInt(n, 10)
	numOfDigits := len(in)
	if n < 0 {
		numOfDigits-- // First character is the - sign (not a digit)
	}
	numOfCommas := (numOfDigits - 1) / 3

	out := make([]byte, len(in)+numOfCommas)
	if n < 0 {
		in, out[0] = in[1:], '-'
	}

	for i, j, k := len(in)-1, len(out)-1, 0; ; i, j = i-1, j-1 {
		out[j] = in[i]
		if i == 0 {
			return string(out)
		}
		if k++; k == 3 {
			j, k = j-1, 0
			out[j] = ','
		}
	}
}

func init() {
	log.Println("Lambda kktv-api-v3 DailyPaymentReport init")

	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	kkapp.ContextInit()

}

// Handler function of process
func Handler() {

	var wg sync.WaitGroup
	var totalUserCount, freeTrialUserCount, expiredUserCount, paidSubscribersCount int64
	var premiumUserCount []UserTypeCount
	var categoryUserCount, graceCategoryUserCount []UserCateCount
	var report []Report
	var graceReport []GraceReport
	var membership, content []string

	db := kkapp.App.DbUser.Slave()

	if kkapp.App.Env == "prod" {
		slackChannel = "#kktv-report"
		s3Bucket = "kktv-prod-report/dailypayment/"
	}

	wg.Add(2)

	go func() {
		var err error
		err = db.Get(&totalUserCount, sqlmap["total_user_count"])
		if err != nil {
			log.Println("[ERROR]", err)
		}

		err = db.Get(&freeTrialUserCount, sqlmap["free_trial_user_count"])
		if err != nil {
			log.Println("[ERROR]", err)
		}

		err = db.Get(&expiredUserCount, sqlmap["expired_user_count"])
		if err != nil {
			log.Println("[ERROR]", err)
		}

		err = db.Select(&premiumUserCount, sqlmap["premium_user_count"])
		if err != nil {
			log.Println("[ERROR]", err)
		}

		err = db.Select(&categoryUserCount, sqlmap["user_count_organic_channel"])
		if err != nil {
			log.Println("[ERROR]", err)
		}

		err = db.Select(&graceCategoryUserCount, sqlmap["user_count_category_grace"])
		if err != nil {
			log.Println("[ERROR]", err)
		}

		err = db.Get(&paidSubscribersCount, sqlmap["paid_user_count"])
		if err != nil {
			log.Println("[ERROR]", err)
		}

		wg.Done()
	}()

	go func() {
		err := db.Select(&report, sqlmap["report"])
		if err != nil {
			log.Println("[ERROR]", err)
		}

		err = db.Select(&graceReport, sqlmap["grace_report"])
		if err != nil {
			log.Println("[ERROR]", err)
		}
		wg.Done()
	}()

	wg.Wait()

	membershipTemplate := "> %s `%s`"
	free := fmt.Sprintf(membershipTemplate, "Free Trial", CountFormat(freeTrialUserCount))
	expired := fmt.Sprintf(membershipTemplate, "Expired", CountFormat(expiredUserCount))
	total := fmt.Sprintf(membershipTemplate, "Total", CountFormat(totalUserCount))

	// membership
	membership = append(membership, "User Membership")
	membership = append(membership, "Membership Count")
	membership = append(membership, free)
	membership = append(membership, expired)
	paidSubscribersStr := fmt.Sprintf(membershipTemplate, "KKTV Paid Subscribers", CountFormat(paidSubscribersCount))
	membership = append(membership, paidSubscribersStr)
	for _, item := range premiumUserCount {
		if match, ok := memberships[item.Type]; ok {
			tmpstr := fmt.Sprintf(membershipTemplate, match, CountFormat(item.Count))
			membership = append(membership, tmpstr)
		}
	}
	membership = append(membership, total)
	membership = append(membership, "")
	membership = append(membership, "Organic / Channel Count")
	organicTemplate := "> %s %s `%s` (%s), %s `%s` (%s)"
	for _, item := range categoryUserCount {
		var graceCount int64
		for _, graceItem := range graceCategoryUserCount {
			if item.Category == graceItem.Category {
				graceCount = graceItem.Count
				break
			}
			graceCount = 0
		}

		tmpstr := fmt.Sprintf(
			organicTemplate,
			item.Category,
			"已實現組數",
			CountFormat(item.Count),
			diffCategoryCount(item.Count, item.Category, false),
			"寬限期組數",
			CountFormat(graceCount),
			diffCategoryCount(graceCount, item.Category, true))
		membership = append(membership, tmpstr)
	}

	// count by product
	header := "Date,類別,付費管道,購買方式,訂購週期,方案類型,方案代碼,方案名稱,首購0元,優惠折扣,方案金額,付費總人數,免費中人數,寬限期人數"
	template := "%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%d"
	content = append(content, "")
	content = append(content, "")
	content = append(content, "Count by Product")
	content = append(content, header)

	for _, v := range report {
		var graceCount int64 = 0
		discount := ""
		if v.Discount.String != "" {
			discount = fmt.Sprintf("%s-%s", v.Discount.String, v.DiscountPrice)
		}

		for _, r := range graceReport {
			if r.Name == v.Name {
				graceCount = r.Count
				break
			}
			graceCount = 0
		}

		datastr := strings.Replace(v.Date, "T00:00:00Z", "", -1)
		itemName := strings.Replace(v.ItemName, ",", "，", -1)
		line := fmt.Sprintf(template, datastr, v.Category.String, v.PaymentType, v.PeriodName,
			v.ItemUnit, v.Genera, v.Name, itemName, v.Firstbuy.String, discount, v.Price, v.Paid, v.PaidFree, graceCount)
		content = append(content, line)

	}
	cacheCategoryCount(categoryUserCount, false)
	cacheCategoryCount(graceCategoryUserCount, true)
	sendSlackMessage(membership)
	presignUrl := uploadFiletoS3(content)
	if presignUrl != nil {
		formatUrlString := []string{
			fmt.Sprintf("<%s|Download>", presignUrl[0]),
		}
		sendSlackMessage(formatUrlString)
	}
}

func uploadFiletoS3(content []string) (presignUrl []string) {

	now := time.Now()
	zone, err := time.LoadLocation("Asia/Taipei")
	if err != nil {
		log.Println("Load timezone error:", err)
	}

	end = now.In(zone)
	csvFileName = fmt.Sprintf("/tmp/report_payment_%d-%02d-%02d_%d.csv", end.Year(), end.Month(), end.Day(), end.Unix())

	file, err := os.Create(csvFileName)
	if err != nil {
		log.Println("failed creating file: ", err)
	}

	writer := bufio.NewWriter(file)

	for _, line := range content {
		// write line to file
		fmt.Fprintln(writer, line)
	}

	writer.Flush()
	file.Close()
	tempFileName := file.Name()

	log.Println("Preparing upload file to S3...", time.Now())
	log.Println(tempFileName)
	s3file, err := os.Open(tempFileName)
	if err != nil {
		log.Println("[ERROR] Open file failed", err)
	}
	sess, _ := session.NewSessionWithOptions(session.Options{Config: aws.Config{Region: aws.String("ap-northeast-1")}})
	uploader := s3manager.NewUploader(sess)
	key := filepath.Base(s3file.Name())

	_, err = uploader.Upload(&s3manager.UploadInput{
		Bucket: aws.String(s3Bucket),
		Key:    aws.String(key),
		Body:   s3file,
	})

	if err != nil {
		log.Println("!!! Upload File to S3 Failed !!!", err)
	}
	log.Println("Upload file to S3 finish. ", time.Now())

	svc := s3.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})
	req, _ := svc.GetObjectRequest(&s3.GetObjectInput{
		Bucket: aws.String(s3Bucket),
		Key:    aws.String(key),
	})
	url, err := req.Presign(1440 * time.Minute)
	if err != nil {
		log.Println("Get presign url failed,", err)
		presignUrl = nil
		return
	}
	presignUrl = append(presignUrl, url)
	log.Println("Presign url:", url)
	return
}

func sendSlackMessage(msgBlock []string) {
	var buffer bytes.Buffer
	for _, item := range msgBlock {
		buffer.WriteString(fmt.Sprintf("%s\n", item))
	}

	slackAPI.PostMessage(slackChannel, slack.MsgOptionText(buffer.String(), false))
	log.Printf("Message successfully sent to channel %s at %s", slackChannel, time.Now())
}

func cacheCategoryCount(categoryUserCount []UserCateCount, isGrace bool) {
	var latestKey string = "user-category-latest"
	var keyTmp string = "user-category"

	if isGrace {
		latestKey = "user-grace-category-latest"
		keyTmp = "user-grace-category"
	}

	today := time.Now().Format("2006-01-02")
	key := fmt.Sprintf("%s-%s", keyTmp, today)
	redisPool := kkapp.App.RedisUser.Master()
	redisPool.Cmd("SET", latestKey, key)
	redisPool.Cmd("expire", key, 604800)

	for _, c := range categoryUserCount {
		redisPool.Cmd("HMSET", key, c.Category, c.Count)
	}
}

func getLatestCategoryCount(isGrace bool) map[string]string {
	var latestKey string
	if isGrace {
		latestKey = "user-grace-category-latest"
	} else {
		latestKey = "user-category-latest"
	}

	redisPool := kkapp.App.RedisUser.Slave()
	key, err := redisPool.Cmd("GET", latestKey).Str()
	if err != nil {
		log.Println("[ERROR]", err)
		return nil
	}

	latestCategoryCount, err := redisPool.Cmd("HGETALL", key).Map()

	if err != nil {
		log.Println("[ERROR]", err)
		return nil
	}
	return latestCategoryCount
}

func diffCategoryCount(todayCount int64, category string, isGrace bool) string {
	latestCategoryCount := getLatestCategoryCount(isGrace)
	latestCountString := latestCategoryCount[category]
	latestCount, _ := strconv.ParseInt(latestCountString, 10, 64)
	diff := todayCount - latestCount
	if diff > 0 {
		return fmt.Sprintf("+%s", strconv.FormatInt(diff, 10))
	}
	return fmt.Sprintf("%s", strconv.FormatInt(diff, 10))
}

func main() {
	// AWS lambda
	lambda.Start(Handler)
}
