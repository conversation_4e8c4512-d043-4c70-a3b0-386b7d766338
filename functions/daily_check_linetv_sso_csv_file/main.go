package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"

	awslambda "github.com/aws/aws-sdk-go-v2/service/lambda"
	"github.com/aws/aws-sdk-go-v2/service/lambda/types"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

type SlackPayload struct {
	Message     string        `json:"message"`
	Channel     string        `json:"channel"`
	Username    string        `json:"username"`
	Attachments []interface{} `json:"attachments"`
}

func Handler(ctx context.Context) (interface{}, error) {
	appConfig := LoadConfig()
	today := time.Now().In(datetimer.LocationTaipei).Format("2006-01-02")

	cfg, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %w", err)
	}
	// S3 必須 us-east-1, LineTV Bucket 在 us-east-1
	s3Cfg := cfg
	s3Cfg.Region = "us-east-1"
	s3Client := s3.NewFromConfig(s3Cfg)

	listInput := &s3.ListObjectsV2Input{
		Bucket: aws.String(appConfig.Bucket),
		Prefix: aws.String(appConfig.Prefix),
	}
	found := false
	paginator := s3.NewListObjectsV2Paginator(s3Client, listInput)
	for paginator.HasMorePages() {
		page, err := paginator.NextPage(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to list S3 objects: %w", err)
		}
		log.Debug("S3 這一頁物件數量: ").Int("count", len(page.Contents)).Send()
		for _, obj := range page.Contents {
			if strings.Contains(*obj.Key, today) {
				found = true
				break
			}
		}
		if found {
			break
		}
	}

	if !found {
		log.Info("⚠️ LineTV SSO CSV File 缺少今日 %s 的檔案").Send()
		lambdaClient := awslambda.NewFromConfig(cfg)
		payload := SlackPayload{
			Message:  fmt.Sprintf("⚠️ LineTV SSO CSV File 缺少今日 %s 的檔案", today),
			Channel:  appConfig.Channel,
			Username: "kktv-slacker",
			Attachments: []interface{}{
				map[string]interface{}{
					"fallback": fmt.Sprintf("缺少 LineTV SSO CSV File %s.csv 檔案", today),
					"pretext":  "LineTV SSO CSV File S3 檔案檢查通知",
					"title":    fmt.Sprintf("缺少檔案 LineTV SSO CSV File %s.csv", today),
					// "title_link": "https://console.aws.amazon.com/s3/buckets/kktv-prod-report/retention_users/",
					"text":  "請確認自動化流程是否成功產出檔案。",
					"color": "#FF0000",
				},
			},
		}
		payloadBytes, _ := json.Marshal(payload)
		_, err := lambdaClient.Invoke(ctx, &awslambda.InvokeInput{
			FunctionName:   aws.String("kktv-slacker"),
			InvocationType: types.InvocationTypeEvent,
			Payload:        payloadBytes,
			Qualifier:      aws.String("test"),
		})
		if err != nil {
			return nil, fmt.Errorf("failed to invoke kktv-slacker: %w", err)
		}
	} else {
		log.Info("有發現今日 LineTV SSO CSV 檔案").Send()
	}

	return map[string]interface{}{
		"statusCode": 200,
		"body":       "Check complete",
	}, nil
}

func main() {

	log.Info("LAMBDA_TASK_ROOT: " + os.Getenv("LAMBDA_TASK_ROOT")).Send()
	if os.Getenv("LAMBDA_TASK_ROOT") != "" {
		lambda.Start(Handler)
		return
	}

	// 本地測試
	result, err := Handler(context.Background())
	if err != nil {
		fmt.Println("[本地測試] 執行失敗:", err)
	} else {
		fmt.Printf("[本地測試] 執行成功: %+v\n", result)
	}

}
