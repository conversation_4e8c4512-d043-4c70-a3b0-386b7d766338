{"description": "Check that today's SSO CSV file is in the LineTV S3 bucket. Alert kktv-{env}-log channel if it's missing.", "timeout": 30, "memory": 1024, "runtime": "provided.al2023", "handler": "bootstrap", "release": "0.0.1", "hooks": {"build": "ldflags=\"-X github.com/KKTV/kktv-api-v3/functions/daily_check_linetv_sso_csv_file/main.version=$(git describe --tags).$(date -u +%Y%m%d.%H%M%S)\" && GOOS=linux GOARCH=amd64 go build -o bootstrap -ldflags \"$ldflags\" *.go"}}