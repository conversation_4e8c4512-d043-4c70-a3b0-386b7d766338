package main

import (
	"log"

	"os"

	"github.com/caarlos0/env/v7"
	"github.com/joho/godotenv"
)

type AppConfig struct {
	Bucket  string `env:"BUCKET_NAME"`
	Prefix  string `env:"BUCKET_PREFIX"`
	Channel string `env:"SLACK_CHANNEL"`
}

func LoadConfig() AppConfig {
	_ = godotenv.Load()

	cfg := AppConfig{
		Bucket:  "kktv-membership-staging",
		Prefix:  "",
		Channel: "#kktv-log-test",
	}

	if os.Getenv("ENV") == "prod" {
		cfg.Bucket = "kktv-membership"
		cfg.Channel = "#kktv-log-prod"
	}

	// 解析環境變數覆蓋預設值
	if err := env.Parse(&cfg); err != nil {
		log.Fatalf("failed to parse env: %v", err)
	}
	return cfg
}
