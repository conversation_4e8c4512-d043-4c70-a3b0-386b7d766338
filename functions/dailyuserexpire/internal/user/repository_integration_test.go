package user

import (
	_ "embed"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/suite"
)

type RepositoryTestSuite struct {
	suite.Suite

	db *sqlx.DB
	tx *sqlx.Tx

	repo Repository
}

func TestRepositoryTestSuite(t *testing.T) {
	suite.Run(t, new(RepositoryTestSuite))
}

func (suite *RepositoryTestSuite) SetupTest() {
	// new a transaction for each test case
	tx, err := suite.db.Beginx()
	if err != nil {
		suite.Fail("fail to begin transaction", err)
	}
	suite.tx = tx

	suite.repo = &repository{
		db: suite.tx,
	}
}

func (suite *RepositoryTestSuite) TearDownTest() {
	// rollback the transaction after each test
	if suite.tx != nil {
		defer func() {
			if err := suite.tx.Rollback(); err != nil {
				suite.Fail("fail to rollback", err)
			}
		}()
	}
}

func (suite *RepositoryTestSuite) SetupSuite() {
	t := suite.T()
	dbDSN := "postgres://foo:bar@localhost:5432/kktv_users?sslmode=disable"
	db := datastore.NewDBPool([]string{dbDSN})
	if db == nil {
		t.Fatalf("fail to build DB connection for %s", dbDSN)
	}
	suite.db = db.Master().Unsafe()
}

//go:embed testdata/repository/BulkExpire.sql
var data_BulkExpireUsers string

func (suite *RepositoryTestSuite) TestBulkExpireUsers() {
	require := suite.Require()
	testcases := []struct {
		name          string
		now           time.Time
		traceBackFrom time.Time
		limit         int
		thenAssert    func(expiringUsers []*ExpiringUser, err error)
	}{
		{
			name:          "bulk update users with past `expired_at` within the `traceBackFrom`",
			now:           time.Date(2020, 4, 29, 0, 0, 0, 0, time.UTC),
			traceBackFrom: time.Date(2020, 1, 1, 0, 0, 0, 0, datetimer.LocationTaipei),
			limit:         10,
			thenAssert: func(expiringUsers []*ExpiringUser, err error) {
				require.NoError(err)
				require.Len(expiringUsers, 2)

				users := suite.loadAllUsers()
				for i := 0; i < 2; i++ { // assert the first two users are expired
					suite.assertUserIsExpired(users[i])
				}
				for i := 2; i < len(users); i++ {
					suite.assertUserNotExpired(users[i])
				}
			},
		},
		{
			name:          "limit is less than the number of users to be expired",
			now:           time.Date(2020, 4, 29, 0, 0, 0, 0, time.UTC),
			traceBackFrom: time.Date(2020, 1, 1, 0, 0, 0, 0, datetimer.LocationTaipei),
			limit:         1,
			thenAssert: func(expiringUsers []*ExpiringUser, err error) {
				require.NoError(err)
				require.Len(expiringUsers, 1)

				users := suite.loadAllUsers()
				suite.assertUserIsExpired(users[0]) // assert that only the first user is expired
				for i := 1; i < len(users); i++ {
					suite.assertUserNotExpired(users[i])
				}
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			suite.givenUsersAndOrdersAndProducts(data_BulkExpireUsers)
			expiringUsers, err := suite.repo.BulkExpireUsers(tc.now, tc.traceBackFrom, tc.limit)
			tc.thenAssert(expiringUsers, err)
		})
	}

}

func (suite *RepositoryTestSuite) loadAllUsers() []*dbuser.User {
	require := suite.Require()
	users := make([]*dbuser.User, 0)
	require.NoError(suite.tx.Select(&users, `SELECT * FROM users ORDER BY id ASC`))
	require.Len(users, 4)
	return users
}

func (suite *RepositoryTestSuite) assertUserNotExpired(u *dbuser.User) {
	require := suite.Require()
	require.NotEqual(dbuser.RoleExpired.String(), u.Role)
}

func (suite *RepositoryTestSuite) assertUserIsExpired(u *dbuser.User) {
	require := suite.Require()
	require.Equal(dbuser.RoleExpired.String(), u.Role)
	require.Equal(dbuser.TypeGeneral.String(), u.Type)
	require.Equal(false, u.AutoRenew)
	require.Equal(dbuser.Membership{{Role: dbuser.MemberRoleExpired}}, u.Membership)
}

// DeleteFromTable cleans the record in given tables
func (suite *RepositoryTestSuite) DeleteFromTable(tables ...string) {
	for _, table := range tables {
		if _, err := suite.tx.Exec("DELETE FROM " + table); err != nil {
			suite.Fail("fail to delete tables", err)
		}
	}
}

func (suite *RepositoryTestSuite) givenUsersAndOrdersAndProducts(testData string) {
	suite.DeleteFromTable("unsubscribe", "payment_info", "product_packages_to_products", "product_packages", "tokens", "orders", "products", "users")
	if _, err := suite.tx.Exec(testData); err != nil {
		suite.Fail("create fail", err)
	}
}
