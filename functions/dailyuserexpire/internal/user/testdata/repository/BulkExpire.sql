-- users > product, orders

INSERT INTO public.users (id, email, phone, avatar_url, name, birthday, gender, expired_at, created_at,
                          updated_at, "role", media_source, auto_renew, payment_info, "type", created_by,
                          cold_start_selected_titles, kkid_bound_at, origin_provider, kkid, revoked_at,
                          unsubscribed_edm_at, "password", email_verified_at, phone_verified_at)
VALUES ('kktv-api-unittest-0001', '<EMAIL>', NULL, NULL, NULL, NULL, NULL,
        '2020-04-29 00:00:00.000', '2016-11-16 23:20:56.172', '2016-11-21 00:08:55.276', 'premium',
        '{"family": ["a167686a-ae58-43f2-93a9-67f0e580efe1", "0420e345-df37-4c23-ad22-184d85c9fb72"], "appsflyer": {"idfa": "00000000-0000-0000-0000-000000000000", "bundleId": "com.kktv.ios.kktv"}}',
        false,
        NULL, 'general'::users_type, '', NULL, NULL, 'facebook'::users_origin_provider, NULL, NULL, NULL, NULL, NULL,
        NULL),
       ('kktv-api-unittest-0002', '<EMAIL>', NULL, NULL, NULL, NULL, NULL,
        '2020-04-28 00:00:00.000', '2016-11-16 23:20:56.172', '2016-11-21 00:08:55.276', 'premium', NULL, false, NULL,
        'general'::users_type, '',
        NULL, NULL, 'facebook'::users_origin_provider, NULL, '2020-11-16 23:20:56.172', NULL, NULL, NULL, NULL),
       ('kktv-api-unittest-0003', NULL, NULL, NULL, NULL, NULL, NULL,
        '2020-04-30 00:00:00.000', '2016-11-16 23:20:56.172', '2016-11-21 00:08:55.276', 'premium', NULL, false,
        NULL, 'general'::users_type, '', NULL, NULL, 'facebook'::users_origin_provider, NULL, NULL, NULL, NULL, NULL,
        NULL),
       ('kktv-api-unittest-0004', NULL, '**********', NULL, NULL, NULL, NULL,
        '2020-04-30 00:00:00.000', '2016-11-16 23:20:56.172', '2016-11-21 00:08:55.276', 'premium', NULL, false,
        NULL, 'general'::users_type, '', NULL, NULL, 'facebook'::users_origin_provider, NULL, NULL, NULL, NULL, NULL,
        NULL);


INSERT INTO public.products (id, "name", country, price, duration, created_at, updated_at, deleted_at, "payment_type",
                             currency, price_no_tax, tax_rate, item_name, item_unit, auto_renew, active, free_duration,
                             as_subscribe, fee, payment_type_code, purchase_upper_limit_count, external_product_id,
                             discount_duration, discount_price, bundle, sort, category, fee_rate, discount_price_no_tax,
                             discount_fee)
VALUES (99, 'creaditcard.promo10days', 'TW', 60.00, '10 days'::interval, '2020-01-07 12:15:04.290', NULL, NULL,
        'credit_card'::public."payment_type", 'NTD', 57.00, 5, '10天 VIP 體驗包', '次', false, true,
        '00:00:00'::interval, true, 1, '00', 0, NULL, '00:00:00'::interval, 0.00, '{}'::jsonb, 0, 'Organic', 1.90,
        0.00, 0);


-- orders
INSERT INTO public.orders (id, user_id, product_id, price, "payment_type", start_date, end_date, status, order_date,
                           info, created_at, realized_at, price_no_tax, tax_rate, invoice, canceled_at, fee,
                           external_order_id)
VALUES ('KT0020161010053242', 'kktv-api-unittest-0001', 99, 0.00,
        'credit_card'::public."payment_type", '2016-10-18 00:00:00.000', '2016-11-18 00:00:00.000',
        NULL, '2020-01-06 20:28:41.000', NULL,
        '2016-10-10 20:28:40.407', '2016-10-10 20:28:42.407', 142.00, 5, NULL, NULL, 0, NULL),
       ('KT0020161010053241', 'kktv-api-unittest-0001', 99, 0.00,
        'credit_card'::public."payment_type", '2016-09-18 00:00:00.000', '2016-10-18 00:00:00.000',
        'ok', '2020-01-06 20:28:41.000', NULL,
        '2016-10-10 20:28:40.407', '2016-10-10 20:28:42.407', 142.00, 5, NULL, NULL, 0, NULL),
       ('KT0020161010053243', 'kktv-api-unittest-0003', 99, 0.00,
        'credit_card':: public."payment_type", '2016-10-18 00:00:00.000', '2016-11-18 00:00:00.000',
        'ok', '2020-01-06 20:28:41.000', NULL,
        '2016-10-10 20:28:40.407', '2016-10-10 20:28:42.407', 142.00, 5, NULL, NULL, 0, NULL),
       ('KT0020161010053244', 'kktv-api-unittest-0004', 99, 0.00,
        'credit_card':: public."payment_type", '2016-10-18 00:00:00.000', '2016-11-18 00:00:00.000',
        'ok', '2020-01-06 20:28:41.000', NULL,
        '2016-10-10 20:28:40.407', '2016-10-10 20:28:42.407', 142.00, 5, NULL, NULL, 0, NULL);
