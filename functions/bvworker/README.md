## Login docker via AWS

aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin 312530604583.dkr.ecr.ap-northeast-1.amazonaws.com

## build new version 

 docker build -t 312530604583.dkr.ecr.ap-northeast-1.amazonaws.com/kktv-video-worker:latest .

## push ECR

docker push 312530604583.dkr.ecr.ap-northeast-1.amazonaws.com/kktv-video-worker:latest

## ECS cluster update to push version
