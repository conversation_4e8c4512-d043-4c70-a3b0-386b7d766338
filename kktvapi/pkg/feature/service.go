//go:generate mockgen -source service.go -destination service_mock.go -package feature
package feature

import (
	"net/http"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
)

type Service interface {
	HasFlag(flag Flag, req *http.Request) (bool, error)
	HasFlagForUser(flag Flag, userID string) (bool, error)
}

type configuration struct {
	cachemeta.AppConfiguration
	mu sync.Mutex
}

var (
	appConfig *configuration
	once      sync.Once
)

type service struct {
	features         map[Flag]feature
	featuresByUserID map[Flag]userFeature
}

// NewService returns a new feature service
// error is returned when init appConfig from redis failed
func NewService() Service {
	return &service{
		features: map[Flag]feature{
			FlagUseBvDRM:                  &featureUseBvDRM{},
			FlagSupportBrowseEntryProtect: &featureSupportBrowseEntryProtect{},
			FlagProvideV4WatchHistoryAPI:  &featureV4WatchHistoryAPI{},
			FlagAnimeAiringSchedule:       &featureAnimeAiringSchedule{},
		},
		featuresByUserID: map[Flag]userFeature{
			FlagEnablingMembership:  &featureEnablingMembership{},
			FlagEnablingAnimeAiring: &featureEnablingAnimeAiring{},
		},
	}
}

// HasFlag returns whether the flag is enabled
// error is returned when flag is not defined or appConfig is not initialized
func (s *service) HasFlag(flag Flag, req *http.Request) (bool, error) {
	if appConfig == nil {
		return false, ErrAppConfigNotInit
	}
	fea, ok := s.features[flag]
	if !ok {
		return false, ErrFlagNotDefined
	}
	return fea.hasFlag(req)
}

func (s *service) HasFlagForUser(flag Flag, userID string) (bool, error) {
	if appConfig == nil {
		return false, ErrAppConfigNotInit
	}
	fea, ok := s.featuresByUserID[flag]
	if !ok {
		return false, ErrFlagNotDefined
	}
	return fea.hasFlagForUser(userID)
}

func RefreshAppConfig(cacheMetaReader cache.Cacher) error {
	var ap = cachemeta.AppConfiguration{}
	if err := cacheMetaReader.HGet(key.MetaGetServiceGeneralConfig(), key.MetaServiceGeneralConfigHashKeys.AppConfig, &ap); err != nil {
		return err
	}
	appConfig.mu.Lock()
	defer appConfig.mu.Unlock()
	appConfig.AppConfiguration = ap
	log.Debug("feature.RefreshAppConfig: app config refreshed").Interface("config", appConfig.AppConfiguration).Send()
	return nil
}

func KeepRefreshAppConfig(cacheMetaReader cache.Cacher) {
	// TODO make controlleable to stop by calling context cancel
	once.Do(func() {
		appConfig = &configuration{}
		go func(reader cache.Cacher) {
			for {
				interval := 30 * time.Second
				if err := RefreshAppConfig(reader); err != nil {
					log.Warn("featureService: refresh app config failed").Err(err).Send()
					interval = 2 * time.Minute
				}
				time.Sleep(interval)
			}

		}(cacheMetaReader)
	})
}
