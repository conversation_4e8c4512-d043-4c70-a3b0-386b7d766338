//go:generate mockgen -source sms.go -destination sms_mock.go -package message
package message

import (
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/sms"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"sync"
)

type SMSer interface {
	SendMessage(recipient, content string) error
}

type smser struct {
	awsClient     sms.ServiceProvider
	vonageClient  sms.ServiceProvider
	defaultClient sms.ServiceProvider

	cacheReader cache.Cacher
}

var (
	onceSMS       sync.Once
	smserInstance SMSer
)

func NewSMSer() SMSer {
	onceSMS.Do(func() {
		var awsClient sms.ServiceProvider
		if sess, err := session.NewSession(&aws.Config{
			Region: aws.String("ap-northeast-1"),
		}); err != nil {
			log.Warn("smser: aws: new session failed").Err(err).Send()
		} else {
			awsClient = sms.NewProviderAWS(sess)
		}
		vonageClient := sms.NewProviderVonage(config.NexmoKey, config.NexmoSecret)
		smserInstance = &smser{
			awsClient:     awsClient,
			vonageClient:  vonageClient,
			defaultClient: vonageClient,
			cacheReader:   cache.New(container.CachePoolMeta().Slave()),
		}

	})
	return smserInstance

}

const (
	smsServiceProviderAWS    = "aws"
	smsServiceProviderVonage = "vonage"
)

func (s *smser) getServiceProvider() sms.ServiceProvider {
	if s.awsClient == nil {
		return s.defaultClient // currently, we only have 2 clients. so if aws client is nil, we use default client
	}
	var sp string
	err := s.cacheReader.HGet(key.MetaGetServiceGeneralConfig(), key.MetaServiceGeneralConfigHashKeys.SmsServiceProvider, &sp)
	if err != nil {
		log.Warn("sms client: cache: fail to get sms service provider").Err(err).Send()
	}
	switch sp {
	case smsServiceProviderAWS:
		return s.awsClient
	case smsServiceProviderVonage:
		return s.vonageClient
	default:
		return s.defaultClient
	}
}

func (s *smser) SendMessage(recipient, content string) error {
	return s.getServiceProvider().SendMessage(SenderSMS, recipient, content)
}
