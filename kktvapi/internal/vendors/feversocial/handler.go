package feversocial

import (
	"encoding/json"
	"errors"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/appauth"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/validation"
	"github.com/KKTV/kktv-api-v3/pkg/auth"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/encrypt"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"gopkg.in/dgrijalva/jwt-go.v3"
)

type Handler struct {
	userRepo        user.UserRepository
	authedAppRepo   appauth.Repository
	userCacheWriter cache.Cacher
	userCacheReader cache.Cacher
	clock           clock.Clock
}

func NewFeverSocialHandler() *Handler {
	return &Handler{
		userRepo:        user.NewUserRepository(),
		authedAppRepo:   appauth.NewRepository(),
		userCacheWriter: cache.New(container.CachePoolUser().Master()),
		userCacheReader: cache.New(container.CachePoolUser().Slave()),
		clock:           clock.New(),
	}
}

// GetUserByCode is design for the server-to-server communication between KKTV and FeverSocial.
// after KKTV user login via the FeverSocial campaign request, KKTV server will redirect to FeverSocial server with the user code.
// FeverSocial server will call this API to get the user information.
// refer to https://feversocial.stoplight.io/docs/feversocial/86412ed3e41f8-get-user-info
func (h *Handler) GetUserByCode(w http.ResponseWriter, r *http.Request) {
	var code string
	if urlValues, err := httpreq.ParseQuery(r); err != nil {
		render.JSONBadRequest(w, ErrInvalidParameters)
		return
	} else if code = urlValues.Get("code"); code == "" {
		render.JSONBadRequest(w, ErrInvalidParameters)
		return
	}

	var userID string
	cKey := key.UserAuthCode(code)
	if err := h.userCacheReader.Get(cKey, &userID); errors.Is(err, cache.ErrCacheMiss) {
		render.JSONNotFound(w, ErrInvalidUserCode)
		return
	} else if err != nil {
		log.Error("feversocialHandler: GetUserByCode: failed to userCacheReader.Get").Str("cache_key", cKey).Err(err).Send()
		render.JSONInternalServerErr(w, ErrUnknownError)
		return
	}

	defer func() {
		go func() {
			if err := h.userCacheWriter.Del(cKey); err != nil {
				log.Error("feversocialHandler: GetUserByCode: failed to userCacheWriter.Del").Str("cache_key", cKey).Err(err).Send()
			}
		}()
	}()

	u, err := h.userRepo.GetActiveByID(userID)
	if err != nil {
		log.Error("feversocialHandler: GetUserByCode: failed to userRepo.GetActiveByID").Str("user_id", userID).Err(err).Send()
		render.JSONInternalServerErr(w, ErrUnknownError)
		return
	} else if u == nil {
		render.JSONNotFound(w, ErrInvalidUser)
		return
	}

	render.JSONOk(w, getUserResp{
		ID:    encryptUserID(u.ID),
		Name:  u.GetDisplayName(),
		Email: u.Email.ValueOrZero(),
		Phone: u.Phone.ValueOrZero(),
	})
}

// GetAccessToken is design for the server-to-server communication between KKTV and FeverSocial.
// FeverSocial server will call this API to get the access token.
// refer to https://feversocial.stoplight.io/docs/feversocial/6823515998dbd-get-access-token
func (h *Handler) GetAccessToken(w http.ResponseWriter, r *http.Request) {
	var req getAccessTokenReq
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		render.JSONBadRequest(w, ErrInvalidParameters)
		return
	} else if err = validation.Validate(req); err != nil {
		render.JSONBadRequest(w, ErrInvalidParameters)
		return
	}

	var (
		err       error
		authedApp *dbuser.AuthedApp
	)
	if authedApp, err = h.authedAppRepo.GetActiveByAppID(req.AppID); err != nil {
		log.Error("feversocialHandler: GetAccessToken: failed to authedAppRepo.GetActiveByAppID").
			Str("app_id", req.AppID).
			Err(err).
			Send()
		render.JSONInternalServerErr(w, ErrUnknownError)
		return
	} else if authedApp == nil || authedApp.AppSecret != req.AppSecret {
		render.JSONBadRequest(w, ErrInvalidApp)
		return
	}

	now := h.clock.Now()
	jwtAuth := auth.NewJWTAuth(jwt.SigningMethodHS256, []byte(authedApp.SignKey.String))
	claims := jwt.StandardClaims{
		Audience:  authedApp.Name,
		Issuer:    "kktv",
		Subject:   authedApp.AppID,
		ExpiresAt: now.AddDate(0, 0, accessTokenExpireDays).Unix(),
		IssuedAt:  now.Unix(),
	}

	var accessToken string
	if accessToken, err = jwtAuth.GenerateToken(claims); err != nil {
		log.Error("feversocialHandler: GetAccessToken: failed to jwtAuth.GenerateToken").
			Str("app_id", authedApp.AppID).
			Interface("claims", claims).
			Err(err).
			Send()
		render.JSONInternalServerErr(w, ErrUnknownError)
		return
	}

	render.JSONOk(w, getAccessTokenResp{
		AccessToken: accessToken,
		ExpiresIn:   accessTokenExpireDays * 24 * 60 * 60,
		TokenType:   "Bearer",
	})
}

// GetMembershipTier is design for the server-to-server communication between KKTV and FeverSocial.
// FeverSocial server will call this API to get the membership tier.
// refer to https://feversocial.stoplight.io/docs/feversocial/pc31qe0tyrvjp-
func (h *Handler) GetMembershipTier(w http.ResponseWriter, r *http.Request) {
	var encUserID string
	if urlValues, err := httpreq.ParseQuery(r); err != nil {
		render.JSONBadRequest(w, ErrInvalidParameters)
		return
	} else if encUserID = urlValues.Get("user_id"); encUserID == "" {
		render.JSONBadRequest(w, ErrInvalidParameters)
		return
	}

	var (
		err    error
		userID string
	)
	if userID, err = decryptUserID(encUserID); err != nil {
		render.JSONNotFound(w, ErrInvalidParameters)
		return
	}

	var existedUser *dbuser.User
	if existedUser, err = h.userRepo.GetActiveByID(userID); err != nil {
		log.Error("feversocialHandler: GetMembershipTier: failed to userRepo.GetActiveByID").
			Str("user_id", userID).
			Err(err).
			Send()
		render.JSONInternalServerErr(w, ErrUnknownError)
		return
	} else if existedUser == nil {
		render.JSONNotFound(w, ErrInvalidUser)
		return
	}

	if len(existedUser.Membership) == 0 {
		log.Error("feversocialHandler: GetMembershipTier: user has no membership").Str("user_id", userID).Send()
		render.JSONInternalServerErr(w, ErrInvalidUser)
		return
	}

	ms := existedUser.Membership[0] //TODO currently only support one membership, need to update when support multiple memberships
	render.JSONOk(w, getMembershipTierResp{
		ID:   ms.Role.String(),
		Name: membershipTierMap[ms.Role.String()],
	})
}

func encryptUserID(userID string) string {
	encUserID, _ := encrypt.AesCBCEncrypt(encryptionKey, encryptionIV, userID)
	return encUserID
}

func decryptUserID(userID string) (string, error) {
	decUserID, err := encrypt.AesCBCDecrypt(encryptionKey, encryptionIV, userID)
	if err != nil {
		return "", err
	}
	return decUserID, nil
}
