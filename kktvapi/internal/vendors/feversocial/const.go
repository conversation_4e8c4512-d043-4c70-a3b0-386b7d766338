package feversocial

import "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"

const (
	accessTokenExpireDays = 10
)

var (
	encryptionKey = []byte("feversocialxKKTV_encryption_key`")
	encryptionIV  = []byte("fEvers0cialXKkTV")

	membershipTierMap = map[string]string{
		dbuser.MemberRolePaidAnime.String(): "動漫付費會員",
		dbuser.MemberRolePremium.String():   "全站付費會員",
		dbuser.MemberRoleExpired.String():   "過期會員",
		dbuser.MemberRoleFreeTrial.String(): "試用會員",
		dbuser.MemberRolePR.String():        "公關會員",
	}
)
