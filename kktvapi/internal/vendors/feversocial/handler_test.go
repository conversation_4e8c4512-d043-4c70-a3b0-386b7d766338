package feversocial

import (
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/appauth"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/go-zoo/bone"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"gopkg.in/guregu/null.v3"
)

type FeverSocialHandlerSuite struct {
	suite.Suite
	ctrl                *gomock.Controller
	mockUserRepo        *user.MockUserRepository
	mockAuthedAppRepo   *appauth.MockRepository
	mockUserCacheReader *cache.MockCacher
	mockUserCacheWriter *cache.MockCacher
	mockClock           *clock.MockClock

	handler Handler
	app     *bone.Mux
}

func TestFeverSocialHandlerSuite(t *testing.T) {
	suite.Run(t, new(FeverSocialHandlerSuite))
}

func (suite *FeverSocialHandlerSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockUserRepo = user.NewMockUserRepository(suite.ctrl)
	suite.mockAuthedAppRepo = appauth.NewMockRepository(suite.ctrl)
	suite.mockUserCacheReader = cache.NewMockCacher(suite.ctrl)
	suite.mockUserCacheWriter = cache.NewMockCacher(suite.ctrl)
	suite.mockClock = clock.NewMockClock(suite.ctrl)
	suite.handler = Handler{
		userRepo:        suite.mockUserRepo,
		authedAppRepo:   suite.mockAuthedAppRepo,
		userCacheReader: suite.mockUserCacheReader,
		userCacheWriter: suite.mockUserCacheWriter,
		clock:           suite.mockClock,
	}

	suite.app = bone.New()
	suite.app.GetFunc("/vendors/feversocial/get-user", suite.handler.GetUserByCode)
	suite.app.PostFunc("/vendors/feversocial/get-access-token", suite.handler.GetAccessToken)
	suite.app.GetFunc("/vendors/feversocial/get-membership-tier", suite.handler.GetMembershipTier)
}

func (suite *FeverSocialHandlerSuite) TearDownTest() {
}

func (suite *FeverSocialHandlerSuite) TestGetUserByCode() {
	var (
		existedUser = &dbuser.User{
			ID:    "josie",
			Email: null.StringFrom("<EMAIL>"),
			Phone: null.StringFrom("123890123"),
		}
	)

	testcases := []struct {
		name           string
		queryStrings   string
		given          func() (wait chan struct{})
		expectedStatus int
		assertBody     func(body []byte)
	}{
		{
			name:         "WHEN code not found in cache THEN return 404",
			queryStrings: "code=encodedjosie",
			given: func() chan struct{} {
				suite.mockUserCodeNotFound("encodedjosie")
				return nil
			},
			expectedStatus: http.StatusNotFound,
		},
		{
			name:         "WHEN code found in cache THEN return user model",
			queryStrings: "code=encodedjosie",
			given: func() chan struct{} {
				suite.mockGotUserCode("encodedjosie", "josie")
				suite.mockFoundUserByID("josie", existedUser)
				return suite.mockDeletedUserCodeFromCache("encodedjosie")
			},
			expectedStatus: http.StatusOK,
			assertBody: func(body []byte) {
				var res getUserResp
				suite.NoError(json.Unmarshal(body, &res))
				suite.Equal("123890123", res.Name)
				suite.Equal("123890123", res.Phone)
				suite.Equal("<EMAIL>", res.Email)
				suite.Equal("46f61d9af7be1d4d67fe463e81177818", res.ID) // aes encrypt of `josie`
			},
		},
		{
			name:           "WHEN code is not given THEN return 400",
			queryStrings:   "",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:         "WHEN code found in cache but user not found THEN return 404 not found, and code is deleted from cache",
			queryStrings: "code=encodedjosie",
			given: func() chan struct{} {
				suite.mockGotUserCode("encodedjosie", "josie")
				suite.mockUserNotFound("josie")
				return suite.mockDeletedUserCodeFromCache("encodedjosie")
			},
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			var wait chan struct{}
			if tc.given != nil {
				wait = tc.given()
			}

			req := httptest.NewRequest(http.MethodGet, "/vendors/feversocial/get-user?"+tc.queryStrings, nil)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			if wait != nil {
				<-wait
			}
			suite.Equal(tc.expectedStatus, rr.Code)
			if tc.assertBody != nil {
				tc.assertBody(rr.Body.Bytes())
			}
		})
	}
}

func (suite *FeverSocialHandlerSuite) TestGetAccessToken() {
	var (
		reqBody    = `{"app_id":"testAppID","app_secret":"testAppSecret"}`
		existedApp = &dbuser.AuthedApp{
			AppID:     "testAppID",
			AppSecret: "testAppSecret",
			SignKey:   null.StringFrom("test-jwt-key"),
		}
	)

	testcases := []struct {
		name           string
		body           string
		given          func() (wait chan struct{})
		expectedStatus int
		assertBody     func(body []byte)
	}{
		{
			name:           "WHEN request body is not valid JSON THEN return 400",
			body:           `{"app_id":"testAppID",`,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "WHEN request body lacks required parameter THEN return 400",
			body:           `{"app_id":"testAppID"}`,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "WHEN repository error occurs THEN return 500",
			body: reqBody,
			given: func() chan struct{} {
				suite.mockAppAuthRepoError("testAppID")
				return nil
			},
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name: "WHEN app not found in repository THEN return 400",
			body: reqBody,
			given: func() chan struct{} {
				suite.mockAppNotFound("testAppID")
				return nil
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "WHEN app found in repository but app secret is invalid THEN return 400",
			body: `{"app_id":"testAppID","app_secret":"invalidAppSecret"}`,
			given: func() chan struct{} {
				suite.mockFoundAppByID("testAppID", existedApp)
				return nil
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "WHEN app found in repository THEN return access token",
			body: reqBody,
			given: func() chan struct{} {
				suite.mockFoundAppByID("testAppID", existedApp)
				suite.mockNow()
				return nil
			},
			expectedStatus: http.StatusOK,
			assertBody: func(body []byte) {
				var res getAccessTokenResp
				suite.NoError(json.Unmarshal(body, &res))
				suite.NotEmpty(res.AccessToken)
				suite.EqualValues(accessTokenExpireDays*24*60*60, res.ExpiresIn)
				suite.Equal("Bearer", res.TokenType)
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			if tc.given != nil {
				tc.given()
			}

			req := httptest.NewRequest(http.MethodPost, "/vendors/feversocial/get-access-token", strings.NewReader(tc.body))
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			suite.Equal(tc.expectedStatus, rr.Code)
			if tc.assertBody != nil {
				tc.assertBody(rr.Body.Bytes())
			}
		})
	}
}

func (suite *FeverSocialHandlerSuite) TestGetMembershipTier() {
	const (
		userID    = "test-user-id"
		encUserID = "3f2497ff53d140ea2197833b0f2bde91"
	)

	assertTier := func(role dbuser.MemberRole) func(body []byte) {
		return func(body []byte) {
			var res getMembershipTierResp
			suite.NoError(json.Unmarshal(body, &res))
			suite.Equal(getMembershipTierResp{
				ID:   role.String(),
				Name: membershipTierMap[role.String()],
			}, res)
		}
	}
	testcases := []struct {
		name           string
		queryString    string
		given          func()
		expectedStatus int
		assertBody     func(body []byte)
	}{
		{
			name:           "WHEN user id is not given THEN return 400",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "WHEN user id fails to decrypt THEN return 404",
			queryString:    "user_id=invalidUserID",
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "WHEN user not found THEN return 404",
			queryString:    "user_id=" + encUserID,
			expectedStatus: http.StatusNotFound,
			given: func() {
				suite.mockUserNotFound(userID)
			},
		},
		{
			name:           "WHEN user has no membership tier THEN internal server error",
			queryString:    "user_id=" + encUserID,
			expectedStatus: http.StatusInternalServerError,
			given: func() {
				suite.mockFoundUserByID(userID, &dbuser.User{
					ID: userID,
				})
			},
		},
		{
			name:        "WHEN pr user is found THEN return membership tier",
			queryString: "user_id=" + encUserID,
			given: func() {
				suite.mockFoundUserByID(userID, &dbuser.User{
					ID:         userID,
					Membership: dbuser.MembershipPublicRelationsOnly,
				})
			},
			expectedStatus: http.StatusOK,
			assertBody:     assertTier(dbuser.MemberRolePR),
		},
		{
			name:        "WHEN expired user is found THEN return membership tier",
			queryString: "user_id=" + encUserID,
			given: func() {
				suite.mockFoundUserByID(userID, &dbuser.User{
					ID:         userID,
					Membership: dbuser.MembershipExpired,
				})
			},
			expectedStatus: http.StatusOK,
			assertBody:     assertTier(dbuser.MemberRoleExpired),
		},
		{
			name:        "WHEN AnimePass user is found THEN return membership tier",
			queryString: "user_id=" + encUserID,
			given: func() {
				suite.mockFoundUserByID(userID, &dbuser.User{
					ID:         userID,
					Membership: dbuser.Membership{{Role: dbuser.MemberRolePaidAnime}},
				})
			},
			expectedStatus: http.StatusOK,
			assertBody:     assertTier(dbuser.MemberRolePaidAnime),
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			if tc.given != nil {
				tc.given()
			}

			req := httptest.NewRequest(http.MethodGet, "/vendors/feversocial/get-membership-tier?"+tc.queryString, nil)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			suite.Equal(tc.expectedStatus, rr.Code)
			if tc.assertBody != nil {
				tc.assertBody(rr.Body.Bytes())
			}
		})
	}
}

func (suite *FeverSocialHandlerSuite) mockUserCodeNotFound(code string) {
	cKey := key.UserAuthCode(code)
	suite.mockUserCacheReader.EXPECT().Get(cKey, gomock.Any()).Return(cache.ErrCacheMiss)
}

func (suite *FeverSocialHandlerSuite) mockGotUserCode(code, userID string) {
	cKey := key.UserAuthCode(code)
	suite.mockUserCacheReader.EXPECT().Get(cKey, gomock.Any()).SetArg(1, userID).Return(nil)
}

func (suite *FeverSocialHandlerSuite) mockFoundUserByID(userID string, u *dbuser.User) {
	suite.mockUserRepo.EXPECT().GetActiveByID(userID).Return(u, nil)
}

func (suite *FeverSocialHandlerSuite) mockUserNotFound(userID string) {
	suite.mockUserRepo.EXPECT().GetActiveByID(userID).Return(nil, nil)
}

func (suite *FeverSocialHandlerSuite) mockDeletedUserCodeFromCache(code string) chan struct{} {
	wait := make(chan struct{})
	cKey := key.UserAuthCode(code)
	suite.mockUserCacheWriter.EXPECT().Del(cKey).Return(nil).Do(func(string) {
		close(wait)
	})
	return wait
}

func (suite *FeverSocialHandlerSuite) mockFoundAppByID(appID string, app *dbuser.AuthedApp) {
	suite.mockAuthedAppRepo.EXPECT().GetActiveByAppID(appID).Return(app, nil)
}

func (suite *FeverSocialHandlerSuite) mockAppNotFound(appID string) {
	suite.mockAuthedAppRepo.EXPECT().GetActiveByAppID(appID).Return(nil, nil)
}

func (suite *FeverSocialHandlerSuite) mockAppAuthRepoError(appID string) {
	suite.mockAuthedAppRepo.EXPECT().GetActiveByAppID(appID).Return(nil, errors.New("repository error"))
}

func (suite *FeverSocialHandlerSuite) mockNow() {
	suite.mockClock.EXPECT().Now().Return(time.Now())
}
