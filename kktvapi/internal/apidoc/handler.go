package apidoc

import (
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/apidoc"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/validation"
	"github.com/KKTV/kktv-api-v3/pkg/auth"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/markbates/goth/gothic"
	"github.com/markbates/goth/providers/google"
	"gopkg.in/dgrijalva/jwt-go.v3"
)

type Handler struct {
	authProvider    *google.Provider
	jwtAuth         auth.JWTAuth
	allowedDomains  []string
	allowedAccounts []string
}

func NewHandler(jwtAuth auth.JWTAuth, authProvider *google.Provider) *Handler {
	return &Handler{
		authProvider:    authProvider,
		jwtAuth:         jwtAuth,
		allowedDomains:  config.SSOAllowedDomain,
		allowedAccounts: config.APIDocSSOAllowedAccounts,
	}
}

type codeAuth struct {
	Code        string `json:"code" validate:"required"`
	RedirectURL string `json:"redirect_url"`
}

func (h *Handler) HandleCallback(w http.ResponseWriter, r *http.Request) {
	var authReq codeAuth
	err := httpreq.Scan(&authReq, r.URL.Query())
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	} else if err := validation.Validate(authReq); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	state := gothic.SetState(r)
	sess, err := h.authProvider.BeginAuth(state)
	if err != nil {
		http.Error(w, err.Error(), http.StatusUnauthorized)
		return
	}
	params, _ := url.ParseQuery("code=" + authReq.Code)
	_, err = sess.Authorize(h.authProvider, params)
	if err != nil {
		http.Error(w, err.Error(), http.StatusUnauthorized)
		return
	}
	user, err := h.authProvider.FetchUser(sess)
	if err != nil {
		http.Error(w, err.Error(), http.StatusUnauthorized)
		return
	}

	if !h.canAccess(user.Email) {
		http.Error(w, "Unauthorized domain", http.StatusUnauthorized)
		return
	}

	claims := apidoc.DocClaims{
		Email: user.Email,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: time.Now().Add(24 * time.Hour).Unix(),
			IssuedAt:  time.Now().Unix(),
		},
	}

	token, err := h.jwtAuth.GenerateToken(claims)
	if err != nil {
		log.Warn("APIDocHandler: HandleCallback: token generation fail").Err(err).Str("user", user.Email).Send()
		http.Error(w, "Token generation failed", http.StatusInternalServerError)
		return
	}

	q := url.Values{}
	q.Add("token", token)
	q.Add("exp", fmt.Sprintf("%d", claims.ExpiresAt))
	if authReq.RedirectURL != "" {
		q.Add("redirect", authReq.RedirectURL)
	}
	redirectURI := fmt.Sprintf("%s?%s", LoginURL, q.Encode())
	http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
}

func (h *Handler) canAccess(email string) bool {
	for _, domain := range h.allowedDomains {
		if strings.HasSuffix(email, "@"+domain) {
			return true
		}
	}
	if slice.Contain(h.allowedAccounts, email) {
		return true
	}
	return false
}
