package favorite

import (
	"encoding/json"
	"errors"
	"net/http"
	"slices"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/meta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/platform"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/request"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/watchhistory"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbmeta"
	legacymeta "github.com/KKTV/kktv-api-v3/pkg/model/dbmeta/legacy"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/go-zoo/bone"
)

type Handler struct {
	service           Service
	userPlaylistRepo  user.UserPlaylistRepository
	playlistTitleRepo user.PlaylistTitleRepository
	titleRepo         meta.TitleRepository
	permissionSrv     permission.Service
	watchHistorySrv   watchhistory.Service
	metaCacheReader   cache.Cacher
}

func NewHandler() *Handler {
	userPlaylistRepo := user.NewUserPlaylistRepository()
	playlistTitleRepo := user.NewPlaylistTitleRepository()
	return &Handler{
		service:           NewService(userPlaylistRepo, playlistTitleRepo),
		userPlaylistRepo:  userPlaylistRepo,
		playlistTitleRepo: playlistTitleRepo,
		titleRepo:         meta.NewTitleRepository(),
		permissionSrv:     container.PermissionService(),
		watchHistorySrv:   watchhistory.NewService(),
		metaCacheReader:   cache.New(container.CachePoolMeta().Slave()),
	}
}

type titleFilterByType string

const (
	titleFilterByTypeAiring           titleFilterByType = "airing"
	titleFilterByTypeExpireSoon       titleFilterByType = "expire_soon"
	titleFilterByStatusLicenseExpired titleFilterByType = "license_expired"
)

func (t titleFilterByType) String() string {
	return string(t)
}

func (h *Handler) GetMyFavoriteTitles(w http.ResponseWriter, r *http.Request) {
	dt := bone.GetValue(r, "deviceType")
	deviceType := platform.DeviceTypeWeb
	if dt == "a" {
		deviceType = platform.DeviceTypeMobileApp
	}

	filterBy := r.URL.Query().Get("filter_by")

	statusCode, resp := http.StatusOK, rest.Ok()
	defer func() {
		render.JSON(w, statusCode, resp)
	}()

	paging := request.Paging{Page: 1, PageSize: 20}
	if err := paging.ScanFromRequest(r, 20); err != nil {
		plog.Warn("get my favorite titles: get paging failed").Err(err).Send()
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrInvalidParameters.Message, ErrInvalidParameters.Code)
		return
	}

	var userID string
	access := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	membership := access.Memberships
	userID = access.UserID

	favoritePlaylist, err := h.userPlaylistRepo.GetFavoritePlaylist(userID)
	if err != nil {
		plog.Error("v4favoriteHandler: failed to GetFavoritePlaylist").Str("user_id", userID).Err(err).Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrUnknown.Message, ErrUnknown.Code)
		return
	}

	if favoritePlaylist == nil {
		resp.Data = favoriteTitlesResp{
			Pagination: &presenter.Pagination{
				Page:     paging.Page,
				PageSize: paging.PageSize,
				Total:    0,
			},
		}
		return
	}

	titleIDs, err := h.playlistTitleRepo.GetPlaylistTitleIDsByPlaylistID(favoritePlaylist.ID)
	if err != nil {
		plog.Error("v4favoriteHandler: failed to GetPlaylistTitleIDsByPlaylistID").Str("playlist_id", favoritePlaylist.ID).Err(err).Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrUnknown.Message, ErrUnknown.Code)
		return
	}

	tds, err := h.titleRepo.ListViewableTitleDetailWithoutSeries(titleIDs, false)
	if err != nil {
		plog.Error("v4favoriteHandler: failed to ListViewableTitlesWithDetail").Interface("title_ids", titleIDs).Err(err).Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrUnknown.Message, ErrUnknown.Code)
		return
	}

	filtered := make([]*legacymeta.TitleDetail, 0)
	if filterBy == titleFilterByTypeAiring.String() {
		for _, td := range tds {
			if !td.LegacyTitleDetail.IsEnding {
				filtered = append(filtered, td)
			}
		}
	} else if filterBy == titleFilterByTypeExpireSoon.String() {
		for _, td := range tds {
			for _, label := range td.LegacyTitleDetail.ContentLabels {
				if label == "expire_soon" {
					filtered = append(filtered, td)
				}
			}
		}
	} else {
		filtered = tds
	}

	from, to := paging.GetRange(len(filtered))
	result := filtered[from:to]

	titles := make([]presenter.ListedTitle, len(result))
	for i, titleDetail := range result {
		titles[i] = presenter.ListedTitle{}
		isFullAccess := false
		req := permission.RequestFullAccessTitleDetail(titleDetail, membership)
		if err := h.permissionSrv.Grant(req); kktverror.IsInternalErr(err) {
			plog.Error("v4favoriteHandler: permissionService: internal error").Interface("titleDetail", titleDetail).Err(err).Send()
			statusCode, resp = http.StatusInternalServerError, rest.Error(ErrUnknown.Message, ErrUnknown.Code)
			return
		} else if err == nil {
			isFullAccess = true
		}
		titles[i].ConvertFromModel(titleDetail, isFullAccess, deviceType)
	}
	data := favoriteTitlesResp{
		Pagination: &presenter.Pagination{
			Page:     paging.Page,
			PageSize: paging.PageSize,
			Total:    len(filtered),
		},
		Items:       titles,
		AllTitleIDs: titleIDs,
	}
	resp.Data = data
}

// ExploreMyFavorite
// 1. 取得 user 的 favorite playlist
// 2. 取得 playlist 裡面的 title ids
// 3. 用 title ids 去 title repo 拿 title detail & watch history
// 4. 用 title detail 去 permissionService 拿權限
// 5. 把 title detail 轉成 presenter.FavoriteExplorer
// 6. 把 title detail 的 genres 取出來，並且不重複
// 7. 回傳 presenter.FavoriteExplorer
func (h *Handler) ExploreMyFavorite(w http.ResponseWriter, r *http.Request) {
	access := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	membership := access.Memberships
	userID := access.UserID

	dt := bone.GetValue(r, "deviceType")
	deviceType := platform.DeviceTypeWeb
	if dt == "a" {
		deviceType = platform.DeviceTypeMobileApp
	}

	filterBy := r.URL.Query().Get("filter_by")

	statusCode, resp := http.StatusOK, rest.Ok()
	defer func() {
		render.JSON(w, statusCode, resp)
	}()

	playlistTitles, err := h.playlistTitleRepo.GetPlaylistTitlesByUserIDAndName(userID, "favorite")
	if err != nil {
		plog.Error("v4favoriteHandler: failed to GetPlaylistTitlesByPlaylistID").Str("user_id", userID).Err(err).Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrUnknown.Message, ErrUnknown.Code)
		return
	}

	if len(playlistTitles) == 0 {
		resp.Data = favoriteExplorerResp{
			Total:  0,
			Genres: []string{},
			Items:  []presenter.FavoriteExplorer{},
		}
		return
	}

	titleIDs := make([]string, len(playlistTitles))
	playlistTitleMap := make(map[string]*dbuser.PlaylistTitle, len(playlistTitles))
	for i, playlistTitle := range playlistTitles {
		titleIDs[i] = playlistTitle.TitleID
		playlistTitleMap[playlistTitle.TitleID] = playlistTitle
	}

	var wg sync.WaitGroup
	var tdsErr, watchedErr, planLustErr error
	var tds []*legacymeta.TitleDetail
	var watched watchhistory.WatchedTitles
	var planLustTitleIDs []string
	wg.Add(3)

	go func() {
		defer wg.Done()
		tds, tdsErr = h.titleRepo.ListBulkViewableTitleDetailByIDs(titleIDs)
	}()

	go func() {
		defer wg.Done()
		watched, watchedErr = h.watchHistorySrv.GetByUserID(userID)
	}()

	go func() {
		defer wg.Done()
		planLustTitleIDs, planLustErr = h.getPlanLustTitleIDs()
	}()

	wg.Wait()

	if tdsErr != nil {
		plog.Error("v4favoriteHandler: ExploreMyFavorite: ListBulkViewableTitleDetailByIDs: failed to get title details").Interface("title_ids", titleIDs).Err(tdsErr).Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrUnknown.Message, ErrUnknown.Code)
		return
	}

	if watchedErr != nil && !errors.Is(watchedErr, watchhistory.ErrNotFound) {
		plog.Error("v4favoriteHandler: ExploreMyFavorite: watchHistorySrv failed to get GetByUserID").Str("user_id", userID).Err(watchedErr).Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrUnknown.Message, ErrUnknown.Code)
		return
	}

	if planLustErr != nil {
		plog.Error("v4favoriteHandler: ExploreMyFavorite: failed to get plan lust title ids").Err(planLustErr).Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrUnknown.Message, ErrUnknown.Code)
		return
	}

	watchedMap := make(map[string]watchhistory.WatchedTitle, len(watched))
	for _, w := range watched {
		watchedMap[w.ID] = w
	}

	filtered := make([]*legacymeta.TitleDetail, 0)
	isLicenseExpired := filterBy == dbmeta.TitleStatusLicenseExpire.String()
	for _, td := range tds {
		titleStatusIsExpired := td.LegacyTitleDetail.Status != dbmeta.TitleStatusLicenseValid.String() &&
			td.LegacyTitleDetail.Status != dbmeta.TitleStatusComingSoon.String()
		if titleStatusIsExpired == isLicenseExpired {
			filtered = append(filtered, td)
		}
	}

	titles := make([]presenter.FavoriteExplorer, len(filtered))
	genreMap := make(map[string]bool)
	for i, titleDetail := range filtered {
		for _, genre := range titleDetail.LegacyTitleDetail.Genres {
			if genre.CollectionType == "genre" {
				genreMap[genre.CollectionName] = true
			}
		}

		isPlanLust := slices.Contains(planLustTitleIDs, titleDetail.LegacyTitleDetail.ID)
		if isPlanLust {
			genreMap["深夜"] = true
		}

		titles[i] = presenter.FavoriteExplorer{}
		isFullAccess := false
		req := permission.RequestFullAccessTitleDetail(titleDetail, membership)
		if err := h.permissionSrv.Grant(req); kktverror.IsInternalErr(err) {
			plog.Error("v4favoriteHandler: permissionService: internal error").Interface("titleDetail", titleDetail).Err(err).Send()
			statusCode, resp = http.StatusInternalServerError, rest.Error(ErrUnknown.Message, ErrUnknown.Code)
			return
		} else if err == nil {
			isFullAccess = true
		}
		titles[i].ConvertFromModel(titleDetail, isFullAccess, deviceType, watchedMap[titleDetail.LegacyTitleDetail.ID], playlistTitleMap[titleDetail.LegacyTitleDetail.ID], isPlanLust)
	}

	resp.Data = favoriteExplorerResp{
		Total:  len(filtered),
		Genres: h.sortGenres(genreMap),
		Items:  titles,
	}
}

func (h *Handler) getPlanLustTitleIDs() ([]string, error) {
	var obj struct {
		TitleIDs []string `json:"title_ids"`
	}
	if err := h.metaCacheReader.HGet(key.MetaGetServiceGeneralConfig(), key.MetaServiceGeneralConfigHashKeys.PlanLustTitleIDs, &obj); err != nil {
		return nil, err
	}

	return obj.TitleIDs, nil
}

func (h *Handler) sortGenres(genreMap map[string]bool) []string {
	desiredOrder := []string{"戲劇", "動漫", "娛樂", "電影", "親子", "深夜"}
	var sorted []string
	for _, genre := range desiredOrder {
		if _, ok := genreMap[genre]; ok {
			sorted = append(sorted, genre)
		}
	}
	return sorted
}

func (h *Handler) DeleteMyFavoriteTitles(w http.ResponseWriter, r *http.Request) {
	statusCode, resp := http.StatusOK, rest.Ok()
	defer func() {
		if statusCode != http.StatusNoContent {
			render.JSON(w, statusCode, resp)
		} else {
			w.WriteHeader(http.StatusNoContent)
		}
	}()

	access := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	userID := access.UserID

	var req struct {
		TitleIDs []string `json:"title_ids"`
	}

	jsonDecoder := json.NewDecoder(r.Body)
	if err := jsonDecoder.Decode(&req); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrInvalidBody.Message, ErrInvalidBody.Code)
		return
	}

	if len(req.TitleIDs) == 0 {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrNotFoundBodyTitleIDs.Message, ErrNotFoundBodyTitleIDs.Code)
		return
	}

	if err := h.service.RemoveTitlesFromFavoritePlaylist(userID, req.TitleIDs); err != nil {
		plog.Error("DeleteMyFavoriteTitles: failed to remove titles from favorite playlist").Str("user_id", userID).Interface("title_ids", req.TitleIDs).Err(err).Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrUnknown.Message, ErrUnknown.Code)
		return
	}

	statusCode = http.StatusNoContent
}

func (h *Handler) AddMyFavoriteTitles(w http.ResponseWriter, r *http.Request) {
	statusCode, resp := http.StatusOK, rest.Ok()
	defer func() {
		render.JSON(w, statusCode, resp)
	}()
	access := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	userID := access.UserID

	var req AddMyFavoriteTitlesRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrInvalidParameters.Message, ErrInvalidParameters.Code)
		return
	}

	titleIDs := req.TitleIDs

	if len(titleIDs) == 0 {
		return
	}

	_, err := h.service.AddFavorite(userID, titleIDs)
	if errors.Is(err, ErrMaxFavoriteCountReached) {
		plog.Warn("v4favoriteHandler: failed to AddFavorite").Str("user_id", userID).Err(err).Send()
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrFavoriteTitleOverLimit.Message, ErrFavoriteTitleOverLimit.Code)
		resp.Data = struct {
			Limit int `json:"limit"`
		}{
			Limit: MaxFavoriteCount,
		}
		return
	} else if err != nil {
		plog.Error("v4favoriteHandler: failed to AddFavorite").Str("user_id", userID).Err(err).Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrUnknown.Message, ErrUnknown.Code)
		return
	}

	return
}

func (h *Handler) UpdateMyFavoriteTitles(w http.ResponseWriter, r *http.Request) {
	statusCode, resp := http.StatusOK, rest.Ok()
	defer func() {
		render.JSON(w, statusCode, resp)
	}()

	access := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	userID := access.UserID

	var req AddMyFavoriteTitlesRequest
	if err := httpreq.PayloadBinding(&req, r); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrInvalidParameters.Message, ErrInvalidParameters.Code)
		return
	}

	titleIDs := req.TitleIDs

	if len(titleIDs) == 0 {
		return
	}

	if err := h.service.AddFavoriteAndRemoveLatest(userID, titleIDs); err != nil {
		plog.Error("v4favoriteHandler: UpdateMyFavoriteTitles: failed to AddFavoriteAndRemoveLatest").Str("user_id", userID).Interface("title_ids", titleIDs).Err(err).Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrUnknown.Message, ErrUnknown.Code)
		return
	}

	return
}

func (h *Handler) GetMyFavoriteTitleIDs(w http.ResponseWriter, r *http.Request) {

	var userID string
	access := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	userID = access.UserID
	statusCode, resp := http.StatusOK, rest.Ok()
	defer func() {
		render.JSON(w, statusCode, resp)
	}()

	favoritePlaylist, err := h.userPlaylistRepo.GetFavoritePlaylist(userID)
	if err != nil {
		plog.Error("v4favoriteHandler: GetMyFavoriteTitleIDs: userPlaylistRepo: failed to GetFavoritePlaylist").Str("user_id", userID).Err(err).Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrUnknown.Message, ErrUnknown.Code)
		return
	}
	if favoritePlaylist == nil {
		resp.Data = favoriteTitleIDsResp{}
		return
	}
	titleIDs, err := h.playlistTitleRepo.GetPlaylistTitleIDsByPlaylistID(favoritePlaylist.ID)
	if err != nil {
		plog.Error("v4favoriteHandler: GetMyFavoriteTitleIDs: playlistTitleRepo: failed to GetMyFavoriteTitleIDs").Str("playlist_id", favoritePlaylist.ID).Err(err).Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrUnknown.Message, ErrUnknown.Code)
		return
	}
	resp.Data = favoriteTitleIDsResp{
		AllTitleIDs: titleIDs,
	}
}
