//go:generate mockgen -source service.go -destination service_mock.go -package productpackage
package productpackage

import (
	"sort"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/productpackagerule"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/pkg/billing"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/duke-git/lancet/v2/slice"
	"gopkg.in/guregu/null.v3"
)

var (
	srv     Service
	onceSrv sync.Once
)

type Service interface {
	ListByPlatform(platform string) (*dbuser.ProductPackages, error)
	ListBillingByPlatform(platform string) (*dbuser.ProductPackages, error)
	ListBillingProducts() (billing.Products, error)
	GetLayoutByPackageIDs(pkgIDs []int) (layouts *dbuser.PackageLayouts, err error)
	FilterPackages(userID string, pkgs *dbuser.ProductPackages) ([]*FilteredPackage, error)
}

type service struct {
	repo             user.ProductPackageRepository
	layoutRepo       user.PackageLayoutRepository
	billingClient    billing.Client
	billingOrderRepo user.PackageBillingOrderRepository
	userRepo         user.UserRepository
	legacyHelper     legacyHelper
}

type FilteredPackage struct {
	Package         *dbuser.ProductPackage
	SatisfiedTarget *dbuser.PackageTarget
}

func NewService() Service {
	onceSrv.Do(func() {
		srv = &service{
			repo:             user.NewProductPackageRepository(),
			layoutRepo:       user.NewPackageLayoutRepository(),
			billingClient:    container.BillingClient(),
			billingOrderRepo: user.NewPackageBillingOrderRepository(),
			userRepo:         user.NewUserRepository(),
			legacyHelper:     &legacyHelp{},
		}
	})
	return srv
}

func (s *service) ListByPlatform(platform string) (*dbuser.ProductPackages, error) {
	return s.repo.ListByPlatform(platform)
}

func (s *service) ListBillingByPlatform(platform string) (*dbuser.ProductPackages, error) {
	return s.repo.ListBillingPkgsByPlatform(platform)
}

func (s *service) ListBillingProducts() (billing.Products, error) {
	return s.billingClient.ListProducts()
}

func (s *service) GetLayoutByPackageIDs(pkgIDs []int) (layouts *dbuser.PackageLayouts, err error) {
	return s.layoutRepo.GetByPackageIDs(pkgIDs)
}

func (s *service) getUserIdentities(userID string, membership dbuser.Membership, hadSubscribed bool) ([]dbuser.PackageTargetIdentity, error) {
	if !membership.IsMember() {
		return []dbuser.PackageTargetIdentity{dbuser.PackageTargetIdentityGuest}, nil
	}

	roleMapping := map[dbuser.MemberRole]func() dbuser.PackageTargetIdentity{
		dbuser.MemberRolePrime:     func() dbuser.PackageTargetIdentity { return dbuser.PackageTargetIdentityPrime },
		dbuser.MemberRolePR:        func() dbuser.PackageTargetIdentity { return dbuser.PackageTargetIdentityVip },
		dbuser.MemberRolePremium:   func() dbuser.PackageTargetIdentity { return dbuser.PackageTargetIdentityVip },
		dbuser.MemberRoleFreeTrial: func() dbuser.PackageTargetIdentity { return dbuser.PackageTargetIdentityFreeTrial },
		dbuser.MemberRolePaidAnime: func() dbuser.PackageTargetIdentity { return dbuser.PackageTargetIdentityAnimePass },
		dbuser.MemberRoleExpired: func() dbuser.PackageTargetIdentity {
			if hadSubscribed {
				return dbuser.PackageTargetIdentityExpiredPurchased
			}
			return dbuser.PackageTargetIdentityExpiredNeverPurchased
		},
	}

	var identities []dbuser.PackageTargetIdentity
	for _, ms := range membership {
		f, ok := roleMapping[ms.Role]
		if !ok {
			log.Warn("v4ProductPackageService: getUserTargetIdentities: unmapped role").
				Str("role", string(ms.Role)).Interface("user_id", userID).Send()
			continue
		}
		identities = append(identities, f())
	}

	if userInfo, err := s.legacyHelper.getLegacyUserInfoWithWasPrime(userID); err != nil {
		return nil, err
	} else if userInfo.WasPrime {
		identities = append(identities, dbuser.PackageTargetIdentityPrime)
	}

	return slice.Unique(identities), nil
}

func (s *service) FilterPackages(userID string, pkgs *dbuser.ProductPackages) ([]*FilteredPackage, error) {
	membership := dbuser.NonMember
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, err
	} else if user != nil {
		membership = user.Membership
	}

	userLatestPackageID := null.Int{}
	if membership.IsMember() {
		userLatestPackageID, err = s.getUserLatestPurchasedPackageID(userID)
	}
	if err != nil {
		return nil, err
	}
	var resultPkgs []*FilteredPackage

	hadUserEverSubscribed := userLatestPackageID.Valid
	userIdentities, err := s.getUserIdentities(userID, membership, hadUserEverSubscribed)
	if err != nil {
		return nil, err
	}

	for _, pkg := range *pkgs {
		visibilityCondition := productpackagerule.NewVisibilityCondition(pkg, userIdentities, userLatestPackageID.ValueOrZero())
		if visibilityCondition.IsSatisfied() {
			resultPkgs = append(resultPkgs, &FilteredPackage{
				Package:         pkg,
				SatisfiedTarget: visibilityCondition.SatisfiedTarget,
			})
		}
	}
	sort.Slice(resultPkgs, func(i, j int) bool {
		return resultPkgs[i].Package.Sort < resultPkgs[j].Package.Sort
	})
	return resultPkgs, nil
}

func (s *service) getUserLatestPurchasedPackageID(userID string) (null.Int, error) {
	orders, err := s.billingOrderRepo.ListByUserID(userID)
	if err != nil {
		return null.Int{}, err
	}
	if len(orders) > 0 {
		maxID := orders[0].ID
		pkgID := orders[0].ProductPackageID
		for _, order := range orders {
			if order.ID > maxID {
				maxID = order.ID
				pkgID = order.ProductPackageID
			}
		}
		return null.IntFrom(int64(pkgID)), nil
	}
	return null.Int{}, nil
}
