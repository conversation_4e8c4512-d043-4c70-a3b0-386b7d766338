package productpackage

import (
	"context"
	"database/sql"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/pkg/billing"
	mwmodel "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"gopkg.in/guregu/null.v3"

	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/go-zoo/bone"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

type HandlerTestSuite struct {
	suite.Suite
	r    *require.Assertions
	ctrl *gomock.Controller
	app  *bone.Mux

	mockService          *MockService
	mockCategorySrv      *MockPackageCategoryService
	mockClock            *clock.MockClock
	mockBillingOrderRepo *user.MockPackageBillingOrderRepository
	mockLegacyHelper     *MocklegacyHelper
	mockBillingClient    *billing.MockClient
}

func TestHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(HandlerTestSuite))
}

func (suite *HandlerTestSuite) SetupTest() {
	suite.r = suite.Require()
	suite.app = bone.New()
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockService = NewMockService(suite.ctrl)
	suite.mockCategorySrv = NewMockPackageCategoryService(suite.ctrl)
	suite.mockClock = clock.NewMockClock(suite.ctrl)
	suite.mockBillingOrderRepo = user.NewMockPackageBillingOrderRepository(suite.ctrl)
	suite.mockLegacyHelper = NewMocklegacyHelper(suite.ctrl)

	handler := &Handler{
		service:          suite.mockService,
		billingOrderRepo: suite.mockBillingOrderRepo,
		categorySrv:      suite.mockCategorySrv,
		legacyHelper:     suite.mockLegacyHelper,
	}

	suite.app.GetFunc("/v4/product_packages", handler.GetProductPackages)
}

func (suite *HandlerTestSuite) TearDownTest() {
	defer suite.ctrl.Finish()
}

func (suite *HandlerTestSuite) TestGetProductPackages() {
	var (
		pkgPXpay = dbuser.ProductPackage{
			ID:       2,
			Price:    "1111",
			Duration: "月",
			Platform: "web",
			Sort:     40,
			Category: dbuser.ProductPackageCategory{"單人獨享"},
			Product:  dbuser.Product{Name: "product1", PaymentType: dbuser.PaymentInfoTypePXPayPlus},
		}
		pkgCreditCard = dbuser.ProductPackage{
			ID:       3,
			Price:    "2222",
			Duration: "月",
			Platform: "web",
			Sort:     10,
			Category: dbuser.ProductPackageCategory{"共享"},
			Product: dbuser.Product{
				Name: "product2", PaymentType: dbuser.PaymentInfoTypeCreditCard,
			},
		}
		cate1 = dbuser.PackageCategory{
			Id: 1, General: true, Sort: 1,
			Category: "單人獨享",
		}
		cate2 = dbuser.PackageCategory{
			Id: 2, General: true, Sort: 2,
			Category: "共享",
		}

		platform        = "web"
		billingProducts = billing.Products{
			Map: map[string]billing.Product{
				"KKTV-Prime": {
					Identifier:  "KKTV-Prime",
					Name:        "KKTV Prime",
					PaymentType: "credit_card",
					Recurring:   true,
				},
			},
		}
	)

	pkgCampaign := pkgCreditCard
	pkgCampaign.Platform = "campaign"
	testcases := []struct {
		name   string
		query  string
		access mwmodel.AccessUser
		given  func()
		userID string
		then   func(code int, body []byte)
	}{
		{
			name:   "Retrieve all packages fitting given platform",
			query:  "platform=web",
			access: mwmodel.AccessUser{Memberships: dbuser.NonMember},
			given: func() {
				pkgs := &dbuser.ProductPackages{&pkgPXpay, &pkgCreditCard}
				sortedPkgs := &dbuser.ProductPackages{&pkgCreditCard, &pkgPXpay}
				suite.mockService.EXPECT().ListByPlatform(platform).Return(pkgs, nil)
				suite.mockService.EXPECT().ListBillingByPlatform(platform).Return(nil, nil)

				var filteredPkgs []*FilteredPackage
				for _, pkg := range *sortedPkgs {
					filteredPkgs = append(filteredPkgs, &FilteredPackage{Package: pkg})
				}
				suite.mockService.EXPECT().FilterPackages("", pkgs).Return(filteredPkgs, nil)
				suite.mockService.EXPECT().ListBillingProducts().Return(billingProducts, nil)
				suite.mockCategorySrv.EXPECT().GetByCategories([]string{"共享", "單人獨享"}).
					Return(&dbuser.PackageCategories{&cate1, &cate2}, nil)
				suite.mockService.EXPECT().GetLayoutByPackageIDs([]int{3, 2}).Return(nil, nil)
			},
			then: func(code int, bBytes []byte) {
				suite.Equal(http.StatusOK, code)
				var resp map[string]interface{}
				suite.NoError(json.Unmarshal(bBytes, &resp))
				// check $.data.categories should only have 2 element
				suite.Len(resp["data"].(map[string]interface{})["categories"], 2)
				suite.Len(resp["data"].(map[string]interface{})["product_packages"], 2)
			},
		},
		{
			name:   "given paymentType",
			query:  "platform=web&payment_type=pxpayplus",
			access: mwmodel.AccessUser{UserID: "josie", Memberships: dbuser.MembershipExpired},
			given: func() {
				userInfo := &legacyUserInfo{}
				userInfo.WasPrime = false
				pkgs := &dbuser.ProductPackages{&pkgPXpay, &pkgCreditCard}
				sortedPkgs := &dbuser.ProductPackages{&pkgCreditCard, &pkgPXpay}
				suite.mockService.EXPECT().ListByPlatform(platform).Return(pkgs, nil)
				suite.mockService.EXPECT().ListBillingByPlatform(platform).Return(nil, nil)

				var filteredPkgs []*FilteredPackage
				for _, pkg := range *sortedPkgs {
					filteredPkgs = append(filteredPkgs, &FilteredPackage{Package: pkg})
				}
				suite.mockService.EXPECT().FilterPackages("josie", pkgs).Return(filteredPkgs, nil)
				suite.mockService.EXPECT().ListBillingProducts().Return(billingProducts, nil)
				suite.mockCategorySrv.EXPECT().GetByCategories([]string{"單人獨享"}).
					Return(&dbuser.PackageCategories{&cate1}, nil)
				suite.mockService.EXPECT().GetLayoutByPackageIDs([]int{3, 2}).Return(nil, nil)
			},
			then: func(code int, bBytes []byte) {
				suite.Equal(http.StatusOK, code)
				var resp map[string]interface{}
				suite.NoError(json.Unmarshal(bBytes, &resp))
				suite.Len(resp["data"].(map[string]interface{})["categories"], 1)
				suite.Len(resp["data"].(map[string]interface{})["product_packages"], 1)

			},
		},
		{
			name:  "given invalid platform",
			query: "platform=truncate&payment_type=pxpayplus",
			given: func() {},
			then: func(code int, bBytes []byte) {
				suite.Equal(http.StatusBadRequest, code)
				var resp map[string]interface{}
				suite.NoError(json.Unmarshal(bBytes, &resp))
				suite.Equal("invalid parameter", resp["error"].(map[string]interface{})["message"])
			},
		},
		{
			name:   "Retrieve campaign packages",
			query:  "platform=campaign",
			access: mwmodel.AccessUser{Memberships: dbuser.NonMember},
			given: func() {
				pkgs := &dbuser.ProductPackages{&pkgPXpay, &pkgCampaign}
				sortedPkgs := &dbuser.ProductPackages{&pkgCampaign, &pkgPXpay}
				suite.mockService.EXPECT().ListByPlatform("campaign").Return(pkgs, nil)
				suite.mockService.EXPECT().ListBillingByPlatform("campaign").Return(nil, nil)

				var filteredPkgs []*FilteredPackage
				for _, pkg := range *sortedPkgs {
					filteredPkgs = append(filteredPkgs, &FilteredPackage{Package: pkg})
				}
				suite.mockService.EXPECT().FilterPackages("", pkgs).Return(filteredPkgs, nil)
				suite.mockService.EXPECT().ListBillingProducts().Return(billingProducts, nil)
				suite.mockCategorySrv.EXPECT().GetByCategories([]string{"共享", "單人獨享"}).
					Return(&dbuser.PackageCategories{&cate1, &cate2}, nil)
				layouts := dbuser.PackageLayouts{
					{
						ProductPackagesID: sql.NullInt64{Int64: 3, Valid: true},
						Title:             null.NewString("package-title-3", true),
					},
					{
						ProductPackagesID: sql.NullInt64{Int64: 2, Valid: true},
						Title:             null.NewString("package-title-2", true),
					},
				}
				suite.mockService.EXPECT().GetLayoutByPackageIDs([]int{3, 2}).Return(&layouts, nil)
			},
			then: func(code int, bBytes []byte) {
				suite.Equal(http.StatusOK, code)
				var resp map[string]interface{}
				suite.NoError(json.Unmarshal(bBytes, &resp))
				// check $.data.categories should only have 2 element
				suite.Len(resp["data"].(map[string]interface{})["categories"], 2)
				suite.Len(resp["data"].(map[string]interface{})["product_packages"], 2)
				productpackages := resp["data"].(map[string]interface{})["product_packages"]
				productpackage := productpackages.([]interface{})[0]
				layout := productpackage.(map[string]interface{})["layout"]
				suite.Equal("package-title-3", layout.(map[string]interface{})["title"])
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()
			req := httptest.NewRequest(http.MethodGet, "/v4/product_packages?"+tc.query, nil)
			req = req.WithContext(context.WithValue(req.Context(), middleware.KeyAccessUser, tc.access))
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)
			tc.then(rr.Code, rr.Body.Bytes())
		})
	}
}
