package productpackage

import (
	"testing"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/pkg/billing"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type ProductPackageSrvTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller

	srv                  Service
	MockRepo             *user.MockProductPackageRepository
	mockBillingClient    *billing.MockClient
	mockBillingOrderRepo *user.MockPackageBillingOrderRepository
	mockUserRepo         *user.MockUserRepository
	mockLegacyHelper     *MocklegacyHelper
}

func TestServiceSuite(t *testing.T) {
	suite.Run(t, new(ProductPackageSrvTestSuite))
}

func (suite *ProductPackageSrvTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.MockRepo = user.NewMockProductPackageRepository(suite.ctrl)
	suite.mockBillingClient = billing.NewMockClient(suite.ctrl)
	suite.mockBillingOrderRepo = user.NewMockPackageBillingOrderRepository(suite.ctrl)
	suite.mockUserRepo = user.NewMockUserRepository(suite.ctrl)
	suite.mockLegacyHelper = NewMocklegacyHelper(suite.ctrl)

	suite.srv = &service{
		repo:             suite.MockRepo,
		billingClient:    suite.mockBillingClient,
		billingOrderRepo: suite.mockBillingOrderRepo,
		userRepo:         suite.mockUserRepo,
		legacyHelper:     suite.mockLegacyHelper,
	}
}

func (suite *ProductPackageSrvTestSuite) TearDownTest() {
	defer suite.ctrl.Finish()
}

func (suite *ProductPackageSrvTestSuite) TestListByPlatform() {
	var (
		webPlatform = "web"
		pdpkgs      = &dbuser.ProductPackages{
			&dbuser.ProductPackage{
				ID:       36,
				Price:    "1111",
				Duration: "月",
				Platform: "web",
			},
			&dbuser.ProductPackage{
				ID:       100,
				Price:    "2222",
				Duration: "月",
				Platform: "web",
			},
		}
	)
	testcases := []struct {
		name     string
		given    func()
		assert   func(*dbuser.ProductPackages, error)
		platform string
	}{
		{
			name: "Given web platform then retrieve web packages",
			given: func() {
				suite.MockRepo.EXPECT().ListByPlatform(webPlatform).Return(pdpkgs, nil)
			},
			assert: func(pp *dbuser.ProductPackages, err error) {
				suite.NoError(err)
				suite.Equal(pdpkgs, pp)
			},
			platform: webPlatform,
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()
			pp, err := suite.srv.ListByPlatform(tc.platform)
			tc.assert(pp, err)
		})
	}
}

func (suite *ProductPackageSrvTestSuite) TestGetBillingByPlatform() {
	var (
		webPlatform = "web"
		billpkgs    = &dbuser.ProductPackages{
			&dbuser.ProductPackage{
				ID:       36,
				Price:    "1111",
				Duration: "月",
				Platform: "web",
				BillingProductIds: &dbuser.ProductPackageProductIDs{
					"101",
				},
			},
			&dbuser.ProductPackage{
				ID:       100,
				Price:    "2222",
				Duration: "月",
				Platform: "web",
				BillingProductIds: &dbuser.ProductPackageProductIDs{
					"102",
				},
			},
		}
	)
	testcases := []struct {
		name     string
		given    func()
		assert   func(*dbuser.ProductPackages, error)
		platform string
	}{
		{
			name: "Given web platform then retrieve billing web packages",
			given: func() {
				suite.MockRepo.EXPECT().ListBillingPkgsByPlatform(webPlatform).Return(billpkgs, nil)
			},
			assert: func(pp *dbuser.ProductPackages, err error) {
				suite.NoError(err)
				suite.Equal(billpkgs, pp)
			},
			platform: webPlatform,
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()
			pp, err := suite.srv.ListBillingByPlatform(tc.platform)
			tc.assert(pp, err)
		})
	}
}

func (suite *ProductPackageSrvTestSuite) TestFilterPackages() {
	testcases := []struct {
		name   string
		given  func()
		pkgs   *dbuser.ProductPackages
		userID string
		assert func([]*FilteredPackage)
	}{
		{
			name: "Given user has subscribed and latest pkg id is 1, then return pkg id 1",
			given: func() {
				suite.mockUserRepo.EXPECT().GetByID("test-user-id").Return(&dbuser.User{
					Membership: dbuser.MembershipPremiumOnly,
				}, nil)

				orders := []*dbuser.PackageBillingOrder{
					{ID: 1, ProductPackageID: 1},
				}
				suite.mockBillingOrderRepo.EXPECT().ListByUserID("test-user-id").Return(orders, nil)
				suite.mockLegacyHelper.EXPECT().getLegacyUserInfoWithWasPrime("test-user-id").Return(&legacyUserInfo{}, nil)
			},
			pkgs: &dbuser.ProductPackages{
				&dbuser.ProductPackage{
					ID: 1,
					Targets: &dbuser.PackageTargets{
						{
							Condition: dbuser.PackageTargetCondition{
								Identities: []string{"vip", "expired.purchased"},
								LatestPackages: &dbuser.PackageTargetLatestPackages{
									Operator: "include",
									IDs:      []int64{1, 2},
								},
							},
						},
					},
				},
				&dbuser.ProductPackage{
					ID: 2,
					Targets: &dbuser.PackageTargets{
						{
							Condition: dbuser.PackageTargetCondition{
								Identities: []string{"prime"},
								LatestPackages: &dbuser.PackageTargetLatestPackages{
									Operator: "exclude",
									IDs:      []int64{1},
								},
							},
						},
					},
				},
			},
			userID: "test-user-id",
			assert: func(pkgs []*FilteredPackage) {
				suite.Equal(len(pkgs), 1)
				suite.Equal(pkgs[0].Package.ID, 1)
			},
		},
		{
			name: "Given expired user has subscribed and latest pkg id is 1, then return pkg id 1",
			given: func() {
				suite.mockUserRepo.EXPECT().GetByID("test-user-id").Return(&dbuser.User{
					Membership: dbuser.MembershipExpired,
				}, nil)
				orders := []*dbuser.PackageBillingOrder{
					{ID: 1, ProductPackageID: 1},
				}
				suite.mockBillingOrderRepo.EXPECT().ListByUserID("test-user-id").Return(orders, nil)
				suite.mockLegacyHelper.EXPECT().getLegacyUserInfoWithWasPrime("test-user-id").Return(&legacyUserInfo{}, nil)
			},
			pkgs: &dbuser.ProductPackages{
				&dbuser.ProductPackage{
					ID: 1,
					Targets: &dbuser.PackageTargets{
						{
							Condition: dbuser.PackageTargetCondition{
								Identities: []string{"vip", "expired.purchased"},
								LatestPackages: &dbuser.PackageTargetLatestPackages{
									Operator: "include",
									IDs:      []int64{1, 2},
								},
							},
						},
					},
				},
				&dbuser.ProductPackage{
					ID: 2,
					Targets: &dbuser.PackageTargets{
						{
							Condition: dbuser.PackageTargetCondition{
								Identities: []string{"expired.never_purchased"},
								LatestPackages: &dbuser.PackageTargetLatestPackages{
									Operator: "include",
									IDs:      []int64{1},
								},
							},
						},
					},
				},
			},
			userID: "test-user-id",
			assert: func(pkgs []*FilteredPackage) {
				suite.Equal(len(pkgs), 1)
				suite.Equal(pkgs[0].Package.ID, 1)
			},
		},
		{
			name: "Given guest user hasn't subscribed, then return pkg id 1",
			given: func() {
				suite.mockUserRepo.EXPECT().GetByID("test-user-id").Return(&dbuser.User{
					Membership: dbuser.NonMember,
				}, nil)
			},
			pkgs: &dbuser.ProductPackages{
				&dbuser.ProductPackage{
					ID: 1,
					Targets: &dbuser.PackageTargets{
						{
							Condition: dbuser.PackageTargetCondition{
								Identities: []string{"guest"},
								LatestPackages: &dbuser.PackageTargetLatestPackages{
									Operator: "exclude",
									IDs:      []int64{1, 2},
								},
							},
						},
					},
				},
				&dbuser.ProductPackage{
					ID: 2,
					Targets: &dbuser.PackageTargets{
						{
							Condition: dbuser.PackageTargetCondition{
								Identities: []string{"expired.never_purchased"},
								LatestPackages: &dbuser.PackageTargetLatestPackages{
									Operator: "include",
									IDs:      []int64{1},
								},
							},
						},
					},
				},
			},
			userID: "test-user-id",
			assert: func(pkgs []*FilteredPackage) {
				suite.Equal(len(pkgs), 1)
				suite.Equal(pkgs[0].Package.ID, 1)
			},
		},
		{
			name: "Given expired user had never been subscribed, the only pkg target for expired.purchased, THEN got no packages",
			given: func() {
				suite.mockUserRepo.EXPECT().GetByID("test-user-id").Return(&dbuser.User{
					Membership: dbuser.MembershipExpired,
				}, nil)
				suite.mockBillingOrderRepo.EXPECT().ListByUserID("test-user-id").Return([]*dbuser.PackageBillingOrder{}, nil)
				suite.mockLegacyHelper.EXPECT().getLegacyUserInfoWithWasPrime("test-user-id").Return(&legacyUserInfo{}, nil)
			},
			pkgs: &dbuser.ProductPackages{
				&dbuser.ProductPackage{
					ID: 1,
					Targets: &dbuser.PackageTargets{
						{
							Condition: dbuser.PackageTargetCondition{
								Identities: []string{"expired.purchased"},
							},
						},
					},
				},
			},
			userID: "test-user-id",
			assert: func(pkgs []*FilteredPackage) {
				suite.Equal(len(pkgs), 0)
			},
		},
		{
			name: `Given freetrial user and two packages,
			one of which has expired.purchased target,
			the other has no target,
			THEN got package without target`,
			given: func() {
				suite.mockUserRepo.EXPECT().GetByID("test-user-id").Return(&dbuser.User{
					Membership: dbuser.MembershipFreeTrial,
				}, nil)
				suite.mockBillingOrderRepo.EXPECT().ListByUserID("test-user-id").Return([]*dbuser.PackageBillingOrder{}, nil)
				suite.mockLegacyHelper.EXPECT().getLegacyUserInfoWithWasPrime("test-user-id").Return(&legacyUserInfo{}, nil)
			},
			pkgs: &dbuser.ProductPackages{
				&dbuser.ProductPackage{
					ID: 1,
					Targets: &dbuser.PackageTargets{
						{
							Condition: dbuser.PackageTargetCondition{
								Identities: []string{"expired.purchased"},
							},
						},
					},
				},
				&dbuser.ProductPackage{
					ID:      2,
					Targets: &dbuser.PackageTargets{},
				},
			},
			userID: "test-user-id",
			assert: func(pkgs []*FilteredPackage) {
				suite.Equal(len(pkgs), 1)
				suite.Equal(pkgs[0].Package.ID, 2)
			},
		},
		{
			name: `Given freetrial user and two packages,
			one of which has bigger sort value,
			the other has smaller sort value,
			THEN got package with correct sort`,
			given: func() {
				suite.mockUserRepo.EXPECT().GetByID("test-user-id").Return(&dbuser.User{
					Membership: dbuser.MembershipFreeTrial,
				}, nil)
				suite.mockBillingOrderRepo.EXPECT().ListByUserID("test-user-id").Return([]*dbuser.PackageBillingOrder{}, nil)
				suite.mockLegacyHelper.EXPECT().getLegacyUserInfoWithWasPrime("test-user-id").Return(&legacyUserInfo{}, nil)
			},
			pkgs: &dbuser.ProductPackages{
				&dbuser.ProductPackage{
					ID: 1,
					Targets: &dbuser.PackageTargets{
						{
							Condition: dbuser.PackageTargetCondition{
								Identities: []string{"freetrial"},
							},
						},
					},
					Sort: 5,
				},
				&dbuser.ProductPackage{
					ID:      2,
					Targets: &dbuser.PackageTargets{},
					Sort:    2,
				},
			},
			userID: "test-user-id",
			assert: func(pkgs []*FilteredPackage) {
				suite.Equal(len(pkgs), 2)
				suite.Equal(pkgs[0].Package.ID, 2)
				suite.Equal(pkgs[1].Package.ID, 1)
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()
			pkgs, _ := suite.srv.FilterPackages(tc.userID, tc.pkgs)
			tc.assert(pkgs)
		})
	}
}
