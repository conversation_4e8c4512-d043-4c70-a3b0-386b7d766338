//go:generate mockgen -source legacy.go -destination legacy_mock.go -package productpackage
package productpackage

import (
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
)

type legacyHelper interface {
	getLegacyUserInfoWithWasPrime(userID string) (*legacyUserInfo, error)
}

type legacyHelp struct {
}

type legacyUserInfo struct {
	model.UserInfo
}

func (l *legacyHelp) getLegacyUserInfoWithWasPrime(userID string) (*legacyUserInfo, error) {
	var (
		err      error
		userInfo model.UserInfo
	)

	if userInfo, err = model.NewUserInfo(userID, container.DBPoolUser().Slave()); err != nil {
		return nil, err
	}

	userInfo.SetWasPrime()

	return &legacyUserInfo{userInfo}, nil
}
