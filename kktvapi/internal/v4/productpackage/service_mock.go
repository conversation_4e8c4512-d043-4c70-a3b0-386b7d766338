// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package productpackage is a generated GoMock package.
package productpackage

import (
	reflect "reflect"

	billing "github.com/KKTV/kktv-api-v3/pkg/billing"
	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// FilterPackages mocks base method.
func (m *MockService) FilterPackages(userID string, pkgs *dbuser.ProductPackages) ([]*FilteredPackage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FilterPackages", userID, pkgs)
	ret0, _ := ret[0].([]*FilteredPackage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FilterPackages indicates an expected call of FilterPackages.
func (mr *MockServiceMockRecorder) FilterPackages(userID, pkgs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FilterPackages", reflect.TypeOf((*MockService)(nil).FilterPackages), userID, pkgs)
}

// GetLayoutByPackageIDs mocks base method.
func (m *MockService) GetLayoutByPackageIDs(pkgIDs []int) (*dbuser.PackageLayouts, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLayoutByPackageIDs", pkgIDs)
	ret0, _ := ret[0].(*dbuser.PackageLayouts)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLayoutByPackageIDs indicates an expected call of GetLayoutByPackageIDs.
func (mr *MockServiceMockRecorder) GetLayoutByPackageIDs(pkgIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLayoutByPackageIDs", reflect.TypeOf((*MockService)(nil).GetLayoutByPackageIDs), pkgIDs)
}

// ListBillingByPlatform mocks base method.
func (m *MockService) ListBillingByPlatform(platform string) (*dbuser.ProductPackages, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBillingByPlatform", platform)
	ret0, _ := ret[0].(*dbuser.ProductPackages)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBillingByPlatform indicates an expected call of ListBillingByPlatform.
func (mr *MockServiceMockRecorder) ListBillingByPlatform(platform interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBillingByPlatform", reflect.TypeOf((*MockService)(nil).ListBillingByPlatform), platform)
}

// ListBillingProducts mocks base method.
func (m *MockService) ListBillingProducts() (billing.Products, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBillingProducts")
	ret0, _ := ret[0].(billing.Products)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBillingProducts indicates an expected call of ListBillingProducts.
func (mr *MockServiceMockRecorder) ListBillingProducts() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBillingProducts", reflect.TypeOf((*MockService)(nil).ListBillingProducts))
}

// ListByPlatform mocks base method.
func (m *MockService) ListByPlatform(platform string) (*dbuser.ProductPackages, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByPlatform", platform)
	ret0, _ := ret[0].(*dbuser.ProductPackages)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByPlatform indicates an expected call of ListByPlatform.
func (mr *MockServiceMockRecorder) ListByPlatform(platform interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByPlatform", reflect.TypeOf((*MockService)(nil).ListByPlatform), platform)
}
