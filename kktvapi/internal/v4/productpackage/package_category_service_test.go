package productpackage

import (
	"testing"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type PackageCategorySrvTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller

	srv      PackageCategoryService
	MockRepo *user.MockPackageCategoryRepository
}

func TestServiceTestSuite(t *testing.T) {
	suite.Run(t, new(PackageCategorySrvTestSuite))
}

func (suite *PackageCategorySrvTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.MockRepo = user.NewMockPackageCategoryRepository(suite.ctrl)

	suite.srv = &packageCategoryService{
		Repo: suite.MockRepo,
	}
}

func (suite *PackageCategorySrvTestSuite) TearDownTest() {
	defer suite.ctrl.Finish()
}

func (suite *PackageCategorySrvTestSuite) TestGetByCategories() {
	var (
		packageCategories = &dbuser.PackageCategories{
			&dbuser.PackageCategory{
				Id:       1,
				Category: "單人獨享",
				General:  true,
				Sort:     1,
			},
		}
		param = []string{
			"單人獨享",
		}
	)
	testcases := []struct {
		name   string
		given  func()
		params []string
		assert func(*dbuser.PackageCategories, error)
	}{
		{
			name: "Given categories, then retrieve full and correct categories",
			given: func() {
				suite.MockRepo.EXPECT().GetByCategories(param).Return(packageCategories, nil)
			},
			assert: func(categories *dbuser.PackageCategories, err error) {
				suite.NoError(err)
				suite.Equal(categories, packageCategories)
			},
			params: param,
		},
		{
			name: "Given nil, then retrieve full and correct categories",
			given: func() {
				suite.MockRepo.EXPECT().GetByCategories(nil).Return(
					nil, nil)
			},
			assert: func(categories *dbuser.PackageCategories, err error) {
				suite.NoError(err)
			},
			params: nil,
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()
			categories, err := suite.srv.GetByCategories(tc.params)
			tc.assert(categories, err)
		})
	}
}
