package productpackage

import (
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

var (
	categorySrv     PackageCategoryService
	onceCategorySrv sync.Once
)

type PackageCategoryService interface {
	GetByCategories(categories []string) (*dbuser.PackageCategories, error)
}

type packageCategoryService struct {
	Repo user.PackageCategoryRepository
}

func NewPackageCategoryService() PackageCategoryService {
	onceCategorySrv.Do(func() {
		categorySrv = &packageCategoryService{
			Repo: user.NewPackageCategoryRepository(),
		}
	})
	return categorySrv
}

func (s *packageCategoryService) GetByCategories(categories []string) (*dbuser.PackageCategories, error) {
	return s.Repo.GetByCategories(categories)
}
