//go:generate mockgen -source legacy.go -destination legacy_mock.go -package seo
package seo

import (
	"errors"

	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
)

type legacyHelper interface {
	GetTitleByID(titleID string) (*legacyTitleMeta, error)
}

type legacyTitleMeta struct {
	meta *dbmeta.TitleMeta
}

type legacy struct {
	cacheReader cache.Cacher
}

func (l *legacy) GetTitleByID(titleID string) (*legacyTitleMeta, error) {
	record := new(dbmeta.TitleMeta)
	if err := l.cacheReader.HGet(key.TitleDetail(titleID), "whole", record); errors.Is(err, cache.ErrCacheMiss) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return &legacyTitleMeta{meta: record}, nil
}
