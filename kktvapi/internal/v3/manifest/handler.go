package manifest

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/meta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	v4rest "github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v3/internal/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/feature"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/go-zoo/bone"
)

const (
	bvDRMRequestHeaderDeviceID = "X-Device-Id"
	bvDRMRequestHeaderTenantID = "X-KK-Tenant-Id"
)

type Handler struct {
	legacy            LegacyHelper
	metaCacheReader   cache.Cacher
	featureService    feature.Service
	episodeRepo       meta.EpisodeRepository
	permissionService permission.Service
	titleRepo         meta.TitleRepository
}

func NewHandler() *Handler {
	metaCacheReader := cache.New(container.CachePoolMeta().Slave())
	return &Handler{
		episodeRepo:       meta.NewEpisodeRepository(),
		metaCacheReader:   metaCacheReader,
		legacy:            NewLegacyHelper(clock.New(), config.TheaterCDNSignKey, metaCacheReader),
		featureService:    feature.NewService(),
		permissionService: container.PermissionService(),
		titleRepo:         meta.NewTitleRepository(),
	}
}

func (h *Handler) GetManifest(w http.ResponseWriter, r *http.Request) {
	// parse path params: episodeids
	titleID := bone.GetValue(r, "titleid")
	epIDs := strings.Split(bone.GetValue(r, "episodeids"), ",")
	platformHeader := httpreq.GetPlatform(r)
	if len(epIDs) == 0 {
		render.JSON(w, http.StatusBadRequest, rest.Resp{Status: rest.Status{
			Type: "BadRequest",
		}})
		return
	}

	access, ok := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	if !ok {
		render.JSON(w, http.StatusUnauthorized, rest.Resp{Status: rest.Status{
			Type: "Unauthorized",
		}})
	}

	// parse query params: quality, purpose, medium, device, subtitles
	urlValues, err := httpreq.ParseQuery(r)
	if err != nil {
		render.JSON(w, http.StatusOK, rest.Ok())
		return
	}

	var withSubTitle bool
	quality := urlValues.Get("quality")
	device := urlValues.Get("device")
	subtitle := urlValues.Get("subtitles")
	purpose := urlValues.Get("purpose")
	medium := urlValues.Get("medium")
	withSubTitle, purpose, medium = processManifestParameter(subtitle, purpose, medium)
	var isFullAccess bool

	if !isExtraTitle(titleID) { // need to check access right if not extra title
		for _, epID := range epIDs {
			if len(epID) != 14 {
				render.JSON(w, http.StatusBadRequest, rest.Resp{Status: rest.Status{
					Type: "BadRequest", Message: fmt.Sprintf("invalid episode id: %s", epID),
				}})
				return
			}

			ep, err := h.legacy.GetEpisodeByID(epID)
			if err != nil {
				plog.Error("v3 manifest handler: get episode by id").Err(err).Str("ep_id", epID).Send()
				render.JSON(w, http.StatusInternalServerError, rest.Resp{Status: rest.Status{Type: "InternalServerError", Message: "unknown error"}})
				return
			} else if ep == nil {
				render.JSON(w, http.StatusNotFound, rest.Resp{Status: rest.Status{Type: "NotFound", Message: fmt.Sprintf("episode %s not found", epID)}})
				return
			}

			titleDetails, err := h.titleRepo.ListViewableTitleDetailWithoutSeries([]string{titleID}, true)
			if err != nil {
				plog.Error("v3ManifestHandler: GetManifest: titleRepo fail to ListViewableTitleDetailWithoutSeries").
					Err(err).
					Str("titleID", titleID).
					Str("userID", access.UserID).
					Send()
				render.JSON(w, http.StatusInternalServerError, rest.Resp{Status: rest.Status{Type: "Internal server error", Message: "unknown error"}})
				return
			} else if len(titleDetails) == 0 {
				render.JSON(w, http.StatusNotFound, rest.Resp{Status: rest.Status{Type: "NotFound", Message: fmt.Sprintf("title %s detail not found", titleID)}})
				return
			}

			if err := h.permissionService.Grant(permission.RequestFullAccessTitleDetail(
				titleDetails[0], access.Memberships,
			)); kktverror.IsInternalErr(err) {
				plog.Error("v3ManifestHandler: permissionService: RequestFullAccessTitleDetail").
					Err(err).
					Str("titleID", titleID).
					Str("userID", access.UserID).
					Send()
				render.JSON(w, http.StatusInternalServerError, rest.Resp{Status: rest.Status{Type: "Internal server error", Message: "unknown error"}})
				return
			} else {
				isFullAccess = err == nil
			}

			if err := h.permissionService.Grant(permission.RequestPlayEpisode(ep, isFullAccess)); kktverror.IsInternalErr(err) {
				plog.Error("v3ManifestHandler: GetManifest: permissionService fail to grant RequestPlayEpisode").
					Err(err).
					Interface("episode", ep).
					Str("userID", access.UserID).
					Bool("isFullAccess", isFullAccess).
					Send()
				render.JSON(w, http.StatusInternalServerError, rest.Resp{Status: rest.Status{Type: "Internal server error", Message: "unknown error"}})
				return
			} else if err != nil {
				render.JSON(w, http.StatusForbidden,
					rest.Resp{Status: rest.Status{
						Type:    "Forbidden",
						Message: fmt.Sprintf("episode %s is not accessible", epID)}})
				return
			}
		}
	} else {
		if err := h.permissionService.Grant(permission.RequestFullAccessExtraTitle(access.Memberships)); kktverror.IsInternalErr(err) {
			plog.Error("v3ManifestHandler: GetManifest: permissionService fail to grant RequestFullAccessExtraTitle").
				Interface("membership", access.Memberships).Err(err).Send()
			render.JSON(w, http.StatusInternalServerError, rest.Resp{Status: rest.Status{Type: "Internal server error", Message: "unknown error"}})
			return
		} else {
			isFullAccess = err == nil
		}
	}

	hasAdvancedPlayerFunction, err := h.permissionService.HasAdvancedPlayerFunction(access.Memberships)
	if err != nil {
		plog.Error("v3ManifestHandler: GetManifest: permissionService fail to grant RequestAdvancedPlayerFunction").
			Interface("membership", access.Memberships).Err(err).Send()
		render.JSON(w, http.StatusInternalServerError, rest.Resp{Status: rest.Status{Type: "Internal server error", Message: "unknown error"}})
		return
	}

	if hasAdvancedPlayerFunction || isFullAccess {
		quality = decideQualityByManifestParam(device, medium, quality)
	} else {
		quality = decideQualityByDevice(device)
	}

	resp := rest.Ok()
	resp.Data = manifestsResp{}
	// get manifests from legacyHelper
	manifests, err := h.legacy.GetManifests(epIDs, device, quality, purpose, platformHeader, withSubTitle)
	if err != nil {
		plog.Warn("v3 manifest handler: get manifest: legacyHelper get manifest fails").Err(err).
			Str("user", access.UserID).Strs("ids", epIDs).Interface("membership", access.Memberships).
			Str("device", device).Str("quality", quality).Str("purpose", purpose).
			Str("medium", medium).Bool("withSubTitle", withSubTitle).Send()
		render.JSON(w, http.StatusInternalServerError, resp)
		return
	} else if len(manifests) == 0 {
		render.JSON(w, http.StatusNotFound, rest.RespNotFound())
		return
	}

	m := manifestsResp{
		Episodes: manifests,
	}

	licenseURLs := map[string]string{
		"playready": config.LicenseUrlPlayready,
		"widevine":  config.LicenseUrlWidevine,
		"fairplay":  config.LicenseUrlFairplay,
	}

	if useBv, err := h.featureService.HasFlag(feature.FlagUseBvDRM, r); err != nil {
		plog.Warn("v3 manifest handler: get manifest: call featureService.HasFlagUseBvDRM fail").Err(err).Send()
	} else if useBv {
		// replace the license url with BV DRM server url
		licenseURLs["playready"] = config.KKSBVLicenseUrl
		licenseURLs["widevine"] = config.KKSBVLicenseUrl
		licenseURLs["fairplay"] = config.KKSBVLicenseUrl
		// add headers for BV DRM server
		deviceID := r.Header.Get(httpreq.HeaderDeviceID)
		if headers, err := h.getLicenseAdditionalHeadersForBV(deviceID); err != nil {
			plog.Warn("v3 manifest handler: get manifest: fail to get license additional headers").Err(err).Send()
		} else {
			m.LicenseHeaders = headers
		}
	}
	m.LicenseURL = licenseURLs
	resp.Data = m
	render.JSON(w, http.StatusOK, resp)

	// TODO remove logging after BV DRM server is stable and remove the feature flag
	plog.Info("manifest: return license url").
		Interface("resp.license_url", m.LicenseURL).
		Interface("resp.license_headers", m.LicenseHeaders).
		Str("resp.user", access.UserID).Strs("ids", epIDs).
		Str("resp.hls_url", manifests[0].Hls.URL).
		Str("resp.dash_url", manifests[0].Dash.URL).
		Str("resp.thumbnail_url", manifests[0].ThumbnailURL).
		Interface("resp.subtitle_urls", manifests[0].SubtitleURL).
		Interface("req.headers", r.Header).Send()
}

func isExtraTitle(id string) bool {
	return strings.HasSuffix(id, "extra")
}

func decideQualityByDevice(device string) string {
	if device == "web" {
		return "low"
	}
	return "medium"
}

func decideQualityByManifestParam(device, medium, quality string) string {
	if device != "web" && medium == "AVOD" && quality != "playzone" {
		quality = "medium"
	}
	return quality
}

func processManifestParameter(paramSubtitle, paramPurpose, paramMedium string) (withSubtitle bool, purpose, medium string) {
	withSubtitle = true
	purpose = paramPurpose
	if paramSubtitle == "0" {
		withSubtitle = false
	}
	if paramPurpose != "download" {
		purpose = "playback"
	}
	if paramMedium == "linear" || paramMedium == "AVOD" {
		medium = "AVOD"
	} else {
		medium = "SVOD"
	}
	return
}

func (h *Handler) getLicenseAdditionalHeadersForBV(deviceID string) ([]headerItem, error) {
	headers := make(map[string]string)
	if err := h.metaCacheReader.HGet(key.MetaGetServiceGeneralConfig(), key.MetaServiceGeneralConfigHashKeys.LicenseRequestHeaders, &headers); err != nil {
		return nil, err
	}
	returnHeaders := make([]headerItem, 0, len(headers))
	for k, v := range headers {
		switch k {
		case bvDRMRequestHeaderDeviceID:
			if deviceID == "" {
				continue
			}
			v = deviceID
		case bvDRMRequestHeaderTenantID:
			v = config.KKSBVTenantID
		}
		returnHeaders = append(returnHeaders, headerItem{
			Key:   k,
			Value: v,
		})
	}
	return returnHeaders, nil
}

// TODO implement me
func (h *Handler) GetTrialEpisodeManifest(w http.ResponseWriter, r *http.Request) {
	const purpose = "playback" // only support playback by now
	platform := httpreq.GetPlatform(r)

	// parse query params: quality
	urlValues, err := httpreq.ParseQuery(r)
	if err != nil {
		render.JSONInternalServerErr(w, v4rest.Error("Invalid query parameters", "400.0"))
		return
	}
	quality := urlValues.Get("quality")
	if quality == "" {
		quality = "medium"
	}

	// [BEGIN] TODO to achieve real info
	device := "web"
	epID := "01000749010001"
	// [END]

	manifests, err := h.legacy.GetManifests([]string{epID}, device, quality, purpose, platform, true)
	if err != nil {
		render.JSONInternalServerErr(w, v4rest.Error("Unknown error", "500.0"))
		return
	} else if len(manifests) == 0 {
		render.JSONNotFound(w, v4rest.Error("Episode not found", "404.1"))
		return
	}

	mf := manifests[0]
	mockData := trialManifestsResp{
		Episode: trialMezzanine{
			EpisodeID:       epID,
			DefaultSubtitle: "zh-Hant",
			SubtitleURL:     mf.SubtitleURL,
			ThumbnailURL:    mf.ThumbnailSmallURL,
			Dash:            mf.Dash,
			Hls:             mf.Hls,
		},
		LicenseURL: map[string]string{
			"playready":     config.KKSBVLicenseUrl,
			"widevine":      config.KKSBVLicenseUrl,
			"fairplay":      config.LicenseUrlFairplay,
			"fairplay_cert": config.LicenseUrlFairplay + "/fairplay_cert",
		},
		LicenseHeaders: []headerItem{
			{Key: "X-KK-Tenant-Id", Value: "00037e2b-b3fd-4d44-a3cb-4f227a518142", For: []string{"playready", "widevine"}},
		},
	}
	resp := v4rest.Ok()
	resp.Data = mockData
	render.JSON(w, http.StatusOK, resp)
}
