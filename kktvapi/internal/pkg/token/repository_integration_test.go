package token

import (
	_ "embed"
	"os"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/dbtest"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/rand"
	"github.com/golang/mock/gomock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gopkg.in/guregu/null.v3"
)

type TokenRepositoryTestSuite struct {
	dbtest.Suite
	r    *require.Assertions
	ctrl *gomock.Controller

	mockClock  *clock.MockClock
	mockRandom *rand.MockRand
	repo       Repository
}

func TestTokenRepositoryIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(TokenRepositoryTestSuite))
}

func (suite *TokenRepositoryTestSuite) SetupTest() {
	suite.Suite.SetupTest()
	tx := suite.GetTransaction()

	suite.ctrl = gomock.NewController(suite.T())
	suite.mockClock = clock.NewMockClock(suite.ctrl)
	suite.mockRandom = rand.NewMockRand(suite.ctrl)

	suite.repo = &repository{
		dbReader: tx,
		dbWriter: tx,
		clock:    suite.mockClock,
		random:   suite.mockRandom,
	}

	suite.r = suite.Require()
}

func (suite *TokenRepositoryTestSuite) TearDownTest() {
	suite.Suite.TearDownTest()
}

func (suite *TokenRepositoryTestSuite) SetupSuite() {
	suite.Suite.SetupSuite(os.Getenv("TEST_DB_USER_DSN"))
}

func init() {
	_ = os.Setenv("TEST_DB_USER_DSN", "postgres://foo:bar@localhost:5432/kktv_users?sslmode=disable")
}

func (suite *TokenRepositoryTestSuite) TestWithTx() {
	suite.Run("get token repo with tx", func() {
		suite.r.Implements((*Repository)(nil), suite.repo.WithTx(&sqlx.Tx{}))
	})
}

func (suite *TokenRepositoryTestSuite) TestCreate() {
	suite.givenUsers()

	now := time.Now()
	uid := "42096b23-790c-4bea-8423-aaad7dd0c43d"
	token := &dbuser.Token{
		UserID:    "03de5dafba5ae07cb2ea0a85b950f27eeb205ff75b4061c2f182f5f268926e72",
		Token:     "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfcmFuZG9tIjoiY2Rvdm5kaTdxbzBraWltZjh0MjAiLCJhdWQiOiJra3R2LmNvbSIsImV4cCI6MTY3MTAwNjM5MCwiaWF0IjoxNjY4NDE0MzkwLCJpc3MiOiJLS1RWIiwicm9sZSI6ImV4cGlyZWQiLCJzdWIiOiIwM2RlNWRhZmJhNWFlMDdjYjJlYTBhODViOTUwZjI3ZWViMjA1ZmY3NWI0MDYxYzJmMTgyZjVmMjY4OTI2ZTcyIiwidHlwZSI6ImdlbmVyYWwifQ.m0tfTbXqM1fOxqTU7rpVxPz2K24aO_X3kWOCKVIVRlo",
		SourceIP:  "127.0.0.1",
		UserAgent: "Apache-HttpClient/4.5.13 (Java/17.0.3)",
		ExpiredAt: null.TimeFrom(now),
	}

	testcases := []struct {
		name  string
		token *dbuser.Token
		then  func(created *dbuser.Token, err error)
	}{
		{
			name:  "create token successfully",
			token: token,
			then: func(createdToken *dbuser.Token, err error) {
				suite.r.NoError(err)
				suite.NotNil(createdToken)
				suite.r.Equal(token.UserID, createdToken.UserID)
				suite.r.Equal(token.Token, createdToken.Token)
			},
		},
		{
			name: "create token failed due to required fields are missing",
			token: &dbuser.Token{
				Token:     "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfcmFuZG9tIjoiY2Rvdm5kaTdxbzBraWltZjh0MjAiLCJhdWQiOiJra3R2LmNvbSIsImV4cCI6MTY3MTAwNjM5MCwiaWF0IjoxNjY4NDE0MzkwLCJpc3MiOiJLS1RWIiwicm9sZSI6ImV4cGlyZWQiLCJzdWIiOiIwM2RlNWRhZmJhNWFlMDdjYjJlYTBhODViOTUwZjI3ZWViMjA1ZmY3NWI0MDYxYzJmMTgyZjVmMjY4OTI2ZTcyIiwidHlwZSI6ImdlbmVyYWwifQ.m0tfTbXqM1fOxqTU7rpVxPz2K24aO_X3kWOCKVIVRlo",
				UserAgent: "Apache-HttpClient/4.5.13 (Java/17.0.3)",
				ExpiredAt: null.TimeFrom(now),
			},
			then: func(createdToken *dbuser.Token, err error) {
				suite.r.Error(err)
				suite.Nil(createdToken)
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			suite.mockClock.EXPECT().Now().Return(now)
			suite.mockRandom.EXPECT().UUID().Return(uid)
			tc.then(suite.repo.Create(tc.token))
		})
	}

}

//go:embed testdata/repository/Repo.sql
var dataForRepo string

func (suite *TokenRepositoryTestSuite) givenUsers() {
	if _, err := suite.GetTransaction().Exec(dataForRepo); err != nil {
		suite.Require().Fail("fail to insert test data", err)
	}
}
