// Code generated by MockGen. DO NOT EDIT.
// Source: kktvapi/internal/pkg/token/repository.go

// Package token is a generated GoMock package.
package token

import (
	reflect "reflect"

	database "github.com/KKTV/kktv-api-v3/pkg/database"
	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRepository) Create(token *dbuser.Token) (*dbuser.Token, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", token)
	ret0, _ := ret[0].(*dbuser.Token)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRepositoryMockRecorder) Create(token interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRepository)(nil).Create), token)
}

// GetByTokenHashAndUserID mocks base method.
func (m *MockRepository) GetByTokenHashAndUserID(tokenHash, userID string) (*dbuser.Token, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByTokenHashAndUserID", tokenHash, userID)
	ret0, _ := ret[0].(*dbuser.Token)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByTokenHashAndUserID indicates an expected call of GetByTokenHashAndUserID.
func (mr *MockRepositoryMockRecorder) GetByTokenHashAndUserID(tokenHash, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByTokenHashAndUserID", reflect.TypeOf((*MockRepository)(nil).GetByTokenHashAndUserID), tokenHash, userID)
}

// WithTx mocks base method.
func (m *MockRepository) WithTx(tx database.Tx) Repository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(Repository)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockRepositoryMockRecorder) WithTx(tx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockRepository)(nil).WithTx), tx)
}
