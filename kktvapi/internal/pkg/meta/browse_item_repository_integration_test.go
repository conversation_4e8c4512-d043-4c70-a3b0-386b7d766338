package meta

import (
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	"github.com/stretchr/testify/suite"
)

func TestBrowseItemRepositoryIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(BrowseItemRepositoryIntegrationTestSuite))
}

// BrowseItemRepositoryIntegrationTestSuite
type BrowseItemRepositoryIntegrationTestSuite struct {
	suite.Suite
	repo            BrowseItemRepository
	metaCacheWriter cache.Cacher
}

func (suite *BrowseItemRepositoryIntegrationTestSuite) SetupSuite() {
	suite.metaCacheWriter = cache.New(datastore.NewRedisPool([]string{"localhost:6379"}).Master())
	suite.repo = &browseItemRepository{
		metaCacheReader: suite.metaCacheWriter,
	}
}

func (suite *BrowseItemRepositoryIntegrationTestSuite) TearDownSuite() {
}

func (suite *BrowseItemRepositoryIntegrationTestSuite) SetupTest() {
}

func (suite *BrowseItemRepositoryIntegrationTestSuite) TearDownTest() {
}

func (suite *BrowseItemRepositoryIntegrationTestSuite) TestFindByCollection() {
	suite.Run("GIVEN a collection", func() {
		allBrowses := suite.givenBrowsesItemInRedis()
		suite.Run("THEN find by collection should return the collection", func() {
			c, err := suite.repo.FindByCollection("plan", "lust")
			suite.NoError(err)
			suite.Equal(allBrowses[0], c)
		})
	})
}

func (suite *BrowseItemRepositoryIntegrationTestSuite) givenBrowsesItemInRedis() []*cachemeta.BrowseItem {
	browses := []*cachemeta.BrowseItem{
		{
			CollectionType: "plan",
			CollectionName: "lust",
			Platform:       []cachemeta.BrowsePlatform{cachemeta.BrowsePlatformTVKinds},
		},
		{
			CollectionType: "plan",
			CollectionName: "a",
			Platform:       []cachemeta.BrowsePlatform{cachemeta.BrowsePlatformTVKinds, cachemeta.BrowsePlatformWebAndApp},
		},
		{
			CollectionType: "plan",
			CollectionName: "b",
			Platform:       []cachemeta.BrowsePlatform{cachemeta.BrowsePlatformWebAndApp},
		},
		{
			CollectionType: "plan",
			CollectionName: "c",
			Platform:       []cachemeta.BrowsePlatform{},
		},
	}
	suite.NoError(suite.metaCacheWriter.Set(key.GetMetaDataBrowses(), browses, time.Second*3))
	return browses
}

func (suite *BrowseItemRepositoryIntegrationTestSuite) TestListByPlatform() {
	suite.Run("GIVEN a collection", func() {
		allBrowses := suite.givenBrowsesItemInRedis()

		suite.Run("THEN find by collection should return the collection", func() {
			items, err := suite.repo.ListByPlatform(cachemeta.BrowsePlatformTVKinds)
			suite.NoError(err)
			suite.ElementsMatch([]*cachemeta.BrowseItem{allBrowses[0], allBrowses[1]}, items)
		})
	})
}
