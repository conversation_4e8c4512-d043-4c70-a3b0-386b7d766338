//go:generate mockgen -source title_repository.go -destination title_repository_mock.go -package meta
package meta

import (
	"database/sql"
	"errors"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/request"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/meta"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/elasticsearch"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	legacymeta "github.com/KKTV/kktv-api-v3/pkg/model/dbmeta/legacy"
	modelsearch "github.com/KKTV/kktv-api-v3/pkg/model/search"
	"github.com/lib/pq"
)

var (
	titleRepo     TitleRepository
	onceTitleRepo sync.Once
)

type TitleCondition struct {
	Genre        string
	ContentAgent string
}

type TitleRepository interface {
	GetTitleByID(titleID string) (*dbmeta.Title, error)
	ListSeriesByTitleID(titleID string) ([]*dbmeta.SeriesRow, error)
	ListEpisodesBySeriesID(seriesID string) ([]*dbmeta.Episode, error)
	ListExtraByTitleID(titleID string) ([]*dbmeta.ExtraRow, error)
	// ListViewableTitlesWithDetail returns the client-viewable only titles and its detail meta
	ListViewableTitlesWithDetail(titleIDs []string) ([]*legacymeta.TitleDetail, error)
	ListViewableTitleDetailWithoutSeries(titleIDs []string, onlyLicenseValid bool) ([]*legacymeta.TitleDetail, error)
	ListBulkViewableTitleDetailByIDs(titleIDs []string) ([]*legacymeta.TitleDetail, error)
	FindTitleIDsForAutoGenList(browseCollectionType string, browseCollectionName string, includeCollectionType string, includeCollectionNames []string, titleAmountMin int, now time.Time) ([]CollectionName, error)
	FindTitleIDsForExpireSoon(cond TitleCondition) ([]string, error)
	FindAiringTitleIDs(cond TitleCondition) ([]string, error)
	ListRelated(titleID string) ([]cachemeta.RelatedTitle, error)
	ListAllOnListedTitles() ([]*modelsearch.Title, error)
	ListAllUnitedTitleIDs() ([]UnitedTitleGroup, error)
	ListAvailableTitlesWithPagination(paging request.Paging) ([]*dbmeta.Title, error)
	CountAvailableTitles() (int, error)
}

type titleRepository struct {
	dbReader    database.DB
	cacheReader cache.Cacher
	es          elasticsearch.Client
}

func NewTitleRepository() TitleRepository {
	onceTitleRepo.Do(func() {
		titleRepo = &titleRepository{
			dbReader:    container.DBPoolMeta().Slave().Unsafe(),
			cacheReader: cache.New(container.CachePoolMeta().Slave()),
			es:          elasticsearch.NewClient(config.SearchHost),
		}
	})
	return titleRepo
}

func NewTitleRepositoryWith(metaDBReader database.DB, metaCacheReader cache.Cacher, es elasticsearch.Client) TitleRepository {
	return &titleRepository{
		dbReader:    metaDBReader,
		cacheReader: metaCacheReader,
		es:          es,
	}
}

func (r *titleRepository) GetTitleByID(titleID string) (*dbmeta.Title, error) {
	var record dbmeta.Title
	if err := r.dbReader.Get(&record, `SELECT * FROM meta_title WHERE id = $1;`, titleID); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	//FIXME should just use sql.Scan
	if _, err := record.Parse(); err != nil {
		return nil, err
	}
	return &record, nil
}

func (r *titleRepository) ListSeriesByTitleID(titleID string) ([]*dbmeta.SeriesRow, error) {
	var records []*dbmeta.SeriesRow
	if err := r.dbReader.Select(&records, `SELECT * FROM meta_series WHERE title_id = $1;`, titleID); err != nil {
		return nil, err
	}
	//FIXME should just use sql.Scan
	for _, r := range records {
		r.Parse()
	}
	return records, nil
}

func (r *titleRepository) ListEpisodesBySeriesID(seriesID string) ([]*dbmeta.Episode, error) {
	var records []*dbmeta.Episode
	if err := r.dbReader.Select(&records, `SELECT * FROM meta_episode WHERE series_id = $1;`, seriesID); err != nil {
		return nil, err
	}
	//FIXME should just use sql.Scan
	for _, r := range records {
		r.Parse()
	}
	return records, nil
}

func (r *titleRepository) ListExtraByTitleID(titleID string) ([]*dbmeta.ExtraRow, error) {
	var records []*dbmeta.ExtraRow
	if err := r.dbReader.Select(&records, `SELECT * FROM meta_extra WHERE title_id = $1 ORDER BY id;`, titleID); err != nil {
		return nil, err
	}
	//FIXME should just use sql.Scan
	for _, r := range records {
		r.Parse()
	}
	return records, nil
}

// FIXME should not use the legacy code function
func (r *titleRepository) ListViewableTitlesWithDetail(titleIDs []string) ([]*legacymeta.TitleDetail, error) {
	if len(titleIDs) == 0 {
		return []*legacymeta.TitleDetail{}, nil
	}
	var titles model.CollectionTitles
	titles, err := model.NewTitleDetailsWithSeries(titleIDs)
	if err != nil {
		return nil, err
	}
	filtered := titles.FilterNotExpired()
	result := make([]*legacymeta.TitleDetail, 0)
	for _, title := range filtered {
		result = append(result, &legacymeta.TitleDetail{
			LegacyTitleDetail: title,
		})
	}
	return result, nil
}

// FIXME should not use the legacy code function
func (r *titleRepository) ListViewableTitleDetailWithoutSeries(titleIDs []string, onlyLicenseValid bool) ([]*legacymeta.TitleDetail, error) {
	if len(titleIDs) == 0 {
		return []*legacymeta.TitleDetail{}, nil
	}
	var titles model.CollectionTitles
	titles, err := model.NewTitleDetails(titleIDs)
	if err != nil {
		return nil, err
	}
	if onlyLicenseValid {
		titles = titles.FilterNotExpired()
	}
	result := make([]*legacymeta.TitleDetail, 0)
	for _, title := range titles {
		result = append(result, &legacymeta.TitleDetail{
			LegacyTitleDetail: title,
		})
	}
	return result, nil
}

func (r *titleRepository) ListBulkViewableTitleDetailByIDs(titleIDs []string) ([]*legacymeta.TitleDetail, error) {
	if len(titleIDs) == 0 {
		return []*legacymeta.TitleDetail{}, nil
	}
	var titles model.CollectionTitles
	titles, err := model.NewBulkTitleDetailsWithGenres(titleIDs)
	if err != nil {
		return nil, err
	}

	result := make([]*legacymeta.TitleDetail, 0)
	for _, title := range titles {
		result = append(result, &legacymeta.TitleDetail{
			LegacyTitleDetail: title,
		})
	}
	return result, nil
}

type CollectionName struct {
	CollectionName string         `db:"collection_name"`
	TitleIDs       pq.StringArray `db:"title_ids"`
}

// FindTitleIDsForAutoGenList return a list of (collectionName, title ID array).
// query by which collectionType and has what collectionNames, which the amount of titles with such collectionType must be greater than the titleAmountMin
// the child_lock titles will be filtered out
func (r *titleRepository) FindTitleIDsForAutoGenList(browseCollectionType string, browseCollectionName string, includeCollectionType string, includeCollectionNames []string, titleAmountMin int, now time.Time) ([]CollectionName, error) {
	if browseCollectionType == "genre" {
		browseCollectionType = "genres"
	} else if browseCollectionType == "content_agent" {
		browseCollectionType = "content_agents"
	} else if browseCollectionType == "content_provider" {
		browseCollectionType = "content_providers"
	}

	var records []CollectionName
	if err := r.dbReader.Select(&records,
		`SELECT collection_name, ids AS title_ids
		FROM(
			WITH nonLockTitle AS (
				SELECT title_id,
					TO_TIMESTAMP(concat(min(meta->>'license_start'),' 00:00:00+08'), 'YYYY-MM-DD HH24:MI:SSTZH') at time zone 'utc' as license_start,
					TO_TIMESTAMP(concat(max(meta->>'license_end'),' 23:59:59+08'), 'YYYY-MM-DD HH24:MI:SSTZH') at time zone 'utc' as license_end
				FROM meta_series
				WHERE (meta->>'child_lock') is null
					AND meta->>'license_start' is not null
					AND meta->>'license_end' is not null
				group by title_id
			)
			SELECT collection_name, ARRAY_AGG(id) AS ids
			FROM(
				SELECT mt.id,
					JSONB_ARRAY_ELEMENTS(CASE WHEN jsonb_typeof(mt.meta->$3)='array'
					  THEN mt.meta->$3
					  ELSE jsonb_build_array(mt.meta->$3)
					END)->>0
					as collection_name
				FROM meta_title as mt
				join nonLockTitle nlt on nlt.title_id=mt.id
				WHERE (mt.meta->>'available')::BOOL = TRUE
				AND nlt.license_start <= $6 and nlt.license_end >= $6
				AND mt.meta->$1 ? $2
				ORDER BY 1 desc
			) as tmp
			WHERE collection_name = ANY($4)
			GROUP BY 1
			ORDER BY 1 DESC
		) AS t
		WHERE ARRAY_LENGTH(ids, 1) >= $5`,
		browseCollectionType, browseCollectionName, includeCollectionType, pq.Array(includeCollectionNames), titleAmountMin, now); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return records, nil
}

func (r *titleRepository) FindTitleIDsForExpireSoon(cond TitleCondition) ([]string, error) {
	var expireSoonIDs []string
	if err := r.cacheReader.Get(key.MetaTitleIDsExpireSoon(), &expireSoonIDs); errors.Is(err, cache.ErrCacheMiss) {
		return expireSoonIDs, nil
	} else if err != nil {
		return nil, err
	}

	q := `SELECT id FROM meta_title WHERE id = ANY(:expire_ids)`
	args := map[string]interface{}{}
	if cond.Genre != "" {
		q += ` AND meta->'genres' ? :genre`
		args["genre"] = cond.Genre
	}
	if cond.ContentAgent != "" {
		q += ` AND meta->'content_agents' ? :content_agent`
		args["content_agent"] = cond.ContentAgent
	}

	if len(args) == 0 { // no extra condition provided
		return expireSoonIDs, nil
	}

	args["expire_ids"] = pq.Array(expireSoonIDs)

	q += ` ORDER BY array_position( cast(:expire_ids as varchar[]), id)`
	var selectIDs []string

	namedQuery, err := r.dbReader.PrepareNamed(q)
	if err != nil {
		return nil, err
	}
	if err := namedQuery.Select(&selectIDs, args); err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}
	return selectIDs, nil
}

// FindAiringTitleIDs returns the airing title IDs with the given condition,
// the returned title must be all on on-listed to users because only on-listed titles will be inserted into elasticsearch
func (r *titleRepository) FindAiringTitleIDs(cond TitleCondition) ([]string, error) {
	shoulds := make([]elasticsearch.BoolFilter, 0)
	filter := elasticsearch.BoolFilter{
		TermOpts: []map[string]interface{}{
			{"genre": cond.Genre},
			{"is_ending": false},
		},
		TermsOpts: []map[string]interface{}{
			{"title_type": meta.AiringTitleTypes},
		},
	}
	shoulds = append(shoulds, filter)

	ids := make([]string, 0)
	const batchSize = 100
	for from, size := 0, batchSize; ; from += size {
		var result elasticsearch.SearchResp[struct{}]
		if err := r.es.SearchTitle(elasticsearch.SearchTitlePayload{
			Should: shoulds,
			Sort: elasticsearch.Sort{
				Field: "sortid", Order: "desc",
			},
			From: from,
			Size: size,
		}, &result); err != nil {
			return nil, err
		}

		for _, hit := range result.Hits.Hits {
			ids = append(ids, hit.ID)
		}
		if len(result.Hits.Hits) < batchSize {
			break
		}
	}
	return ids, nil
}

func (r *titleRepository) ListRelated(titleID string) ([]cachemeta.RelatedTitle, error) {
	var related []cachemeta.RelatedTitle
	if err := r.cacheReader.HGet(key.Title(titleID), "cf", &related); errors.Is(err, cache.ErrCacheMiss) {
		return []cachemeta.RelatedTitle{}, nil
	} else if err != nil {
		return nil, err
	}
	return related, nil
}

func (r *titleRepository) ListAllOnListedTitles() ([]*modelsearch.Title, error) {
	titles := make([]*modelsearch.Title, 0)

	shoulds := make([]elasticsearch.BoolFilter, 0)
	const batchSize = 100
	for from, size := 0, batchSize; ; from += size {
		var result elasticsearch.SearchResp[modelsearch.Title]
		if err := r.es.SearchTitle(elasticsearch.SearchTitlePayload{
			Should: shoulds,
			Sort: elasticsearch.Sort{
				Field: "sortid", Order: "desc",
			},
			From: from,
			Size: size,
		}, &result); err != nil {
			return nil, err
		}

		for _, hit := range result.Hits.Hits {
			titles = append(titles, hit.Source)
		}
		if len(result.Hits.Hits) < batchSize {
			break
		}
	}
	return titles, nil
}

type UnitedTitleGroup struct {
	UnitedID       string         `db:"united_id"`
	OriginTitleIDs pq.StringArray `db:"origin_title_ids"`
}

func (r *titleRepository) ListAllUnitedTitleIDs() ([]UnitedTitleGroup, error) {
	var unitedTitleGroups []UnitedTitleGroup
	q := `SELECT united_id, ARRAY_AGG(id ORDER BY id ASC) AS origin_title_ids
			FROM meta_title
			WHERE united_id IS NOT NULL GROUP BY united_id`
	err := r.dbReader.Select(&unitedTitleGroups, q)
	if err != nil {
		return nil, err
	}
	return unitedTitleGroups, nil
}

func (r *titleRepository) ListAvailableTitlesWithPagination(paging request.Paging) ([]*dbmeta.Title, error) {
	offset := (paging.Page - 1) * paging.PageSize
	limit := paging.PageSize
	var availableTitles []*dbmeta.Title
	query := `select id, name from meta_title where meta #>> '{available}' = 'true' limit $1 offset $2`
	if err := r.dbReader.Select(&availableTitles, query, limit, offset); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	return availableTitles, nil
}

func (r *titleRepository) CountAvailableTitles() (int, error) {
	var count int
	query := `select count(*) from meta_title where meta #>> '{available}' = 'true'`
	if err := r.dbReader.Get(&count, query); errors.Is(err, sql.ErrNoRows) {
		return 0, nil
	} else if err != nil {
		return 0, err
	}

	return count, nil
}
