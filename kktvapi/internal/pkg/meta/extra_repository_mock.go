// Code generated by MockGen. DO NOT EDIT.
// Source: extra_repository.go

// Package meta is a generated GoMock package.
package meta

import (
	reflect "reflect"

	dbmeta "github.com/KKTV/kktv-api-v3/pkg/model/dbmeta"
	gomock "github.com/golang/mock/gomock"
)

// MockExtraRepository is a mock of ExtraRepository interface.
type MockExtraRepository struct {
	ctrl     *gomock.Controller
	recorder *MockExtraRepositoryMockRecorder
}

// MockExtraRepositoryMockRecorder is the mock recorder for MockExtraRepository.
type MockExtraRepositoryMockRecorder struct {
	mock *MockExtraRepository
}

// NewMockExtraRepository creates a new mock instance.
func NewMockExtraRepository(ctrl *gomock.Controller) *MockExtraRepository {
	mock := &MockExtraRepository{ctrl: ctrl}
	mock.recorder = &MockExtraRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockExtraRepository) EXPECT() *MockExtraRepositoryMockRecorder {
	return m.recorder
}

// GetByID mocks base method.
func (m *MockExtraRepository) GetByID(id string) (*dbmeta.Extra, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", id)
	ret0, _ := ret[0].(*dbmeta.Extra)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockExtraRepositoryMockRecorder) GetByID(id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockExtraRepository)(nil).GetByID), id)
}
