//go:generate mockgen -source browse_item_repository.go -destination browse_item_repository_mock.go -package meta
package meta

import (
	"errors"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
)

var (
	browseItemRepo     BrowseItemRepository
	onceBrowseItemRepo sync.Once
)

type BrowseItemRepository interface {
	FindByCollection(key, name string) (*cachemeta.BrowseItem, error)
	ListByPlatform(platform cachemeta.BrowsePlatform) ([]*cachemeta.BrowseItem, error)
}

type browseItemRepository struct {
	metaCacheReader cache.Cacher
}

func NewBrowseItemRepository() BrowseItemRepository {
	onceBrowseItemRepo.Do(func() {
		browseItemRepo = &browseItemRepository{
			metaCacheReader: cache.New(container.CachePoolMeta().Slave()),
		}
	})
	return browseItemRepo
}

func (b *browseItemRepository) FindByCollection(typ, name string) (*cachemeta.BrowseItem, error) {
	records := make([]*cachemeta.BrowseItem, 0)
	err := b.metaCacheReader.Get(key.GetMetaDataBrowses(), &records)
	if errors.Is(err, cache.ErrCacheMiss) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	for _, record := range records {
		if record.CollectionType == typ && record.CollectionName == name {
			return record, nil
		}
	}
	return nil, nil
}

func (b *browseItemRepository) ListByPlatform(platform cachemeta.BrowsePlatform) ([]*cachemeta.BrowseItem, error) {
	records := make([]*cachemeta.BrowseItem, 0)
	err := b.metaCacheReader.Get(key.GetMetaDataBrowses(), &records)
	if errors.Is(err, cache.ErrCacheMiss) {
		return records, nil
	} else if err != nil {
		return nil, err
	}
	filtered := make([]*cachemeta.BrowseItem, 0)
	for _, record := range records {
		if record.ContainPlatform(platform) {
			filtered = append(filtered, record)
		}
	}
	return filtered, nil
}
