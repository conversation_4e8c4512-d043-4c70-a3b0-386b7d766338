// Code generated by MockGen. DO NOT EDIT.
// Source: title_repository.go

// Package meta is a generated GoMock package.
package meta

import (
	reflect "reflect"
	time "time"

	dbmeta "github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	request "github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/request"
	cachemeta "github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	legacy "github.com/KKTV/kktv-api-v3/pkg/model/dbmeta/legacy"
	search "github.com/KKTV/kktv-api-v3/pkg/model/search"
	gomock "github.com/golang/mock/gomock"
)

// MockTitleRepository is a mock of TitleRepository interface.
type MockTitleRepository struct {
	ctrl     *gomock.Controller
	recorder *MockTitleRepositoryMockRecorder
}

// MockTitleRepositoryMockRecorder is the mock recorder for MockTitleRepository.
type MockTitleRepositoryMockRecorder struct {
	mock *MockTitleRepository
}

// NewMockTitleRepository creates a new mock instance.
func NewMockTitleRepository(ctrl *gomock.Controller) *MockTitleRepository {
	mock := &MockTitleRepository{ctrl: ctrl}
	mock.recorder = &MockTitleRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTitleRepository) EXPECT() *MockTitleRepositoryMockRecorder {
	return m.recorder
}

// CountAvailableTitles mocks base method.
func (m *MockTitleRepository) CountAvailableTitles() (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountAvailableTitles")
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountAvailableTitles indicates an expected call of CountAvailableTitles.
func (mr *MockTitleRepositoryMockRecorder) CountAvailableTitles() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountAvailableTitles", reflect.TypeOf((*MockTitleRepository)(nil).CountAvailableTitles))
}

// FindAiringTitleIDs mocks base method.
func (m *MockTitleRepository) FindAiringTitleIDs(cond TitleCondition) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAiringTitleIDs", cond)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindAiringTitleIDs indicates an expected call of FindAiringTitleIDs.
func (mr *MockTitleRepositoryMockRecorder) FindAiringTitleIDs(cond interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAiringTitleIDs", reflect.TypeOf((*MockTitleRepository)(nil).FindAiringTitleIDs), cond)
}

// FindTitleIDsForAutoGenList mocks base method.
func (m *MockTitleRepository) FindTitleIDsForAutoGenList(browseCollectionType, browseCollectionName, includeCollectionType string, includeCollectionNames []string, titleAmountMin int, now time.Time) ([]CollectionName, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindTitleIDsForAutoGenList", browseCollectionType, browseCollectionName, includeCollectionType, includeCollectionNames, titleAmountMin, now)
	ret0, _ := ret[0].([]CollectionName)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindTitleIDsForAutoGenList indicates an expected call of FindTitleIDsForAutoGenList.
func (mr *MockTitleRepositoryMockRecorder) FindTitleIDsForAutoGenList(browseCollectionType, browseCollectionName, includeCollectionType, includeCollectionNames, titleAmountMin, now interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindTitleIDsForAutoGenList", reflect.TypeOf((*MockTitleRepository)(nil).FindTitleIDsForAutoGenList), browseCollectionType, browseCollectionName, includeCollectionType, includeCollectionNames, titleAmountMin, now)
}

// FindTitleIDsForExpireSoon mocks base method.
func (m *MockTitleRepository) FindTitleIDsForExpireSoon(cond TitleCondition) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindTitleIDsForExpireSoon", cond)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindTitleIDsForExpireSoon indicates an expected call of FindTitleIDsForExpireSoon.
func (mr *MockTitleRepositoryMockRecorder) FindTitleIDsForExpireSoon(cond interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindTitleIDsForExpireSoon", reflect.TypeOf((*MockTitleRepository)(nil).FindTitleIDsForExpireSoon), cond)
}

// GetTitleByID mocks base method.
func (m *MockTitleRepository) GetTitleByID(titleID string) (*dbmeta.Title, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTitleByID", titleID)
	ret0, _ := ret[0].(*dbmeta.Title)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTitleByID indicates an expected call of GetTitleByID.
func (mr *MockTitleRepositoryMockRecorder) GetTitleByID(titleID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTitleByID", reflect.TypeOf((*MockTitleRepository)(nil).GetTitleByID), titleID)
}

// ListAllOnListedTitles mocks base method.
func (m *MockTitleRepository) ListAllOnListedTitles() ([]*search.Title, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAllOnListedTitles")
	ret0, _ := ret[0].([]*search.Title)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAllOnListedTitles indicates an expected call of ListAllOnListedTitles.
func (mr *MockTitleRepositoryMockRecorder) ListAllOnListedTitles() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAllOnListedTitles", reflect.TypeOf((*MockTitleRepository)(nil).ListAllOnListedTitles))
}

// ListAllUnitedTitleIDs mocks base method.
func (m *MockTitleRepository) ListAllUnitedTitleIDs() ([]UnitedTitleGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAllUnitedTitleIDs")
	ret0, _ := ret[0].([]UnitedTitleGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAllUnitedTitleIDs indicates an expected call of ListAllUnitedTitleIDs.
func (mr *MockTitleRepositoryMockRecorder) ListAllUnitedTitleIDs() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAllUnitedTitleIDs", reflect.TypeOf((*MockTitleRepository)(nil).ListAllUnitedTitleIDs))
}

// ListAvailableTitlesWithPagination mocks base method.
func (m *MockTitleRepository) ListAvailableTitlesWithPagination(paging request.Paging) ([]*dbmeta.Title, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAvailableTitlesWithPagination", paging)
	ret0, _ := ret[0].([]*dbmeta.Title)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAvailableTitlesWithPagination indicates an expected call of ListAvailableTitlesWithPagination.
func (mr *MockTitleRepositoryMockRecorder) ListAvailableTitlesWithPagination(paging interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAvailableTitlesWithPagination", reflect.TypeOf((*MockTitleRepository)(nil).ListAvailableTitlesWithPagination), paging)
}

// ListBulkViewableTitleDetailByIDs mocks base method.
func (m *MockTitleRepository) ListBulkViewableTitleDetailByIDs(titleIDs []string) ([]*legacy.TitleDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBulkViewableTitleDetailByIDs", titleIDs)
	ret0, _ := ret[0].([]*legacy.TitleDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBulkViewableTitleDetailByIDs indicates an expected call of ListBulkViewableTitleDetailByIDs.
func (mr *MockTitleRepositoryMockRecorder) ListBulkViewableTitleDetailByIDs(titleIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBulkViewableTitleDetailByIDs", reflect.TypeOf((*MockTitleRepository)(nil).ListBulkViewableTitleDetailByIDs), titleIDs)
}

// ListEpisodesBySeriesID mocks base method.
func (m *MockTitleRepository) ListEpisodesBySeriesID(seriesID string) ([]*dbmeta.Episode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListEpisodesBySeriesID", seriesID)
	ret0, _ := ret[0].([]*dbmeta.Episode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListEpisodesBySeriesID indicates an expected call of ListEpisodesBySeriesID.
func (mr *MockTitleRepositoryMockRecorder) ListEpisodesBySeriesID(seriesID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListEpisodesBySeriesID", reflect.TypeOf((*MockTitleRepository)(nil).ListEpisodesBySeriesID), seriesID)
}

// ListExtraByTitleID mocks base method.
func (m *MockTitleRepository) ListExtraByTitleID(titleID string) ([]*dbmeta.ExtraRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListExtraByTitleID", titleID)
	ret0, _ := ret[0].([]*dbmeta.ExtraRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListExtraByTitleID indicates an expected call of ListExtraByTitleID.
func (mr *MockTitleRepositoryMockRecorder) ListExtraByTitleID(titleID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListExtraByTitleID", reflect.TypeOf((*MockTitleRepository)(nil).ListExtraByTitleID), titleID)
}

// ListRelated mocks base method.
func (m *MockTitleRepository) ListRelated(titleID string) ([]cachemeta.RelatedTitle, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRelated", titleID)
	ret0, _ := ret[0].([]cachemeta.RelatedTitle)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRelated indicates an expected call of ListRelated.
func (mr *MockTitleRepositoryMockRecorder) ListRelated(titleID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRelated", reflect.TypeOf((*MockTitleRepository)(nil).ListRelated), titleID)
}

// ListSeriesByTitleID mocks base method.
func (m *MockTitleRepository) ListSeriesByTitleID(titleID string) ([]*dbmeta.SeriesRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSeriesByTitleID", titleID)
	ret0, _ := ret[0].([]*dbmeta.SeriesRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSeriesByTitleID indicates an expected call of ListSeriesByTitleID.
func (mr *MockTitleRepositoryMockRecorder) ListSeriesByTitleID(titleID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSeriesByTitleID", reflect.TypeOf((*MockTitleRepository)(nil).ListSeriesByTitleID), titleID)
}

// ListViewableTitleDetailWithoutSeries mocks base method.
func (m *MockTitleRepository) ListViewableTitleDetailWithoutSeries(titleIDs []string, onlyLicenseValid bool) ([]*legacy.TitleDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListViewableTitleDetailWithoutSeries", titleIDs, onlyLicenseValid)
	ret0, _ := ret[0].([]*legacy.TitleDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListViewableTitleDetailWithoutSeries indicates an expected call of ListViewableTitleDetailWithoutSeries.
func (mr *MockTitleRepositoryMockRecorder) ListViewableTitleDetailWithoutSeries(titleIDs, onlyLicenseValid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListViewableTitleDetailWithoutSeries", reflect.TypeOf((*MockTitleRepository)(nil).ListViewableTitleDetailWithoutSeries), titleIDs, onlyLicenseValid)
}

// ListViewableTitlesWithDetail mocks base method.
func (m *MockTitleRepository) ListViewableTitlesWithDetail(titleIDs []string) ([]*legacy.TitleDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListViewableTitlesWithDetail", titleIDs)
	ret0, _ := ret[0].([]*legacy.TitleDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListViewableTitlesWithDetail indicates an expected call of ListViewableTitlesWithDetail.
func (mr *MockTitleRepositoryMockRecorder) ListViewableTitlesWithDetail(titleIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListViewableTitlesWithDetail", reflect.TypeOf((*MockTitleRepository)(nil).ListViewableTitlesWithDetail), titleIDs)
}
