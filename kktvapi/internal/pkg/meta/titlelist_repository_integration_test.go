package meta

import (
	_ "embed"
	"os"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/dbtest"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbmeta"
	"github.com/stretchr/testify/suite"
)

type TitleListRepositoryTestSuite struct {
	dbtest.Suite
	repo TitlelistRepository
}

func TestTitleListRepositoryIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(TitleListRepositoryTestSuite))
}

func (suite *TitleListRepositoryTestSuite) SetupTest() {
	suite.Suite.SetupTest()
	suite.repo = &titleListRepository{dbReader: suite.GetTransaction()}
}

func (suite *TitleListRepositoryTestSuite) TearDownTest() {
	suite.Suite.TearDownTest()
}

func (suite *TitleListRepositoryTestSuite) SetupSuite() {
	suite.Suite.SetupSuite(os.Getenv("TEST_DB_META_DSN"))
}

func init() {
	os.Setenv("TEST_DB_META_DSN", "postgres://foo:bar@localhost:5432/meta?sslmode=disable")
}

func (suite *TitleListRepositoryTestSuite) TestListHeadline() {
	suite.givenTitleList()

	records, err := suite.repo.ListHeadlines("genre:featured", time.Date(2021, 4, 29, 12, 0, 0, 0, time.UTC))

	suite.NoError(err)
	suite.Lenf(records, 1, "records: %+v", records)
	suite.Equal("來杯熱咖啡，體驗苦中帶甜的戀愛滋味", records[0].Caption)
	suite.Equal(dbmeta.TitlelistPinTypeFirst.String(), records[0].Meta.Pinned)

}

func (suite *TitleListRepositoryTestSuite) TestListChoices() {
	suite.givenTitleList()

	records, err := suite.repo.ListChoices("genre:featured", time.Date(2022, 4, 29, 12, 0, 0, 0, time.UTC))

	suite.NoError(err)
	suite.Lenf(records, 1, "records: %+v", records)
	suite.Equal("集合啦", records[0].Caption)
	suite.Equal("choice", records[0].ListType)
}

func (suite *TitleListRepositoryTestSuite) TestGetPinned() {
	suite.givenTitleList()

	record, err := suite.repo.GetPinned("genre:動漫", dbmeta.TitlelistPinTypeAiring, time.Date(2022, 4, 29, 12, 0, 0, 0, time.UTC))

	suite.NoError(err)
	suite.NotNil(record)
	suite.Equal("drama airing", record.Caption)
	suite.Equal("choice", record.ListType)
}

func (suite *TitleListRepositoryTestSuite) TestGetRanking() {
	suite.givenTitleList()

	testcases := []struct {
		collectionKey      string
		targetDate         time.Time
		expectedCaption    string
		expectedTitleCount int
	}{
		{
			collectionKey:      "genre:featured",
			targetDate:         time.Date(2022, 4, 29, 12, 0, 0, 0, time.UTC),
			expectedCaption:    "首頁排行榜",
			expectedTitleCount: 5,
		},
		{
			collectionKey:      "plan:lust",
			targetDate:         time.Date(2022, 4, 29, 12, 0, 0, 0, time.UTC),
			expectedCaption:    "深夜排行榜",
			expectedTitleCount: 4,
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.collectionKey, func() {
			record, err := suite.repo.GetRanking(tc.collectionKey, tc.targetDate)

			suite.NoError(err)
			suite.NotNil(record)
			suite.Equal(tc.expectedCaption, record.Caption)
			suite.Equal("ranking", record.ListType)
			suite.Equal(tc.expectedTitleCount, len(record.Meta.TitleID))
		})
	}
}

//go:embed testdata/titlelist_repository/TitlelistRepo.sql
var dataForTitlelistRepo string

func (suite *TitleListRepositoryTestSuite) givenTitleList() {
	// clean meta_titlelist first
	suite.DeleteFromTable("meta_titlelist")

	if _, err := suite.GetTransaction().Exec(dataForTitlelistRepo); err != nil {
		suite.Require().Fail("fail to insert test data", err)
	}
}
