package meta

import (
	_ "embed"
	"os"
	"testing"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/dbtest"
	"github.com/stretchr/testify/suite"
)

type EpisodeRepositoryTestSuite struct {
	dbtest.Suite
	repo *episodeRepo
}

func TestEpisodeRepositoryIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(EpisodeRepositoryTestSuite))
}

func (suite *EpisodeRepositoryTestSuite) SetupTest() {
	suite.Suite.SetupTest()
	suite.repo = &episodeRepo{metaDBReader: suite.GetTransaction()}
}

func (suite *EpisodeRepositoryTestSuite) TearDownTest() {
	suite.Suite.TearDownTest()
}

func (suite *EpisodeRepositoryTestSuite) SetupSuite() {
	suite.Suite.SetupSuite(os.Getenv("TEST_DB_META_DSN"))
}

//go:embed testdata/episode_repository/EpisodeRepo.sql
var dataForEpisodeRepo_GetTitleByID string

func (suite *EpisodeRepositoryTestSuite) TestGetByID() {
	suite.DeleteFromTable("meta_episode")
	if _, err := suite.GetTransaction().Exec(dataForEpisodeRepo_GetTitleByID); err != nil {
		suite.Fail("create fail", err)
	}

	suite.Run("get by id", func() {
		id := "00000391010025"
		ep, err := suite.repo.GetByID(id)
		suite.NoError(err)
		suite.Equal("20200417期", ep.Name)
		suite.True(ep.Available())
	})

	suite.Run("get by not exist id", func() {
		ep, err := suite.repo.GetByID("not-exist-id")
		suite.NoError(err)
		suite.Nil(ep)
	})

}
