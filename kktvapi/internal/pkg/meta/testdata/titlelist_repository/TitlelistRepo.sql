INSERT INTO meta_titlelist
(caption, summary, source_image, resized_images, enabled, "order", created_at, updated_at, title_id, visible_since,
 visible_until, topic, uri, url, list_type, trailer_autoplay_enabled, trailer_episode_id, dominant_color, meta)
VALUES ('來喔來喔', '', '', NULL, true, 1, '2021-12-06 15:04:06.622', '2021-12-06 15:04:06.622', NULL,
        '2021-12-06 14:53:40.000', '2022-01-22 14:53:00.000', '', '', '', 'choice', false, NULL, NULL,
        '{"share_id": "abc", "title_id": ["02000259", "01000190", "02000248", "01000409", "01000468"], "copyright": "這是測試用", "description": "這是測試用", "background_image_url": "https://test-images.kktv.com.tw/titlelist/origin/74-backgroundImage.jpeg"}'::jsonb);
INSERT INTO meta_titlelist
(caption, summary, source_image, resized_images, enabled, "order", created_at, updated_at, title_id, visible_since,
 visible_until, topic, uri, url, list_type, trailer_autoplay_enabled, trailer_episode_id, dominant_color, meta)
VALUES ('test', '', 'titlelist/origin/3.jpeg',
        '{"hl":"s3://kktv-test-images/ed/ed4cb19d7aac8b4a1e6ec649e8b741bd1a679b65.hl.jpg","lg":"s3://kktv-test-images/ed/ed4cb19d7aac8b4a1e6ec649e8b741bd1a679b65.lg.jpg","md":"s3://kktv-test-images/ed/ed4cb19d7aac8b4a1e6ec649e8b741bd1a679b65.md.jpg","sm":"s3://kktv-test-images/ed/ed4cb19d7aac8b4a1e6ec649e8b741bd1a679b65.sm.jpg","xs":"s3://kktv-test-images/ed/ed4cb19d7aac8b4a1e6ec649e8b741bd1a679b65.xs.jpg"}',
        true, 2, '2020-04-08 11:11:15.574', '2020-04-08 11:11:15.574', NULL, '2020-04-08 11:10:41.000',
        '2022-02-23 12:00:00.000', '', '', '', 'link', false, NULL, '#6d4c2e',
        '{"share_id": "test", "title_id": ["02000141", "02000004", "09000023", "02000013", "02000007", "06000263", "02000060", "02000104", "02000136", "02000033", "01000173", "06000469", "06000533", "06000421", "01000475", "02000301", "01000468"], "collections":["genre:戲劇"]}'::jsonb);

INSERT INTO meta_titlelist
(caption, summary, source_image, resized_images, enabled, "order", created_at, updated_at, title_id, visible_since,
 visible_until, topic, uri, url, list_type, trailer_autoplay_enabled, trailer_episode_id, dominant_color, meta)
VALUES ('來杯熱咖啡，體驗苦中帶甜的戀愛滋味', '', 'titlelist/origin/73.jpeg',
        '{"hl":"s3://kktv-test-images/bc/bcfcb3a674343b44f9f2eb1496559fa068aa556a.hl.jpg","lg":"s3://kktv-test-images/bc/bcfcb3a674343b44f9f2eb1496559fa068aa556a.lg.jpg","md":"s3://kktv-test-images/bc/bcfcb3a674343b44f9f2eb1496559fa068aa556a.md.jpg","sm":"s3://kktv-test-images/bc/bcfcb3a674343b44f9f2eb1496559fa068aa556a.sm.jpg","xs":"s3://kktv-test-images/bc/bcfcb3a674343b44f9f2eb1496559fa068aa556a.xs.jpg"}',
        true, 1, '2021-02-23 17:08:20.459', '2021-02-23 17:08:20.459', NULL, '2021-02-23 17:03:45.000',
        '2022-01-01 00:00:00.000', '', '', '', 'title', false, NULL, '#6d4c2e',
        '{"og_image": "https://image.kktv.com.tw/stills/7f/7fed9a92b1c99eb8da4cf0562bbc29aa561e7429.md.jpg", "share_id": "coffee_with_love", "title_id": ["02000259", "01000424", "00000333", "03000607", "00000426", "02000283", "01000190", "02000253", "01000418", "03000516", "02000248", "01000409", "03000588", "06000869", "01000468", "01000469", "01000472", "01000018", "01000037", "01000465"], "copyright": "這是第二階段才會用到的東西", "description": "這是第二階段才會用到的東西", "background_image_url": "https://test-images.kktv.com.tw/titlelist/origin/73-backgroundImage.jpeg", "collections":["genre:featured","content_agent:TVB"], "pinned":"first"}'::jsonb);

INSERT INTO public.meta_titlelist (caption, summary, source_image, resized_images, enabled, "order", created_at,
                                   updated_at, title_id, visible_since, visible_until, topic, uri, url, list_type,
                                   trailer_autoplay_enabled, trailer_episode_id, dominant_color, meta)
VALUES ('來喔來喔', '', '', NULL, true, 1, '2021-12-06 15:04:06.622', '2021-12-06 15:04:06.622', NULL,
        '2021-12-06 14:53:40.000', '2022-04-22 14:53:00.000', '', '', '', 'choice', false, NULL, NULL,
        '{"share_id": "abc", "title_id": ["02000259", "01000190", "02000248", "01000409", "01000468"], "copyright": "這是測試用", "description": "這是測試用", "background_image_url": "https://test-images.kktv.com.tw/titlelist/origin/74-backgroundImage.jpeg"}'),
       ('集合啦', '', '', NULL, true, 2, '2020-04-08 12:04:04.050', '2020-04-08 12:04:04.050', NULL,
        '2020-04-08 12:03:20.000', '2022-04-30 12:03:00.000', '', '', '', 'choice', false, NULL, NULL,
        '{"share_id": "collect", "title_id": ["02000301", "01000356", "00000394", "01000510", "01000468"], "background_image_url": "https://test-images.kktv.com.tw/titlelist/origin/47-backgroundImage.jpeg", "collections":["genre:featured","content_agent:TVB"]}'),
       ('新劇跟播中', '', '', NULL, true, 1, '2020-04-08 11:24:18.601', '2020-04-08 11:24:18.601', NULL,
        '2021-04-08 11:23:00.000', '2022-12-31 11:23:00.000', '', '', '', 'choice', false, NULL, NULL,
        '{"pinned": "airing", "share_id": "new_drama", "title_id": ["01000475", "02000301", "01000359", "00000396", "01000360", "01000372", "99000390", "99000523", "00000394", "01000355"], "collections": ["genre:戲劇"]}'),
       ('drama airing', '', '', NULL, true, 1, '2022-03-10 17:22:21.048', '2022-03-10 17:22:21.048', NULL,
        '2022-03-10 17:21:38.000', '2023-03-04 17:21:00.000', '', '', '', 'choice', false, NULL, NULL,
        '{"pinned": "airing", "share_id": "7be6344c4c", "title_id": ["01000475", "02000301"], "collections": ["genre:動漫", "genre:親子"]}');

INSERT INTO public.meta_titlelist (caption, summary, source_image, resized_images, enabled, "order", created_at,
                                   updated_at, title_id, visible_since, visible_until, topic, uri, url, list_type,
                                   trailer_autoplay_enabled, trailer_episode_id, dominant_color, meta)
VALUES ('首頁排行榜', '', '', NULL, true, 1, '2022-03-11 14:31:04.676243+08', '2022-03-11 14:31:04.676243+08', NULL,
        '2022-03-11 14:27:23+08', '2023-03-30 14:27:00+08', '', '', '', 'ranking', false, NULL, NULL,
        '{"share_id": "f0395d639a", "title_id": ["09000919", "09000918", "09000912", "09000907", "09000903"], "collections": ["genre:featured"]}'),
       ('深夜排行榜', '', '', NULL, true, 2, '2022-03-11 14:31:04.676243+08', '2022-03-11 14:31:04.676243+08', NULL,
        '2022-03-11 14:27:23+08', '2023-03-30 14:27:00+08', '', '', '', 'ranking', false, NULL, NULL,
        '{"share_id": "f0395d639b", "title_id": ["09001818", "09001939", "09001940", "09001942"], "collections": ["plan:lust"]}');
