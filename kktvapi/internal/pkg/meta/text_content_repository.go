package meta

import (
	"encoding/json"
	"errors"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	"reflect"
	"sync"
)

var (
	textContentRepo     TextContentRepository
	onceTextContentRepo sync.Once
)

type TextContentRepository interface {
	Get() (*cachemeta.TextContent, error)
}

type textContentRepository struct {
	cacheReader cache.Cacher
}

func NewTextContentRepository() TextContentRepository {
	onceTextContentRepo.Do(func() {
		textContentRepo = &textContentRepository{
			cacheReader: cache.New(container.CachePoolMeta().Slave()),
		}
	})
	return textContentRepo
}

func (r *textContentRepository) Get() (*cachemeta.TextContent, error) {
	tc := new(cachemeta.TextContent)
	tcMap, err := r.cacheReader.HGetAll(key.MetaGetServiceTextContent())
	if errors.Is(err, cache.ErrCacheMiss) {
		return tc, nil
	}

	tcValue := reflect.ValueOf(tc).Elem()
	tcType := tcValue.Type()
	for i := 0; i < tcValue.NumField(); i++ {
		field := tcValue.Field(i)
		fieldType := tcType.Field(i)

		if val, ok := tcMap[fieldType.Tag.Get("json")]; ok {
			raw := json.RawMessage(val)
			field.Set(reflect.ValueOf(raw))
		}
	}

	return tc, nil
}
