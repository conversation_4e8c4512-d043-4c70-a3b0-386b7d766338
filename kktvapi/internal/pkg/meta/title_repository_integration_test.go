package meta

import (
	_ "embed"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/dbtest"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/golang/mock/gomock"
	"github.com/lib/pq"
	"github.com/stretchr/testify/suite"
)

type TitleRepositoryTestSuite struct {
	dbtest.Suite
	repo *titleRepository
}

func TestTitleRepositoryIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(TitleRepositoryTestSuite))
}

func (suite *TitleRepositoryTestSuite) SetupTest() {
	suite.Suite.SetupTest()
	suite.repo = &titleRepository{dbReader: suite.GetTransaction()}
}

func (suite *TitleRepositoryTestSuite) TearDownTest() {
	suite.Suite.TearDownTest()
}

func (suite *TitleRepositoryTestSuite) SetupSuite() {
	suite.Suite.SetupSuite(os.Getenv("TEST_DB_META_DSN"))
}

func (suite *TitleRepositoryTestSuite) TestGetTitleById() {
	titleID := "000011"
	suite.prepareGivenTestData(titleID)
	//WHEN
	t, err := suite.repo.GetTitleByID(titleID)
	//THEN
	suite.NoError(err)
	suite.Equal("天竺鼠車車", t.Name)

	t, err = suite.repo.GetTitleByID("00000338")
	suite.NoError(err)
	suite.Nil(t)
}

func (suite *TitleRepositoryTestSuite) TestListSeriesByTitleID() {
	titleID := "09000477"
	//GIVEN
	suite.prepareGivenTestData(titleID)
	//WHEN
	t, err := suite.repo.GetTitleByID(titleID)
	//THEN
	suite.NoError(err)
	suite.Equal("天竺鼠車車", t.Name)

	series, err := suite.repo.ListSeriesByTitleID(titleID)
	suite.NoError(err)
	suite.Len(series, 1)
	suite.Equal("**********", series[0].ID)
	suite.Equal("美麗的崔蜜卡拉有著能看到世上所有人生命時數的能力。", series[0].Meta.Description)
}

func (suite *TitleRepositoryTestSuite) TestListEpisodesBySeriesID() {
	seriesID := "**********"

	testcases := []struct {
		name string
		sID  string
		then func([]*dbmeta.Episode, error)
	}{
		{
			name: "found WHEN data exist",
			sID:  seriesID,
			then: func(records []*dbmeta.Episode, err error) {
				suite.NoError(err)
				suite.Len(records, 1)
				suite.Equal("**********0001", records[0].ID)
				suite.Equal("第1集", records[0].Meta.EpisodeName)
			},
		},
		{
			name: "not found WHEN data not exist",
			sID:  "nosuchid",
			then: func(records []*dbmeta.Episode, err error) {
				suite.NoError(err)
				suite.Empty(records)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			suite.prepareGivenTestData("000429")
			records, err := suite.repo.ListEpisodesBySeriesID(tc.sID)
			tc.then(records, err)
		})
	}
}

//go:embed testdata/title_repository/FindByMatchingCollectionType.sql
var dataForFindByMatchingCollectionType string

func (suite *TitleRepositoryTestSuite) TestFindTitleIDsForAutoGenList() {
	suite.DeleteFromTable("meta_title")
	tx := suite.GetTransaction()
	if _, err := tx.Exec(dataForFindByMatchingCollectionType); err != nil {
		suite.Fail("create fail", err)
	}
	defaultNow := time.Date(2021, 4, 29, 0, 0, 0, 0, time.UTC)
	const defaultLowerBound = 5

	testcases := []struct {
		name                 string
		browseCollectionType string
		browseCollectionName string
		collectionType       string
		collectionNames      []string
		now                  time.Time
		lowerBound           int
		thenAssert           func(result []CollectionName, err error)
	}{
		{
			name:                 "Find by themes",
			browseCollectionType: "genre",
			browseCollectionName: "動漫",
			collectionType:       "themes",
			collectionNames:      []string{"奇幻冒險", "浪漫愛情"},
			now:                  defaultNow,
			lowerBound:           defaultLowerBound,
			thenAssert: func(records []CollectionName, err error) {
				suite.NoError(err)
				titleCountList := map[string]int{"奇幻冒險": 61, "浪漫愛情": 5}
				for _, r := range records {
					suite.NotEmpty(r.CollectionName)
					suite.GreaterOrEqual(len(r.TitleIDs), defaultLowerBound)
					if c, ok := titleCountList[r.CollectionName]; ok {
						suite.Len(r.TitleIDs, c)
					}
					fmt.Printf("record: %+v\n", r)
				}
			},
		},
		{
			name:                 "Find by country",
			browseCollectionType: "genre",
			browseCollectionName: "動漫",
			collectionType:       "country",
			collectionNames:      []string{"Japan", "Korea", "Taiwan", "Hongkong", "China", "Other"},
			now:                  defaultNow, lowerBound: defaultLowerBound,
			thenAssert: func(records []CollectionName, err error) {
				suite.NoError(err)
				titleCountList := map[string]int{"Japan": 12, "Korea": 5}
				for _, r := range records {
					suite.NotEmpty(r.CollectionName)
					suite.GreaterOrEqual(len(r.TitleIDs), defaultLowerBound)
					if c, ok := titleCountList[r.CollectionName]; ok {
						suite.Len(r.TitleIDs, c)
					}
					fmt.Printf("record: %+v\n", r)
				}
			},
		},
		{
			name:                 "Find by theme WHEN license just starts THEN got result",
			browseCollectionType: "genre",
			browseCollectionName: "動漫",
			collectionType:       "themes",
			collectionNames:      []string{"kktvtest"},
			now:                  time.Date(2022, 4, 28, 16, 0, 0, 0, time.UTC), // UTC+8 2022-04-29 00:01:06
			lowerBound:           1,
			thenAssert: func(records []CollectionName, err error) {
				suite.NoError(err)
				suite.Len(records, 1)
				r := records[0]
				suite.Equal("kktvtest", r.CollectionName)
				suite.ElementsMatch([]string{"01060429"}, r.TitleIDs)
				fmt.Printf("record: %+v\n", r)
			},
		},
		{
			name:                 "got result WHEN browse is content_agent",
			browseCollectionType: "content_agent",
			browseCollectionName: "曼迪",
			collectionType:       "themes",
			collectionNames:      []string{"kktvtest", "闔家歡", "雙語"},
			now:                  time.Date(2022, 4, 28, 16, 0, 0, 0, time.UTC), // UTC+8 2022-04-29 00:01:06
			lowerBound:           1,
			thenAssert: func(records []CollectionName, err error) {
				suite.NoError(err)
				suite.ElementsMatch([]CollectionName{
					{CollectionName: "kktvtest", TitleIDs: []string{"01060429"}},
					{CollectionName: "雙語", TitleIDs: []string{"01060429"}},
				}, records)
			},
		},
		{
			name:                 "got result WHEN browse is content_agent",
			browseCollectionType: "content_provider",
			browseCollectionName: "ViuTV",
			collectionType:       "themes",
			collectionNames:      []string{"kktvtest", "闔家歡", "雙語"},
			now:                  time.Date(2022, 4, 28, 16, 0, 0, 0, time.UTC), // UTC+8 2022-04-29 00:01:06
			lowerBound:           1,
			thenAssert: func(records []CollectionName, err error) {
				suite.NoError(err)
				suite.ElementsMatch([]CollectionName{
					{CollectionName: "kktvtest", TitleIDs: []string{"01060429"}},
					{CollectionName: "雙語", TitleIDs: []string{"01060429"}},
				}, records)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			records, err := suite.repo.FindTitleIDsForAutoGenList(tc.browseCollectionType, tc.browseCollectionName, tc.collectionType, tc.collectionNames, tc.lowerBound, tc.now)
			tc.thenAssert(records, err)
		})
	}
}

//go:embed testdata/title_repository/FindTitleIDsForExpireSoon.sql
var dataForFindTitleIDsForExpireSoon string

func (suite *TitleRepositoryTestSuite) TestFindTitleIDsForExpireSoon() {
	//dependency injection
	ctrl := gomock.NewController(suite.T())
	mockMetaCache := cache.NewMockCacher(ctrl)
	suite.repo.cacheReader = mockMetaCache

	//GIVEN
	suite.DeleteFromTable("meta_title")
	if _, err := suite.GetTransaction().Exec(dataForFindTitleIDsForExpireSoon); err != nil {
		suite.Fail("create fail", err)
	}

	testcases := []struct {
		name       string
		cond       TitleCondition
		thenAssert func(result []string, err error)
	}{
		{
			name: "Find by empty cond",
			cond: TitleCondition{},
			thenAssert: func(records []string, err error) {
				suite.NoError(err)
				suite.ElementsMatch([]string{"09000849", "09000848", "99000522"}, records)
			},
		},
		{
			name: "Find by genre and contentAgent cond",
			cond: TitleCondition{
				Genre: "動漫", ContentAgent: "曼迪",
			},
			thenAssert: func(records []string, err error) {
				suite.NoError(err)
				suite.ElementsMatch([]string{"99000522"}, records)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			mockMetaCache.EXPECT().Get(key.MetaTitleIDsExpireSoon(), gomock.Any()).DoAndReturn(
				func(cKey string, receiver interface{}) error {
					s := receiver.(*[]string)
					*s = []string{"09000849", "09000848", "99000522"}
					return nil
				})

			ids, err := suite.repo.FindTitleIDsForExpireSoon(tc.cond)
			tc.thenAssert(ids, err)
		})
	}
}

//go:embed testdata/title_repository/ListAllUnitedTitleIDs.sql
var dataForListAllUnitedTitleIDs string

func (suite *TitleRepositoryTestSuite) TestListAllUnitedTitleIDs() {

	//GIVEN
	suite.DeleteFromTable("meta_title")
	if _, err := suite.GetTransaction().Exec(dataForListAllUnitedTitleIDs); err != nil {
		suite.Fail("create fail", err)
	}

	suite.Run("list all", func() {
		groups, err := suite.repo.ListAllUnitedTitleIDs()
		suite.NoError(err)
		suite.Len(groups, 2)
		suite.ElementsMatch([]UnitedTitleGroup{
			{
				UnitedID:       "99000064",
				OriginTitleIDs: pq.StringArray{"99000064", "99000065"},
			},
			{
				UnitedID:       "99000060",
				OriginTitleIDs: pq.StringArray{"99000060", "99000062", "99000063"},
			},
		}, groups)
	})
}

func (suite *TitleRepositoryTestSuite) prepareGivenTestData(titleID string) {
	//clean tables
	suite.DeleteFromTable("meta_episode", "meta_series", "meta_title")
	// insert into meta_title
	sql := `INSERT INTO meta_title (id, "name", editor_comments, meta, release_info) VALUES($1, '天竺鼠車車', '為了讓您有更好的直播體驗，請更新到最新版本後觀看（本次直播尚未支援 MOD 平台觀賞，敬請見諒）', '{"casts": {"信": null, "Erika": null, "郭靜": null, "鼓鼓": null, "五月天": null, "任家萱": null, "八三夭": null, "吳克群": null, "周興哲": null, "孫盛希": null, "容祖兒": null, "李佳薇": null, "林俊傑": null, "玖壹壹": null, "田馥甄": null, "鄧紫棋": null, "陳勢安": null, "韋禮安": null, "黃子佼": null, "Under Lover": null, "兄弟本色": null, "草東沒有派對": null}, "cover": "https://images.kktv.com.tw/covers/a8/a81180f86d78e513024a528d37248f97061ee84a.xs.jpg", "genres": ["綜藝"], "stills": ["https://image.kktv.com.tw/stills/63/63215e843c72f88cb4da3bdafa82e34b93d9a210.xs.jpg", "https://image.kktv.com.tw/stills/eb/ebdc572b03631ed294124f8d7e1b5eda3ee9ebeb.xs.jpg", "https://image.kktv.com.tw/stills/61/61eda532f2a452d2e5b7988ef964cd5881ecdfba.xs.jpg"], "themes": ["音樂"], "country": "Taiwan", "wiki_zh": "https://zh.wikipedia.org/wiki/KKBOX%E6%95%B8%E4%BD%8D%E9%9F%B3%E6%A8%82%E9%A2%A8%E9%9B%B2%E6%A6%9C#.E7.AC.AC.E5.8D.81.E4.BA.8C.E5.B1.86_2017", "end_year": 2020, "title_id": "99000065", "available": true, "is_ending": true, "start_year": 2019, "title_name": "KKBOX 風雲榜直播後有預告", "title_type": "live", "updated_at": **********, "description": "KKBOX desc。", "ordered_casts": [{"name": "黃子佼", "roles": null}], "content_agents": ["KKBOX"], "extra_title_id": "99000065extra", "total_license_end": **********, "total_license_start": **********, "total_episode_counts": {"**********": 1}, "reverse_display_order": false}'::jsonb, '2020/01/18 晚間七點整準時直播');`

	if _, err := suite.GetTransaction().Exec(sql, titleID); err != nil {
		suite.Fail("create fail", err)
	}
	// insert into meta_series
	if _, err := suite.GetTransaction().Exec(
		`INSERT INTO meta_series (id, "name", title_id, meta) VALUES('**********', '第1季', '09000477', '{"tags": "音樂劇", "casts": "李相侖;李聖經;林世美", "genres": "浪漫愛情;科幻想像", "themes": "愛情;奇幻", "country": "Korea", "writers": "秋惠美", "currency": "USD", "end_year": "2018", "episodes": "1-16", "title_id": "00000338", "available": "1", "copyright": "© STUDIO DRAGON CORPORATION", "directors": "金亨植", "is_ending": "1", "play_zone": "1", "series_id": "01", "free_trial": "1", "start_year": "2018", "title_name": "想停止的瞬間 About Time", "title_type": "miniseries", "updated_at": "**********", "description": "美麗的崔蜜卡拉有著能看到世上所有人生命時數的能力。", "license_end": "2050-05-21", "series_name": "第%d季", "offline_mode": "1", "roaming_mode": "0", "business_type": "VOD", "content_agent": "CJ E&M", "has_subtitles": "1", "license_start": "2018-05-22", "episode_format": "第%d集", "licensed_right": "VOD", "title_name_orig": "멈추고싶은순간 : 어바웃타임", "content_provider": "CJ E&M", "avod_royalty_rate": "0.45", "minimum_guarantee": "56000", "play_zone_by_episodes": "1-3", "sublicensing_recoupment": "sublicense_restricted"}'::jsonb);
		INSERT INTO meta_series (id, "name", title_id, meta) VALUES('**********', '第1季', '09000478', '{}'::jsonb);`); err != nil {
		suite.Fail("insert series fail", err)
	}

	// insert into meta_episode
	if _, err := suite.GetTransaction().Exec(
		`INSERT INTO meta_episode (id, "name", series_id, meta, pub, unpub) VALUES('**********0001', '第1集', '**********', '{
  "hls": {
    "uri": "https://theater.kktv.com.tw/38/**********0001_8ef36f0220208dc5abefcadc57d0eda7/1527129832519070bcf4_sub_hls.m3u8",
    "size": ***********,
    "sizes": {
      "720p": **********,
      "1080p": ***********
    },
    "uri_s3": "s3://kktv-prod-theater/38/**********0001_8ef36f0220208dc5abefcadc57d0eda7/1527129832519070bcf4_sub_hls.m3u8",
    "uri_playzone": "https://theater.kktv.com.tw/38/**********0001_8ef36f0220208dc5abefcadc57d0eda7/152712984398c49415c1_sub_hls.playzone.m3u8"
  },
  "dash": {
    "uri": "https://theater.kktv.com.tw/38/**********0001_8ef36f0220208dc5abefcadc57d0eda7/1527129832519070bcf4_sub_dash.mpd",
    "size": 13745657009,
    "sizes": {
      "720p": 7310697797,
      "1080p": 13745657009
    },
    "uri_s3": "s3://kktv-prod-theater/38/**********0001_8ef36f0220208dc5abefcadc57d0eda7/1527129832519070bcf4_sub_dash.mpd",
    "uri_playzone": "https://theater.kktv.com.tw/38/**********0001_8ef36f0220208dc5abefcadc57d0eda7/1527129836d44f9d3fff_sub_dash.playzone.mpd"
  },
  "still": "https://image.kktv.com.tw/stills/a9/a92b23feda642d9ad4f000d15864fcaa536c604d.xs.jpg",
  "duration": 4207.469933,
  "end_year": 2018,
  "title_id": "00000338",
  "available": true,
  "is_ending": true,
  "play_zone": true,
  "series_id": "**********",
  "subtitles": {
    "zh-Hant": "s3://kktv-prod-theater/38/**********0001_8ef36f0220208dc5abefcadc57d0eda7/1527129832519070bcf4_sub_sub/zh-Hant.vtt"
  },
  "episode_id": "**********0001",
  "free_trial": true,
  "start_year": 2018,
  "title_name": "想停止的瞬間 About Time",
  "updated_at": **********,
  "license_end": **********,
  "series_name": "第1季",
  "episode_name": "第1集",
  "hls_uris_sub": {
    "download": {
      "p0": {
        "uri": "s3://kktv-prod-theater/38/**********0001_8ef36f0220208dc5abefcadc57d0eda7/1527129845d1dfc0b06e_sub_hls.download_p0.m3u8",
        "size": **********.039,
        "bitrate": 283000,
        "resolution": "240p"
      }
    },
    "playback": {
      "p0": {
        "uri": "s3://kktv-prod-theater/38/**********0001_8ef36f0220208dc5abefcadc57d0eda7/1527129845d1dfc0b06e_sub_hls.playback_p0.m3u8",
        "size": **********.039,
        "bitrate": 283000,
        "resolution": "240p"
      }
    }
  },
  "offline_mode": true,
  "publish_time": **********,
  "roaming_mode": false,
  "business_type": "VOD",
  "content_agent": "CJ E&M",
  "dash_uris_sub": {
    "download": {
      "p0": {
        "uri": "s3://kktv-prod-theater/38/**********0001_8ef36f0220208dc5abefcadc57d0eda7/1527129837fade72317d_sub_dash.download_p0.mpd",
        "size": 853577840.2475761,
        "bitrate": 202872,
        "resolution": "240p"
      },
      "p1": {
        "uri": "s3://kktv-prod-theater/38/**********0001_8ef36f0220208dc5abefcadc57d0eda7/1527129837fade72317d_sub_dash.download_p1.mpd",
        "size": **********.699372,
        "bitrate": 325084,
        "resolution": "360p"
      }
    },
    "playback": {
      "p0": {
        "uri": "s3://kktv-prod-theater/38/**********0001_8ef36f0220208dc5abefcadc57d0eda7/1527129837fade72317d_sub_dash.playback_p0.mpd",
        "size": 853577840.2475761,
        "bitrate": 202872,
        "resolution": "240p"
      },
      "p1": {
        "uri": "s3://kktv-prod-theater/38/**********0001_8ef36f0220208dc5abefcadc57d0eda7/1527129837fade72317d_sub_dash.playback_p1.mpd",
        "size": **********.699372,
        "bitrate": 325084,
        "resolution": "360p"
      }
    }
  },
  "has_subtitles": true,
  "license_start": **********,
  "hls_uris_nosub": {},
  "dash_uris_nosub": {},
  "secure_playback": false,
  "title_name_orig": "멈추고싶은순간 : 어바웃타임",
  "content_provider": "CJ E&M",
  "source_mezzanine": "s3://kktv-prod-source-upload/title/00-韓國/00000338-想停止的瞬間-About-Time/t00000338_s01_e0001_me.mp4",
  "source_subtitles": [
    "s3://kktv-prod-source-upload/title/00-韓國/00000338-想停止的瞬間-About-Time/t00000338_s01_e0001_me.zh.srt"
  ]
}'::jsonb, NULL, NULL);
`); err != nil {
		suite.Fail("insert series fail", err)
	}

}
