// Code generated by MockGen. DO NOT EDIT.
// Source: episode_repository.go

// Package meta is a generated GoMock package.
package meta

import (
	reflect "reflect"

	dbmeta "github.com/KKTV/kktv-api-v3/pkg/model/dbmeta"
	gomock "github.com/golang/mock/gomock"
)

// MockEpisodeRepository is a mock of EpisodeRepository interface.
type MockEpisodeRepository struct {
	ctrl     *gomock.Controller
	recorder *MockEpisodeRepositoryMockRecorder
}

// MockEpisodeRepositoryMockRecorder is the mock recorder for MockEpisodeRepository.
type MockEpisodeRepositoryMockRecorder struct {
	mock *MockEpisodeRepository
}

// NewMockEpisodeRepository creates a new mock instance.
func NewMockEpisodeRepository(ctrl *gomock.Controller) *MockEpisodeRepository {
	mock := &MockEpisodeRepository{ctrl: ctrl}
	mock.recorder = &MockEpisodeRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEpisodeRepository) EXPECT() *MockEpisodeRepositoryMockRecorder {
	return m.recorder
}

// GetByID mocks base method.
func (m *MockEpisodeRepository) GetByID(id string) (*dbmeta.Episode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", id)
	ret0, _ := ret[0].(*dbmeta.Episode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockEpisodeRepositoryMockRecorder) GetByID(id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockEpisodeRepository)(nil).GetByID), id)
}
