package meta

import (
	_ "embed"
	"os"
	"testing"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/dbtest"
	"github.com/stretchr/testify/suite"
)

type ExtraRepositoryTestSuite struct {
	dbtest.Suite
	repo *extraRepo
}

func TestExtraRepositoryIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(ExtraRepositoryTestSuite))
}

func (suite *ExtraRepositoryTestSuite) SetupTest() {
	suite.Suite.SetupTest()
	suite.repo = &extraRepo{metaDBReader: suite.GetTransaction()}
}

func (suite *ExtraRepositoryTestSuite) TearDownTest() {
	suite.Suite.TearDownTest()
}

func (suite *ExtraRepositoryTestSuite) SetupSuite() {
	suite.Suite.SetupSuite(os.Getenv("TEST_DB_META_DSN"))
}

//go:embed testdata/extra_repository/ExtraRepo.sql
var dataForExtraRepo_GetByID string

func (suite *ExtraRepositoryTestSuite) TestGetByID() {
	suite.DeleteFromTable("meta_extra")
	if _, err := suite.GetTransaction().Exec(dataForExtraRepo_GetByID); err != nil {
		suite.Fail("create fail", err)
	}

	suite.Run("get by id", func() {
		id := "00000338ot0008"
		ep, err := suite.repo.GetByID(id)
		suite.NoError(err)
		suite.Equal("花絮8", ep.Name)
		suite.Equal("00000338ot", ep.SeriesID)
		suite.Equal("00000338", ep.TitleID)
	})

	suite.Run("get by not exist id", func() {
		ep, err := suite.repo.GetByID("not-exist-id")
		suite.NoError(err)
		suite.Nil(ep)
	})

}
