//go:generate mockgen -source ads_repository.go -destination ads_repository_mock.go -package meta
package meta

import (
	"errors"
	"sort"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
)

var (
	adsRepo     AdsRepository
	onceAdsRepo sync.Once
)

type AdsRepository interface {
	List() (ads *cachemeta.Ads, err error)
}

type adsRepository struct {
	metaCacheReader cache.Cacher
}

func NewAdsRepository() AdsRepository {
	onceAdsRepo.Do(func() {
		adsRepo = &adsRepository{
			metaCacheReader: cache.New(container.CachePoolMeta().Slave()),
		}
	})
	return adsRepo
}
func (r *adsRepository) List() (ads *cachemeta.Ads, err error) {
	adsKey := key.GetMetaAds()
	ads = new(cachemeta.Ads)
	err = r.metaCacheReader.Get(adsKey, ads)
	if errors.Is(err, cache.ErrCacheMiss) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	sort.Sort(ads.Ads)
	return ads, nil

}
