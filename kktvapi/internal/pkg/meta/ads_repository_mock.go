// Code generated by MockGen. DO NOT EDIT.
// Source: ads_repository.go

// Package meta is a generated GoMock package.
package meta

import (
	reflect "reflect"

	cachemeta "github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	gomock "github.com/golang/mock/gomock"
)

// MockAdsRepository is a mock of AdsRepository interface.
type MockAdsRepository struct {
	ctrl     *gomock.Controller
	recorder *MockAdsRepositoryMockRecorder
}

// MockAdsRepositoryMockRecorder is the mock recorder for MockAdsRepository.
type MockAdsRepositoryMockRecorder struct {
	mock *MockAdsRepository
}

// NewMockAdsRepository creates a new mock instance.
func NewMockAdsRepository(ctrl *gomock.Controller) *MockAdsRepository {
	mock := &MockAdsRepository{ctrl: ctrl}
	mock.recorder = &MockAdsRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAdsRepository) EXPECT() *MockAdsRepositoryMockRecorder {
	return m.recorder
}

// List mocks base method.
func (m *MockAdsRepository) List() (*cachemeta.Ads, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List")
	ret0, _ := ret[0].(*cachemeta.Ads)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockAdsRepositoryMockRecorder) List() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockAdsRepository)(nil).List))
}
