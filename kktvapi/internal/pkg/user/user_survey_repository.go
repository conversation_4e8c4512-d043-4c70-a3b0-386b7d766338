//go:generate mockgen -source user_survey_repository.go -destination user_survey_repository_mock.go -package user
package user

import (
	"database/sql"
	"errors"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"sync"
)

var (
	userSurveyRepo     UserSurveyRepository
	onceUserSurveyRepo sync.Once
)

type UserSurveyRepository interface {
	GetByUserIDAndSurveyID(userID string, surveyID int64) (*dbuser.UserSurvey, error)
	Create(userSurvey *dbuser.UserSurvey) (*dbuser.UserSurvey, error)
}

type userSurveyRepository struct {
	dbReader database.DB
	dbWriter database.DB
	clock    clock.Clock
}

func NewUserSurveyRepository() UserSurveyRepository {
	onceUserSurveyRepo.Do(func() {
		userSurveyRepo = &userSurveyRepository{
			dbReader: container.DBPoolUser().Slave().Unsafe(),
			dbWriter: container.DBPoolUser().Master().Unsafe(),
			clock:    clock.New(),
		}
	})
	return userSurveyRepo
}

func (r *userSurveyRepository) GetByUserIDAndSurveyID(userID string, surveyID int64) (*dbuser.UserSurvey, error) {
	userSurvey := new(dbuser.UserSurvey)
	if err := r.dbReader.Get(userSurvey,
		`SELECT * FROM user_surveys WHERE user_id = $1 AND survey_id = $2`,
		userID,
		surveyID,
	); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return userSurvey, nil
}

func (r *userSurveyRepository) Create(userSurvey *dbuser.UserSurvey) (*dbuser.UserSurvey, error) {
	now := r.clock.Now()
	userSurvey.CreatedAt = now
	userSurvey.UpdatedAt = now
	dbFields := database.GetDBFields(userSurvey, UserSurveyID.String())
	insertSQL := `INSERT INTO user_surveys (` + dbFields.String() + `) VALUES (` + dbFields.NamedString() + `)`
	log.Debug("user survey repository: create").Str("sql", insertSQL).Send()

	if _, err := r.dbWriter.NamedExec(insertSQL, userSurvey); err != nil {
		return nil, err
	}

	return userSurvey, nil
}
