// Code generated by MockGen. DO NOT EDIT.
// Source: payment_info_repository.go

// Package user is a generated GoMock package.
package user

import (
	reflect "reflect"

	database "github.com/KKTV/kktv-api-v3/pkg/database"
	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockPaymentInfoRepository is a mock of PaymentInfoRepository interface.
type MockPaymentInfoRepository struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentInfoRepositoryMockRecorder
}

// MockPaymentInfoRepositoryMockRecorder is the mock recorder for MockPaymentInfoRepository.
type MockPaymentInfoRepositoryMockRecorder struct {
	mock *MockPaymentInfoRepository
}

// NewMockPaymentInfoRepository creates a new mock instance.
func NewMockPaymentInfoRepository(ctrl *gomock.Controller) *MockPaymentInfoRepository {
	mock := &MockPaymentInfoRepository{ctrl: ctrl}
	mock.recorder = &MockPaymentInfoRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPaymentInfoRepository) EXPECT() *MockPaymentInfoRepositoryMockRecorder {
	return m.recorder
}

// GetByMODSubscriberID mocks base method.
func (m *MockPaymentInfoRepository) GetByMODSubscriberID(modSubscriberID string) (*dbuser.PaymentInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByMODSubscriberID", modSubscriberID)
	ret0, _ := ret[0].(*dbuser.PaymentInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByMODSubscriberID indicates an expected call of GetByMODSubscriberID.
func (mr *MockPaymentInfoRepositoryMockRecorder) GetByMODSubscriberID(modSubscriberID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByMODSubscriberID", reflect.TypeOf((*MockPaymentInfoRepository)(nil).GetByMODSubscriberID), modSubscriberID)
}

// GetByUserID mocks base method.
func (m *MockPaymentInfoRepository) GetByUserID(userID string) (*dbuser.PaymentInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByUserID", userID)
	ret0, _ := ret[0].(*dbuser.PaymentInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByUserID indicates an expected call of GetByUserID.
func (mr *MockPaymentInfoRepositoryMockRecorder) GetByUserID(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByUserID", reflect.TypeOf((*MockPaymentInfoRepository)(nil).GetByUserID), userID)
}

// GetPaymentInfoByUserID mocks base method.
func (m *MockPaymentInfoRepository) GetPaymentInfoByUserID(uid string) (*dbuser.PaymentInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentInfoByUserID", uid)
	ret0, _ := ret[0].(*dbuser.PaymentInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentInfoByUserID indicates an expected call of GetPaymentInfoByUserID.
func (mr *MockPaymentInfoRepositoryMockRecorder) GetPaymentInfoByUserID(uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentInfoByUserID", reflect.TypeOf((*MockPaymentInfoRepository)(nil).GetPaymentInfoByUserID), uid)
}

// Upsert mocks base method.
func (m *MockPaymentInfoRepository) Upsert(paymentInfo *dbuser.PaymentInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upsert", paymentInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upsert indicates an expected call of Upsert.
func (mr *MockPaymentInfoRepositoryMockRecorder) Upsert(paymentInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockPaymentInfoRepository)(nil).Upsert), paymentInfo)
}

// WithTx mocks base method.
func (m *MockPaymentInfoRepository) WithTx(tx database.Tx) PaymentInfoRepository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(PaymentInfoRepository)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockPaymentInfoRepositoryMockRecorder) WithTx(tx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockPaymentInfoRepository)(nil).WithTx), tx)
}
