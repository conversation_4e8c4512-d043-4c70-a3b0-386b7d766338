package user

import (
	"database/sql"
	"errors"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/rand"
	"github.com/lib/pq"
)

var (
	packageCategoryRepo     PackageCategoryRepository
	oncePackageCategoryRepo sync.Once
)

type PackageCategoryRepository interface {
	GetByCategories(categories []string) (*dbuser.PackageCategories, error)
}

type packageCategoryRepository struct {
	dbReader database.DB
	dbWriter database.DB
	clock    clock.Clock
	randOp   rand.Rand
}

func NewPackageCategoryRepository() PackageCategoryRepository {
	oncePackageCategoryRepo.Do(func() {
		packageCategoryRepo = &packageCategoryRepository{
			dbReader: container.DBPoolUser().Slave().Unsafe(),
			dbWriter: container.DBPoolUser().Master().Unsafe(),
			clock:    clock.New(),
			randOp:   rand.New(),
		}
	})
	return packageCategoryRepo
}

func (r *packageCategoryRepository) GetByCategories(categories []string) (*dbuser.PackageCategories, error) {
	pkgCategories := new(dbuser.PackageCategories)
	if err := r.dbReader.Select(pkgCategories,
		`SELECT id, category, general, sort
		FROM package_category
		WHERE category = ANY($1)
		ORDER BY
			sort asc;`,
		pq.Array(categories)); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	return pkgCategories, nil
}
