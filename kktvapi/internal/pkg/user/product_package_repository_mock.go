// Code generated by MockGen. DO NOT EDIT.
// Source: product_package_repository.go

// Package user is a generated GoMock package.
package user

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockProductPackageRepository is a mock of ProductPackageRepository interface.
type MockProductPackageRepository struct {
	ctrl     *gomock.Controller
	recorder *MockProductPackageRepositoryMockRecorder
}

// MockProductPackageRepositoryMockRecorder is the mock recorder for MockProductPackageRepository.
type MockProductPackageRepositoryMockRecorder struct {
	mock *MockProductPackageRepository
}

// NewMockProductPackageRepository creates a new mock instance.
func NewMockProductPackageRepository(ctrl *gomock.Controller) *MockProductPackageRepository {
	mock := &MockProductPackageRepository{ctrl: ctrl}
	mock.recorder = &MockProductPackageRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProductPackageRepository) EXPECT() *MockProductPackageRepositoryMockRecorder {
	return m.recorder
}

// ListBillingPkgsByPlatform mocks base method.
func (m *MockProductPackageRepository) ListBillingPkgsByPlatform(platform string) (*dbuser.ProductPackages, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBillingPkgsByPlatform", platform)
	ret0, _ := ret[0].(*dbuser.ProductPackages)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBillingPkgsByPlatform indicates an expected call of ListBillingPkgsByPlatform.
func (mr *MockProductPackageRepositoryMockRecorder) ListBillingPkgsByPlatform(platform interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBillingPkgsByPlatform", reflect.TypeOf((*MockProductPackageRepository)(nil).ListBillingPkgsByPlatform), platform)
}

// ListByPlatform mocks base method.
func (m *MockProductPackageRepository) ListByPlatform(platform string) (*dbuser.ProductPackages, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByPlatform", platform)
	ret0, _ := ret[0].(*dbuser.ProductPackages)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByPlatform indicates an expected call of ListByPlatform.
func (mr *MockProductPackageRepositoryMockRecorder) ListByPlatform(platform interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByPlatform", reflect.TypeOf((*MockProductPackageRepository)(nil).ListByPlatform), platform)
}
