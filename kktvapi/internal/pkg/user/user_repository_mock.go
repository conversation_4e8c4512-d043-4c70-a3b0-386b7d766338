// Code generated by MockGen. DO NOT EDIT.
// Source: user_repository.go

// Package user is a generated GoMock package.
package user

import (
	reflect "reflect"

	database "github.com/KKTV/kktv-api-v3/pkg/database"
	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockUserRepository is a mock of UserRepository interface.
type MockUserRepository struct {
	ctrl     *gomock.Controller
	recorder *MockUserRepositoryMockRecorder
}

// MockUserRepositoryMockRecorder is the mock recorder for MockUserRepository.
type MockUserRepositoryMockRecorder struct {
	mock *MockUserRepository
}

// NewMockUserRepository creates a new mock instance.
func NewMockUserRepository(ctrl *gomock.Controller) *MockUserRepository {
	mock := &MockUserRepository{ctrl: ctrl}
	mock.recorder = &MockUserRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserRepository) EXPECT() *MockUserRepositoryMockRecorder {
	return m.recorder
}

// BatchUpdateByFields mocks base method.
func (m *MockUserRepository) BatchUpdateByFields(userIDs []string, fields map[dbuser.UsersField]interface{}) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateByFields", userIDs, fields)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpdateByFields indicates an expected call of BatchUpdateByFields.
func (mr *MockUserRepositoryMockRecorder) BatchUpdateByFields(userIDs, fields interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateByFields", reflect.TypeOf((*MockUserRepository)(nil).BatchUpdateByFields), userIDs, fields)
}

// Create mocks base method.
func (m *MockUserRepository) Create(user *dbuser.User) (*dbuser.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", user)
	ret0, _ := ret[0].(*dbuser.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockUserRepositoryMockRecorder) Create(user interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockUserRepository)(nil).Create), user)
}

// GetActiveByID mocks base method.
func (m *MockUserRepository) GetActiveByID(userID string) (*dbuser.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveByID", userID)
	ret0, _ := ret[0].(*dbuser.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveByID indicates an expected call of GetActiveByID.
func (mr *MockUserRepositoryMockRecorder) GetActiveByID(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveByID", reflect.TypeOf((*MockUserRepository)(nil).GetActiveByID), userID)
}

// GetByID mocks base method.
func (m *MockUserRepository) GetByID(uid string) (*dbuser.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", uid)
	ret0, _ := ret[0].(*dbuser.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockUserRepositoryMockRecorder) GetByID(uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockUserRepository)(nil).GetByID), uid)
}

// GetByModSubscriberID mocks base method.
func (m *MockUserRepository) GetByModSubscriberID(modID string) (*dbuser.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByModSubscriberID", modID)
	ret0, _ := ret[0].(*dbuser.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByModSubscriberID indicates an expected call of GetByModSubscriberID.
func (mr *MockUserRepositoryMockRecorder) GetByModSubscriberID(modID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByModSubscriberID", reflect.TypeOf((*MockUserRepository)(nil).GetByModSubscriberID), modID)
}

// GetByUserID mocks base method.
func (m *MockUserRepository) GetByUserID(uid string) (*dbuser.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByUserID", uid)
	ret0, _ := ret[0].(*dbuser.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByUserID indicates an expected call of GetByUserID.
func (mr *MockUserRepositoryMockRecorder) GetByUserID(uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByUserID", reflect.TypeOf((*MockUserRepository)(nil).GetByUserID), uid)
}

// GetUsersByEmail mocks base method.
func (m *MockUserRepository) GetUsersByEmail(email string) ([]*dbuser.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsersByEmail", email)
	ret0, _ := ret[0].([]*dbuser.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUsersByEmail indicates an expected call of GetUsersByEmail.
func (mr *MockUserRepositoryMockRecorder) GetUsersByEmail(email interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsersByEmail", reflect.TypeOf((*MockUserRepository)(nil).GetUsersByEmail), email)
}

// GetUsersByPhone mocks base method.
func (m *MockUserRepository) GetUsersByPhone(phone string) ([]*dbuser.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsersByPhone", phone)
	ret0, _ := ret[0].([]*dbuser.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUsersByPhone indicates an expected call of GetUsersByPhone.
func (mr *MockUserRepositoryMockRecorder) GetUsersByPhone(phone interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsersByPhone", reflect.TypeOf((*MockUserRepository)(nil).GetUsersByPhone), phone)
}

// ListFamilyMembersByHostUserID mocks base method.
func (m *MockUserRepository) ListFamilyMembersByHostUserID(userID string) ([]*dbuser.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListFamilyMembersByHostUserID", userID)
	ret0, _ := ret[0].([]*dbuser.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListFamilyMembersByHostUserID indicates an expected call of ListFamilyMembersByHostUserID.
func (mr *MockUserRepositoryMockRecorder) ListFamilyMembersByHostUserID(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListFamilyMembersByHostUserID", reflect.TypeOf((*MockUserRepository)(nil).ListFamilyMembersByHostUserID), userID)
}

// Update mocks base method.
func (m *MockUserRepository) Update(user *dbuser.User) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", user)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockUserRepositoryMockRecorder) Update(user interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockUserRepository)(nil).Update), user)
}

// UpdateByFields mocks base method.
func (m *MockUserRepository) UpdateByFields(userID string, fields map[dbuser.UsersField]interface{}) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateByFields", userID, fields)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateByFields indicates an expected call of UpdateByFields.
func (mr *MockUserRepositoryMockRecorder) UpdateByFields(userID, fields interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateByFields", reflect.TypeOf((*MockUserRepository)(nil).UpdateByFields), userID, fields)
}

// WithTx mocks base method.
func (m *MockUserRepository) WithTx(tx database.Tx) UserRepository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(UserRepository)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockUserRepositoryMockRecorder) WithTx(tx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockUserRepository)(nil).WithTx), tx)
}
