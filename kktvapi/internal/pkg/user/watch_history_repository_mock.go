// Code generated by MockGen. DO NOT EDIT.
// Source: watch_history_repository.go

// Package user is a generated GoMock package.
package user

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockWatchHistoryRepository is a mock of WatchHistoryRepository interface.
type MockWatchHistoryRepository struct {
	ctrl     *gomock.Controller
	recorder *MockWatchHistoryRepositoryMockRecorder
}

// MockWatchHistoryRepositoryMockRecorder is the mock recorder for MockWatchHistoryRepository.
type MockWatchHistoryRepositoryMockRecorder struct {
	mock *MockWatchHistoryRepository
}

// NewMockWatchHistoryRepository creates a new mock instance.
func NewMockWatchHistoryRepository(ctrl *gomock.Controller) *MockWatchHistoryRepository {
	mock := &MockWatchHistoryRepository{ctrl: ctrl}
	mock.recorder = &MockWatchHistoryRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWatchHistoryRepository) EXPECT() *MockWatchHistoryRepositoryMockRecorder {
	return m.recorder
}

// GetByUserID mocks base method.
func (m *MockWatchHistoryRepository) GetByUserID(userID string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByUserID", userID)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByUserID indicates an expected call of GetByUserID.
func (mr *MockWatchHistoryRepositoryMockRecorder) GetByUserID(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByUserID", reflect.TypeOf((*MockWatchHistoryRepository)(nil).GetByUserID), userID)
}

// PurgeAfter mocks base method.
func (m *MockWatchHistoryRepository) PurgeAfter(userID string, index int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PurgeAfter", userID, index)
	ret0, _ := ret[0].(error)
	return ret0
}

// PurgeAfter indicates an expected call of PurgeAfter.
func (mr *MockWatchHistoryRepositoryMockRecorder) PurgeAfter(userID, index interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PurgeAfter", reflect.TypeOf((*MockWatchHistoryRepository)(nil).PurgeAfter), userID, index)
}
