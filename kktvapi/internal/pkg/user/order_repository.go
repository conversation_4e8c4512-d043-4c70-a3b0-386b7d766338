package user

import (
	"database/sql"
	"errors"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

var (
	orderRepo     OrderRepository
	onceOrderRepo sync.Once
)

type OrderRepository interface {
	WithTx(tx database.Tx) OrderRepository
	Insert(order *dbuser.Order) (err error)
	Update(order *dbuser.Order) (err error)
	CancelValuableUnfulfilledByUserId(userID string) error
	NextOrderByUserId(userID string) (*dbuser.Order, error)
	HasEffectiveOrder(userID string) (bool, error)
	GetLastFulfilledByUserId(userID string) (*dbuser.Order, error)
}

type orderRepository struct {
	dbReader database.DB
	dbWriter database.DB
}

func NewOrderRepository() OrderRepository {
	onceOrderRepo.Do(func() {
		orderRepo = &orderRepository{
			dbReader: container.DBPoolUser().Slave(),
			dbWriter: container.DBPoolUser().Master(),
		}
	})
	return orderRepo
}

func NewOrderRepositoryWith(dbReader, dbWriter database.DB) OrderRepository {
	return &orderRepository{
		dbReader: dbReader,
		dbWriter: dbWriter,
	}
}

func NewOrderRepositoryWithTx(db database.DB) OrderRepository {
	orderRepo = &orderRepository{
		dbWriter: db,
	}
	return orderRepo
}

func (r *orderRepository) WithTx(tx database.Tx) OrderRepository {
	return &orderRepository{
		dbWriter: tx,
		dbReader: tx,
	}
}

func (r *orderRepository) Insert(order *dbuser.Order) error {
	_, err := r.dbWriter.NamedExec(
		`INSERT INTO orders
  		(
				id, user_id, product_id, payment_type,
				start_date, end_date, order_date, price,
				price_no_tax, tax_rate, fee, info
			) VALUES
 			(
				:id, :user_id, :product_id, :payment_type,
				:start_date,:end_date,:order_date,:price,
				:price_no_tax, :tax_rate, :fee,:info
			)`, order)
	return err
}

func (r *orderRepository) Update(order *dbuser.Order) error {
	_, err := r.dbWriter.NamedExec(
		`UPDATE orders
		SET status = :status,
			info = :info,
			realized_at = :realized_at,
			price = :price
		WHERE id = :id;`,
		order)
	return err
}

func (r *orderRepository) CancelValuableUnfulfilledByUserId(userID string) error {
	_, err := r.dbWriter.Exec(
		`UPDATE orders
		SET status = 'cancel',
			canceled_at = NOW()
		WHERE user_id = $1
		AND status IS NULL
		AND price > 0::money;`,
		userID)
	return err
}

func (r *orderRepository) NextOrderByUserId(userID string) (*dbuser.Order, error) {
	order := new(dbuser.Order)

	if err := r.dbReader.Get(order,
		`SELECT
			order_date, price::numeric::int
		FROM orders
		WHERE user_id = $1 AND status IS NULL AND price > 0::money
        ORDER BY order_date ASC LIMIT 1
		`,
		userID); err != nil {
		return nil, err
	}

	return order, nil
}
func (r *orderRepository) HasEffectiveOrder(userID string) (bool, error) {
	var numbers int
	if err := r.dbReader.Get(numbers,
		`SELECT 1 FROM orders
        WHERE user_id = $1 AND status IN ('ok', 'error') LIMIT 1
		`,
		userID); err != nil {
		return false, err
	}

	if numbers == 0 {
		return false, nil
	}

	return true, nil
}

func (r *orderRepository) GetLastFulfilledByUserId(userID string) (*dbuser.Order, error) {
	order := new(dbuser.Order)
	query := `SELECT
			id, user_id, product_id,
			price::numeric::int,
			payment_type, status, realized_at
		FROM orders
		WHERE user_id = $1
		AND status = 'ok'
		AND price > 0::money
		AND realized_at IS NOT NULL
    ORDER BY realized_at DESC LIMIT 1`

	if err := r.dbReader.Get(order,
		query, userID); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	return order, nil
}
