package user

import (
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

var (
	unsubscribeRepo     UnsubscribeRepository
	onceUnsubscribeRepo sync.Once
)

type UnsubscribeRepository interface {
	WithTx(tx database.Tx) UnsubscribeRepository
	Create(unsubscribe *dbuser.Unsubscribe) (err error)
}

type unsubscribeRepository struct {
	dbReader database.DB
	dbWriter database.DB
}

func NewUnsubscribeRepository() UnsubscribeRepository {
	onceUnsubscribeRepo.Do(func() {
		unsubscribeRepo = &unsubscribeRepository{
			dbReader: container.DBPoolUser().Slave(),
		}
	})
	return unsubscribeRepo
}

func NewUnsubscribeRepositoryWithTx(db database.DB) UnsubscribeRepository {
	unsubscribeRepo = &unsubscribeRepository{
		dbWriter: container.DBPoolUser().Slave(),
	}
	return unsubscribeRepo
}

func (r *unsubscribeRepository) WithTx(tx database.Tx) UnsubscribeRepository {
	return &unsubscribeRepository{
		dbWriter: tx,
		dbReader: tx,
	}
}

func (r *unsubscribeRepository) Create(unsubscribe *dbuser.Unsubscribe) error {
	_, err := r.dbWriter.NamedExec(
		`INSERT INTO unsubscribe
			(
				user_id, name, email, reason, payment_type, payment_info_snapshot
			) VALUES
			(
				:user_id, :name, :email, :reason, :payment_type, :payment_info_snapshot
			)`, unsubscribe)
	return err
}
