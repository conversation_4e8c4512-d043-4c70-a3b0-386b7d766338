//go:generate mockgen -source playlist_title_repository.go -destination playlist_title_repository_mock.go -package user
package user

import (
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/lib/pq"
)

var (
	playlistTitleRepo     PlaylistTitleRepository
	oncePlaylistTitleRepo sync.Once
)

type PlaylistTitleRepository interface {
	GetPlaylistTitleIDsByPlaylistID(playlistID string) ([]string, error)
	GetPlaylistTitlesByUserIDAndName(userID string, playlistName string) ([]*dbuser.PlaylistTitle, error)
	AddTitlesToPlaylist(playlistID string, titleIDs []string) ([]string, error)
	RemoveTitlesFromPlaylist(playlistID string, titleIDs []string) error
	IsTitleInPlaylist(playlistID string, titleID string) (bool, error)
	CountTitlesInPlaylist(playlistID string) (int, error)
}

type playlistTitleRepository struct {
	dbReader database.DB
	dbWriter database.DB
	clock    clock.Clock
}

func NewPlaylistTitleRepository() PlaylistTitleRepository {
	oncePlaylistTitleRepo.Do(func() {
		playlistTitleRepo = &playlistTitleRepository{
			dbReader: container.DBPoolUser().Slave().Unsafe(),
			dbWriter: container.DBPoolUser().Master().Unsafe(),
			clock:    clock.New(),
		}
	})
	return playlistTitleRepo
}

func NewPlaylistTitleRepositoryWithDB(dbReader database.DB, dbWriter database.DB) PlaylistTitleRepository {
	return &playlistTitleRepository{
		dbReader: dbReader,
		dbWriter: dbWriter,
		clock:    clock.New(),
	}
}

func (r *playlistTitleRepository) GetPlaylistTitleIDsByPlaylistID(playlistID string) ([]string, error) {
	query := `
		SELECT title_id FROM playlist_titles WHERE playlist_id = $1 order by created_at desc;
	`
	var titleIDs []string
	if err := r.dbReader.Select(&titleIDs, query, playlistID); err != nil {
		return nil, err
	}
	return titleIDs, nil
}

func (r *playlistTitleRepository) AddTitlesToPlaylist(playlistID string, titleIDs []string) ([]string, error) {
	query := `
		WITH inserted AS (
			INSERT INTO playlist_titles (playlist_id, title_id, created_at)
			VALUES ($1, unnest($2::text[]), $3)
			ON CONFLICT DO NOTHING
			RETURNING title_id
		)
		SELECT array_agg(title_id) FROM inserted;
	`
	var insertedTitleIDs []string

	err := r.dbWriter.QueryRowx(query, playlistID, pq.Array(titleIDs), r.clock.Now()).Scan(pq.Array(&insertedTitleIDs))
	if err != nil {
		return nil, err
	}
	return insertedTitleIDs, nil
}

func (r *playlistTitleRepository) RemoveTitlesFromPlaylist(playlistID string, titleIDs []string) error {
	query := `
		DELETE FROM playlist_titles WHERE playlist_id = $1 AND title_id = ANY($2);
	`
	_, err := r.dbWriter.Exec(query, playlistID, pq.Array(titleIDs))
	return err
}

func (r *playlistTitleRepository) IsTitleInPlaylist(playlistID string, titleID string) (bool, error) {
	query := `
		SELECT COUNT(*) FROM playlist_titles WHERE playlist_id = $1 AND title_id = $2;
	`
	var count int
	err := r.dbReader.Get(&count, query, playlistID, titleID)
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

func (r *playlistTitleRepository) GetPlaylistTitlesByUserIDAndName(userID string, playlistName string) ([]*dbuser.PlaylistTitle, error) {
	query := `
		SELECT pt.*
		FROM playlist_titles pt INNER JOIN user_playlists up ON pt.playlist_id = up.id
		WHERE up.user_id = $1 AND up.name = $2 ORDER BY created_at DESC;
	`
	var playlistTitles []*dbuser.PlaylistTitle
	if err := r.dbReader.Select(&playlistTitles, query, userID, playlistName); err != nil {
		return nil, err
	}
	return playlistTitles, nil
}

func (r *playlistTitleRepository) CountTitlesInPlaylist(playlistID string) (int, error) {
	query := `
		SELECT COUNT(*) FROM playlist_titles WHERE playlist_id = $1;
	`
	var count int
	err := r.dbReader.Get(&count, query, playlistID)
	if err != nil {
		return 0, err
	}
	return count, nil
}
