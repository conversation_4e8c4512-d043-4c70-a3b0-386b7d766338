// Code generated by MockGen. DO NOT EDIT.
// Source: user_survey_repository.go

// Package user is a generated GoMock package.
package user

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockUserSurveyRepository is a mock of UserSurveyRepository interface.
type MockUserSurveyRepository struct {
	ctrl     *gomock.Controller
	recorder *MockUserSurveyRepositoryMockRecorder
}

// MockUserSurveyRepositoryMockRecorder is the mock recorder for MockUserSurveyRepository.
type MockUserSurveyRepositoryMockRecorder struct {
	mock *MockUserSurveyRepository
}

// NewMockUserSurveyRepository creates a new mock instance.
func NewMockUserSurveyRepository(ctrl *gomock.Controller) *MockUserSurveyRepository {
	mock := &MockUserSurveyRepository{ctrl: ctrl}
	mock.recorder = &MockUserSurveyRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserSurveyRepository) EXPECT() *MockUserSurveyRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockUserSurveyRepository) Create(userSurvey *dbuser.UserSurvey) (*dbuser.UserSurvey, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", userSurvey)
	ret0, _ := ret[0].(*dbuser.UserSurvey)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockUserSurveyRepositoryMockRecorder) Create(userSurvey interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockUserSurveyRepository)(nil).Create), userSurvey)
}

// GetByUserIDAndSurveyID mocks base method.
func (m *MockUserSurveyRepository) GetByUserIDAndSurveyID(userID string, surveyID int64) (*dbuser.UserSurvey, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByUserIDAndSurveyID", userID, surveyID)
	ret0, _ := ret[0].(*dbuser.UserSurvey)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByUserIDAndSurveyID indicates an expected call of GetByUserIDAndSurveyID.
func (mr *MockUserSurveyRepositoryMockRecorder) GetByUserIDAndSurveyID(userID, surveyID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByUserIDAndSurveyID", reflect.TypeOf((*MockUserSurveyRepository)(nil).GetByUserIDAndSurveyID), userID, surveyID)
}
