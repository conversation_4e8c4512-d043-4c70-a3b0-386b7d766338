// Code generated by MockGen. DO NOT EDIT.
// Source: kkbox_billing_prime_member_repository.go

// Package user is a generated GoMock package.
package user

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockPrimeMemberRepository is a mock of PrimeMemberRepository interface.
type MockPrimeMemberRepository struct {
	ctrl     *gomock.Controller
	recorder *MockPrimeMemberRepositoryMockRecorder
}

// MockPrimeMemberRepositoryMockRecorder is the mock recorder for MockPrimeMemberRepository.
type MockPrimeMemberRepositoryMockRecorder struct {
	mock *MockPrimeMemberRepository
}

// NewMockPrimeMemberRepository creates a new mock instance.
func NewMockPrimeMemberRepository(ctrl *gomock.Controller) *MockPrimeMemberRepository {
	mock := &MockPrimeMemberRepository{ctrl: ctrl}
	mock.recorder = &MockPrimeMemberRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPrimeMemberRepository) EXPECT() *MockPrimeMemberRepositoryMockRecorder {
	return m.recorder
}

// GetByMsnoSub mocks base method.
func (m *MockPrimeMemberRepository) GetByMsnoSub(sub string) (*dbuser.KKBOXBillingPrimeMember, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByMsnoSub", sub)
	ret0, _ := ret[0].(*dbuser.KKBOXBillingPrimeMember)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByMsnoSub indicates an expected call of GetByMsnoSub.
func (mr *MockPrimeMemberRepositoryMockRecorder) GetByMsnoSub(sub interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByMsnoSub", reflect.TypeOf((*MockPrimeMemberRepository)(nil).GetByMsnoSub), sub)
}
