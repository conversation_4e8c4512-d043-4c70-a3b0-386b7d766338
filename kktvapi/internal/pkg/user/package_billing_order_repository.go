//go:generate mockgen -source package_billing_order_repository.go -destination package_billing_order_repository_mock.go -package user
package user

import (
	"database/sql"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/lib/pq"
)

var (
	packageBillingOrderRepo     PackageBillingOrderRepository
	oncePackageBillingOrderRepo sync.Once
)

type PackageBillingOrderRepository interface {
	GetByBillingOrderNumber(billingOrderNumber string) (*dbuser.PackageBillingOrder, error)
	GetByBillingOrderNumbers(billingOrderNumbers []string) ([]*dbuser.PackageBillingOrder, error)
	ListByUserID(userID string) ([]*dbuser.PackageBillingOrder, error)
	Create(order *dbuser.PackageBillingOrder) error
}

type packageBillingOrderRepository struct {
	dbReader database.DB
	dbWriter database.DB
}

func NewPackageBillingOrderRepository() PackageBillingOrderRepository {
	oncePackageBillingOrderRepo.Do(func() {
		packageBillingOrderRepo = &packageBillingOrderRepository{
			dbReader: container.DBPoolUser().Slave(),
			dbWriter: container.DBPoolUser().Master(),
		}
	})
	return packageBillingOrderRepo
}

func NewPackageBillingOrderRepositoryWith(dbReader, dbWriter database.DB) PackageBillingOrderRepository {
	return &packageBillingOrderRepository{
		dbReader: dbReader,
		dbWriter: dbWriter,
	}
}

func (r *packageBillingOrderRepository) GetByBillingOrderNumber(billingOrderNumber string) (*dbuser.PackageBillingOrder, error) {
	packageBillingOrder := new(dbuser.PackageBillingOrder)
	query := `SELECT * FROM packages_billing_orders WHERE billing_order_number = $1`

	if err := r.dbReader.Get(packageBillingOrder,
		query, billingOrderNumber); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	return packageBillingOrder, nil
}

func (r *packageBillingOrderRepository) GetByBillingOrderNumbers(billingOrderNumbers []string) ([]*dbuser.PackageBillingOrder, error) {
	var packageBillingOrders []*dbuser.PackageBillingOrder
	if err := r.dbReader.Select(&packageBillingOrders,
		`SELECT * FROM packages_billing_orders WHERE billing_order_number = ANY($1)`,
		pq.Array(billingOrderNumbers)); err != nil {
		return nil, err
	}
	return packageBillingOrders, nil
}

func (r *packageBillingOrderRepository) ListByUserID(userID string) ([]*dbuser.PackageBillingOrder, error) {

	var packageBillingOrders []*dbuser.PackageBillingOrder
	if err := r.dbReader.Select(&packageBillingOrders,
		`SELECT * FROM packages_billing_orders WHERE user_id = $1`,
		userID); err != nil {
		return nil, err
	}
	return packageBillingOrders, nil
}

func (r *packageBillingOrderRepository) Create(order *dbuser.PackageBillingOrder) error {
	if order.ID != 0 {
		return fmt.Errorf("%w: id must be empty", kktverror.ErrInvalidParameter)
	}
	now := time.Now()
	order.CreatedAt, order.UpdatedAt = now, now
	dbFields := database.GetDBFields(order, "id")

	insertSQL := `INSERT INTO packages_billing_orders (` + dbFields.String() + `) VALUES (` + dbFields.NamedString() + `);`

	_, err := r.dbWriter.NamedExec(insertSQL, order)
	if err != nil {
		return err
	}
	return nil
}
