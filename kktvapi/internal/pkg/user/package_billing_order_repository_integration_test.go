package user

import (
	_ "embed"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/dbtest"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"os"
	"testing"
)

type RepositoryTestSuite struct {
	dbtest.Suite

	r    *require.Assertions
	ctrl *gomock.Controller
	repo PackageBillingOrderRepository
}

func init() {
	_ = os.Setenv("TEST_DB_USER_DSN", "postgres://foo:bar@localhost:5432/kktv_users?sslmode=disable")
}

func TestRepositoryIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(RepositoryTestSuite))
}

func (suite *RepositoryTestSuite) SetupSuite() {
	suite.Suite.SetupSuite(os.Getenv("TEST_DB_USER_DSN"))
}

func (suite *RepositoryTestSuite) SetupTest() {
	suite.Suite.SetupTest()

	tx := suite.GetTransaction()
	suite.r = suite.Require()
	suite.ctrl = gomock.NewController(suite.T())
	suite.repo = &packageBillingOrderRepository{
		dbReader: tx,
		dbWriter: tx,
	}
}

func (suite *RepositoryTestSuite) TearDownTest() {
	suite.Suite.TearDownTest()
}

func (suite *RepositoryTestSuite) TestGetByBillingOrderNumbers() {
	suite.givenPackageBillingOrders()

	testcases := []struct {
		name                string
		billingOrderNumbers []string
		then                func(packageBillingOrders []*dbuser.PackageBillingOrder, err error)
	}{
		{
			name: "get package billing orders by billing order numbers successfully",
			billingOrderNumbers: []string{
				"20230825504947",
				"20230825160682",
				"20230825395492",
			},
			then: func(packageBillingOrders []*dbuser.PackageBillingOrder, err error) {
				suite.r.NoError(err)
				suite.r.Len(packageBillingOrders, 3)
				suite.r.Equal("20230825504947", packageBillingOrders[0].BillingOrderNumber)
				suite.r.Equal("20230825160682", packageBillingOrders[1].BillingOrderNumber)
				suite.r.Equal("20230825395492", packageBillingOrders[2].BillingOrderNumber)
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			packageBillingOrders, err := suite.repo.GetByBillingOrderNumbers(tc.billingOrderNumbers)
			tc.then(packageBillingOrders, err)
		})
	}
}

//go:embed testdata/package_billing_order_repository/Repo.sql
var dataForPackageBillingOrderRepo string

func (suite *RepositoryTestSuite) givenPackageBillingOrders() {
	if _, err := suite.GetTransaction().Exec(dataForPackageBillingOrderRepo); err != nil {
		suite.T().Fatal("failed to insert test data for package billing order:", err)
	}
}
