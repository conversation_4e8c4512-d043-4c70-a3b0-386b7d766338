//go:generate mockgen -source kkbox_billing_prime_member_repository.go -destination kkbox_billing_prime_member_repository_mock.go -package user
package user

import (
	"database/sql"
	"errors"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"sync"
)

var (
	primeMemberRepo     PrimeMemberRepository
	oncePrimeMemberRepo sync.Once
)

type PrimeMemberRepository interface {
	GetByMsnoSub(sub string) (*dbuser.KKBOXBillingPrimeMember, error)
}

type primeMemberRepository struct {
	dbReader database.DB
}

func NewPrimeMemberRepository() PrimeMemberRepository {
	oncePrimeMemberRepo.Do(func() {
		primeMemberRepo = &primeMemberRepository{
			dbReader: container.DBPoolUser().Slave().Unsafe(),
		}
	})
	return primeMemberRepo
}

func (p *primeMemberRepository) GetByMsnoSub(sub string) (*dbuser.KKBOXBillingPrimeMember, error) {
	primeMember := new(dbuser.KKBOXBillingPrimeMember)
	query := `SELECT * FROM kkbox_billing_prime_member WHERE msno_sub = $1;`
	if err := p.dbReader.Get(primeMember, query, sub); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return primeMember, nil
}
