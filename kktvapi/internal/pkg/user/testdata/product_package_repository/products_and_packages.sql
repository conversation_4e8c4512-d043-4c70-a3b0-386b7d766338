INSERT INTO public.products (id, "name", country, price, duration, created_at, updated_at, deleted_at, "payment_type",
                             currency, price_no_tax, tax_rate, item_name, item_unit, auto_renew, active, free_duration,
                             as_subscribe, fee, payment_type_code, purchase_upper_limit_count, external_product_id,
                             discount_duration, discount_price, bundle, sort, category, fee_rate, discount_price_no_tax,
                             discount_fee)
VALUES (1, 'pxpayplus.119', 'TW', 119, '1 mon'::interval, '2021-06-08 15:24:01.134', NULL, NULL,
        'pxpayplus'::payment_type, 'NTD', 113.00, 5, '台灣漫讀會員專屬優惠', '期', true, true, '00:00:00'::interval,
        false, 2, '00', 0, NULL, '60 days'::interval, 99.00,
        '{"title": "立即加入 KKTV VIP", "subtitle": "樂享最優質的追劇體驗", "url_name": "bookwalkerKKTV.cc.119", "text_color": "#ffffff", "description": "無廣告超高畫質觀劇體驗\n加入離線觀看播放更順暢\n投放大螢幕超享受\n多樣內容同步跟播最即時", "needRedeemCode": true, "package_description": "全支付首度KKTV合作"}'::jsonb,
        0, 'Channel', 1.90, 94.00, 1);
INSERT INTO public.products (id, "name", country, price, duration, created_at, updated_at, deleted_at, "payment_type",
                             currency, price_no_tax, tax_rate, item_name, item_unit, auto_renew, active, free_duration,
                             as_subscribe, fee, payment_type_code, purchase_upper_limit_count, external_product_id,
                             discount_duration, discount_price, bundle, sort, category, fee_rate, discount_price_no_tax,
                             discount_fee)
VALUES (2, 'creditcard.promo.visa', 'TW', 119.00, '1 mon'::interval, '2020-04-20 17:26:00.327', NULL, NULL,
        'credit_card'::payment_type, 'NTD', 113.00, 5, 'VISA專案，首三個月9元', '月', true, true, '00:00:00'::interval,
        true, 2, '00', 0, NULL, '3 mons'::interval, 9.00, '{"prefix": ["4"]}'::jsonb, 0, 'Channel', 1.90, 9.00, 0);
INSERT INTO public.products (id, "name", country, price, duration, created_at, updated_at, deleted_at, "payment_type",
                             currency, price_no_tax, tax_rate, item_name, item_unit, auto_renew, active, free_duration,
                             as_subscribe, fee, payment_type_code, purchase_upper_limit_count, external_product_id,
                             discount_duration, discount_price, bundle, sort, category, fee_rate, discount_price_no_tax,
                             discount_fee)
VALUES (3, '2021Bonenkai.cc.119', 'TW', 119.00, '1 mon'::interval, '2021-11-10 20:13:19.982', NULL, NULL,
        'credit_card'::payment_type, 'NTD', 113.00, 5, '2021旺年會｜月繳優惠-首60天$11，之後每月$119', '期', true, true,
        '00:00:00'::interval, true, 2, '00', 0, NULL, '60 days'::interval, 3999.00,
        '{"title": "超殺優惠！限時搶購", "url_name": "2021Bonenkai.cc.119", "text_color": "#FFFFFF", "description": "精選好劇隨心看，最優質的追劇體驗", "campaign_image": "product/campaign_125_1636546420.png", "background_color": "#093251", "background_image": "product/bkimage_125_1636546420.png", "package_description": "優惠期間：2021/11/11～2021/12/31。\n購買本優惠方案，首購帳號可享有首 60天 只要 NT$11 的首購優惠，優惠期結束後，將自動續訂，並被收取月繳優惠價。\n「2021旺年會」月繳優惠價為：每月 NT119（原價 $149/月）KKTV 將每月定期自動向您收取，可隨時退訂。\n本「2021旺年會｜月繳優惠」限定使用信用卡付款，不得與 KKTV 其他優惠活動或方案合併使用，限非 KKTV 訂閱式付費會員 購買。\n每組 KKTV 帳號皆限享用本優惠乙次。\n須於購買使用本優惠方案前詳盡閱讀並同意「KKTV 服務條款」內所有的規範與權益。\nKKTV 保留同意用戶參加本活動與否，隨時終止或變更活動之權利。\n計費週期起始日為您申請付費訂閱後的隔日起算，訂閱期間內申請取消訂閱，未滿一個週期(月/季/年)以一個週期(月/季/年)計算。[了解 VIP 有效期間及計費週期](https://help.kkbox.com/tw/zh-tw/payment/plans/2686?p=kktv)\n如有疑問，請至KKTV線上客服快易通詢問：[https://cs.kkbox.com/tw/kktv\b](https://cs.kkbox.com/tw/kktv\b)"}'::jsonb,
        4, 'Organic', 1.90, 3809.00, 75);



INSERT INTO public.product_packages (platform, price, duration, title, description, button_text, "label", created_at,
                                     updated_at, product_ids, active, highlight, auto_renew, sort, promotion, info,
                                     category, pay_duration, billing_product_ids)
VALUES ('android', 149, '月', '續訂懶人包', '每月只要150元，首訂享首月免費！日台陸韓港劇、動漫、綜藝隨選隨看。',
        '立即升級', '', '2017-12-12 16:04:28.658', '2020-09-23 15:40:24.000', '[3]'::jsonb, true, '', false, 1, NULL,
        NULL, NULL, NULL, '[]'::jsonb);
INSERT INTO public.product_packages (platform, price, duration, title, description, button_text, "label", created_at,
                                     updated_at, product_ids, active, highlight, auto_renew, sort, promotion, info,
                                     category, pay_duration, billing_product_ids)
VALUES ('web', 1788, '年', '信用卡每年扣繳', '', '立即訂閱', '最優惠', '2020-08-10 14:57:37.000',
        '2020-11-10 17:18:06.000', '[1]'::jsonb, false, '每年加送60天', true, 0, NULL, NULL, NULL, NULL, '[]'::jsonb);
INSERT INTO public.product_packages (platform, price, duration, title, description, button_text, "label", created_at,
                                     updated_at, product_ids, active, highlight, auto_renew, sort, promotion, info,
                                     category, pay_duration, billing_product_ids)
VALUES ('campaign', 999, '年', '貓肥家潤', '', '立即訂閱', NULL, '2020-08-10 17:15:16.000',
        '2020-08-10 17:15:16.000', '[1]'::jsonb, true, '貓貓肥，家家潤', true, 0, NULL, NULL, NULL, NULL, '[]'::jsonb);
INSERT INTO public.product_packages (platform, price, duration, title, description, button_text, "label", created_at,
                                     updated_at, product_ids, active, highlight, auto_renew, sort, promotion, info,
                                     category, pay_duration, billing_product_ids)
VALUES ('campaign', 200, '月', 'test_cam', '測試', 'btn_test', NULL, '2020-08-14 11:02:10.000',
        '2020-08-17 10:57:22.000', '[2]'::jsonb, true, 'KKTV', false, 0, NULL, NULL, NULL, NULL, '[]'::jsonb);
