package user

import (
	_ "embed"
	"os"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/dbtest"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
)

type KktvMemberBackupRepositoryTestSuite struct {
	dbtest.Suite
	r *require.Assertions

	ctrl      *gomock.Controller
	mockClock *clock.MockClock
	repo      BackupRepository
}

func (suite *KktvMemberBackupRepositoryTestSuite) SetupTest() {
	suite.Suite.SetupTest()
	tx := suite.GetTransaction()

	suite.ctrl = gomock.NewController(suite.T())
	suite.mockClock = clock.NewMockClock(suite.ctrl)

	suite.repo = &backupRepository{
		dbReader: tx,
		dbWriter: tx,
	}
	suite.r = suite.Require()
}

func (suite *KktvMemberBackupRepositoryTestSuite) TearDownTest() {
	suite.Suite.TearDownTest()
}

func (suite *KktvMemberBackupRepositoryTestSuite) SetupSuite() {
	suite.Suite.SetupSuite(os.Getenv("TEST_DB_USER_DSN"))
}

func init() {
	os.Setenv("TEST_DB_USER_DSN", "postgres://foo:bar@localhost:5432/kktv_users?sslmode=disable")
}

func (suite *KktvMemberBackupRepositoryTestSuite) TestGetUserFromKKIDBackup() {
	suite.givenUsers()

	testcases := []struct {
		name    string
		then    func(created *dbuser.KkidBackupUser, err error)
		account string
	}{
		{
			name: "retrieve kkid backup user success",
			then: func(user *dbuser.KkidBackupUser, err error) {
				suite.r.NoError(err)
				suite.r.Equal("<EMAIL>", user.Email)
			},
			account: "<EMAIL>",
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			user, err := suite.repo.GetUserFromKKIDBackup(tc.account)
			tc.then(user, err)
		})
	}
}

//go:embed testdata/kktv_member_backup_repository/BackupRepo.sql
var dataForBackupRepo string

func (suite *KktvMemberBackupRepositoryTestSuite) givenUsers() {
	if _, err := suite.GetTransaction().Exec(dataForBackupRepo); err != nil {
		suite.Require().Fail("fail to insert test data", err)
	}
}
