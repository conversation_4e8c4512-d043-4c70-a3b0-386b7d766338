//go:generate mockgen -source kktv_member_backup_repository.go -destination kktv_member_backup_repository_mock.go -package user
package user

import (
	"database/sql"
	"errors"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

var (
	backupRepo     BackupRepository
	onceBackupRepo sync.Once
)

type BackupRepository interface {
	GetUserFromKKIDBackup(account string) (*dbuser.KkidBackupUser, error)
}

type backupRepository struct {
	dbReader database.DB
	dbWriter database.DB
}

func NewBackupRepository() BackupRepository {
	onceBackupRepo.Do(func() {
		backupRepo = &backupRepository{
			dbReader: container.DBPoolUser().Slave().Unsafe(),
			dbWriter: container.DBPoolUser().Master().Unsafe(),
		}
	})
	return backupRepo
}

func NewBackupRepositoryWithTx(db database.DB) BackupRepository {
	return &backupRepository{
		dbWriter: db,
		dbReader: db,
	}
}

func (r *backupRepository) GetUserFromKKIDBackup(account string) (*dbuser.KkidBackupUser, error) {
	kkidUser := new(dbuser.KkidBackupUser)
	if err := r.dbReader.Get(kkidUser,
		`SELECT *
		FROM kktv_members_from_kkid
		WHERE email = $1 or phone = $1;`,
		account); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	return kkidUser, nil
}
