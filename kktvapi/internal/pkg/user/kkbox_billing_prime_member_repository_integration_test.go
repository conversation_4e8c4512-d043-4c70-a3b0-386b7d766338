package user

import (
	_ "embed"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/dbtest"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"os"
	"testing"
)

type PrimeMemberRepositoryTestSuite struct {
	dbtest.Suite
	r    *require.Assertions
	ctrl *gomock.Controller
	repo PrimeMemberRepository
}

func TestPrimeMemberRepositoryIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(PrimeMemberRepositoryTestSuite))
}

func (suite *PrimeMemberRepositoryTestSuite) SetupTest() {
	suite.Suite.SetupTest()
	tx := suite.GetTransaction()

	suite.ctrl = gomock.NewController(suite.T())
	suite.repo = &primeMemberRepository{
		dbReader: tx,
	}

	suite.r = suite.Require()
}

func (suite *PrimeMemberRepositoryTestSuite) TearDownTest() {
	suite.Suite.TearDownTest()
}

func (suite *PrimeMemberRepositoryTestSuite) SetupSuite() {
	suite.Suite.SetupSuite(os.Getenv("TEST_DB_USER_DSN"))
}

func init() {
	_ = os.Setenv("TEST_DB_USER_DSN", "postgres://foo:bar@localhost:5432/kktv_users?sslmode=disable")
}

func (suite *PrimeMemberRepositoryTestSuite) TestGetByMsnoSub() {
	suite.givenPrimeMembers()
	msnoSub := "prime-test-001"
	testcases := []struct {
		name    string
		msnoSub string
		then    func(primeMember *dbuser.KKBOXBillingPrimeMember, err error)
	}{
		{
			name:    "found prime member",
			msnoSub: msnoSub,
			then: func(primeMember *dbuser.KKBOXBillingPrimeMember, err error) {
				suite.r.NoError(err)
				suite.r.Equal(msnoSub, primeMember.MsnoSub)
			},
		},
		{
			name:    "not found prime member",
			msnoSub: "prime-test-002",
			then: func(primeMember *dbuser.KKBOXBillingPrimeMember, err error) {
				suite.r.NoError(err)
				suite.r.Nil(primeMember)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.then(suite.repo.GetByMsnoSub(tc.msnoSub))
		})
	}
}

//go:embed testdata/kkbox_billing_prime_member/PrimeMember.sql
var dataForPrimeMemberRepo string

func (suite *PrimeMemberRepositoryTestSuite) givenPrimeMembers() {
	if _, err := suite.GetTransaction().Exec(dataForPrimeMemberRepo); err != nil {
		suite.Require().Fail("fail to insert test data", err)
	}
}
