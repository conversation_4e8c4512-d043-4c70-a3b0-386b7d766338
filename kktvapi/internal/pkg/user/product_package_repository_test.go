package user

import (
	_ "embed"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/dbtest"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/stretchr/testify/suite"
	"os"
	"testing"
)

type ProductPackageRepositoryIntegrationTestSuite struct {
	dbtest.Suite
	repo ProductPackageRepository
}

func TestProductPackageRepositoryIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(ProductPackageRepositoryIntegrationTestSuite))
}

func (suite *ProductPackageRepositoryIntegrationTestSuite) SetupSuite() {
	suite.Suite.SetupSuite(os.Getenv("TEST_DB_USER_DSN"))
}

func (suite *ProductPackageRepositoryIntegrationTestSuite) SetupTest() {
	suite.Suite.SetupTest()
	db := suite.GetTransaction()
	suite.repo = &productPackageRepository{
		dbReader: db,
		dbWriter: db,
	}
}

func (suite *ProductPackageRepositoryIntegrationTestSuite) TearDownTest() {
	suite.Suite.TearDownTest()
}

//go:embed testdata/product_package_repository/products_and_packages.sql
var dataForProductPackageRepo string

func (suite *ProductPackageRepositoryIntegrationTestSuite) TestListByPlatform() {
	testcases := []struct {
		name     string
		platform string
		assert   func(result *dbuser.ProductPackages, err error)
	}{
		{
			name:     "found nothing",
			platform: "foo",
			assert: func(result *dbuser.ProductPackages, err error) {
				suite.NoError(err)
				suite.Equal(0, len(*result))
			},
		},
		{
			name:     "given platform",
			platform: "campaign",
			assert: func(result *dbuser.ProductPackages, err error) {
				suite.NoError(err)
				suite.Equal(2, len(*result))
				pkgNames := []string{"貓肥家潤", "test_cam"}
				for i, pkg := range *result {
					suite.Equal(pkgNames[i], pkg.Title)
					suite.Equal("campaign", pkg.Platform)
				}
			},
		},
	}
	suite.cleanTableAndInsert([]string{"orders", "product_packages_to_products", "product_packages", "products"}, dataForProductPackageRepo)
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			result, err := suite.repo.ListByPlatform(tc.platform)
			tc.assert(result, err)
		})
	}
}

func (suite *ProductPackageRepositoryIntegrationTestSuite) cleanTableAndInsert(tables []string, insertSQL string) {
	suite.DeleteFromTable(tables...)
	if _, err := suite.GetTransaction().Exec(insertSQL); err != nil {
		suite.T().Fatal("failed to insert test data for product package repository:", err)
	}
}
