// Code generated by MockGen. DO NOT EDIT.
// Source: order_repository.go

// Package user is a generated GoMock package.
package user

import (
	reflect "reflect"

	database "github.com/KKTV/kktv-api-v3/pkg/database"
	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockOrderRepository is a mock of OrderRepository interface.
type MockOrderRepository struct {
	ctrl     *gomock.Controller
	recorder *MockOrderRepositoryMockRecorder
}

// MockOrderRepositoryMockRecorder is the mock recorder for MockOrderRepository.
type MockOrderRepositoryMockRecorder struct {
	mock *MockOrderRepository
}

// NewMockOrderRepository creates a new mock instance.
func NewMockOrderRepository(ctrl *gomock.Controller) *MockOrderRepository {
	mock := &MockOrderRepository{ctrl: ctrl}
	mock.recorder = &MockOrderRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrderRepository) EXPECT() *MockOrderRepositoryMockRecorder {
	return m.recorder
}

// CancelValuableUnfulfilledByUserId mocks base method.
func (m *MockOrderRepository) CancelValuableUnfulfilledByUserId(userID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelValuableUnfulfilledByUserId", userID)
	ret0, _ := ret[0].(error)
	return ret0
}

// CancelValuableUnfulfilledByUserId indicates an expected call of CancelValuableUnfulfilledByUserId.
func (mr *MockOrderRepositoryMockRecorder) CancelValuableUnfulfilledByUserId(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelValuableUnfulfilledByUserId", reflect.TypeOf((*MockOrderRepository)(nil).CancelValuableUnfulfilledByUserId), userID)
}

// GetLastFulfilledByUserId mocks base method.
func (m *MockOrderRepository) GetLastFulfilledByUserId(userID string) (*dbuser.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastFulfilledByUserId", userID)
	ret0, _ := ret[0].(*dbuser.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastFulfilledByUserId indicates an expected call of GetLastFulfilledByUserId.
func (mr *MockOrderRepositoryMockRecorder) GetLastFulfilledByUserId(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastFulfilledByUserId", reflect.TypeOf((*MockOrderRepository)(nil).GetLastFulfilledByUserId), userID)
}

// HasEffectiveOrder mocks base method.
func (m *MockOrderRepository) HasEffectiveOrder(userID string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasEffectiveOrder", userID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HasEffectiveOrder indicates an expected call of HasEffectiveOrder.
func (mr *MockOrderRepositoryMockRecorder) HasEffectiveOrder(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasEffectiveOrder", reflect.TypeOf((*MockOrderRepository)(nil).HasEffectiveOrder), userID)
}

// Insert mocks base method.
func (m *MockOrderRepository) Insert(order *dbuser.Order) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", order)
	ret0, _ := ret[0].(error)
	return ret0
}

// Insert indicates an expected call of Insert.
func (mr *MockOrderRepositoryMockRecorder) Insert(order interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockOrderRepository)(nil).Insert), order)
}

// NextOrderByUserId mocks base method.
func (m *MockOrderRepository) NextOrderByUserId(userID string) (*dbuser.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NextOrderByUserId", userID)
	ret0, _ := ret[0].(*dbuser.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NextOrderByUserId indicates an expected call of NextOrderByUserId.
func (mr *MockOrderRepositoryMockRecorder) NextOrderByUserId(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NextOrderByUserId", reflect.TypeOf((*MockOrderRepository)(nil).NextOrderByUserId), userID)
}

// Update mocks base method.
func (m *MockOrderRepository) Update(order *dbuser.Order) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", order)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockOrderRepositoryMockRecorder) Update(order interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockOrderRepository)(nil).Update), order)
}

// WithTx mocks base method.
func (m *MockOrderRepository) WithTx(tx database.Tx) OrderRepository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(OrderRepository)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockOrderRepositoryMockRecorder) WithTx(tx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockOrderRepository)(nil).WithTx), tx)
}
