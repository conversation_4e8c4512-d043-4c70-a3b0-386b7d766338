//go:generate mockgen -source user_playlist_repository.go -destination user_playlist_repository_mock.go -package user
package user

import (
	"database/sql"
	"errors"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/rand"
)

var (
	userPlaylistRepo     UserPlaylistRepository
	onceUserPlaylistRepo sync.Once
)

type UserPlaylistRepository interface {
	CreatePlaylist(userID string, name string) (*dbuser.UserPlaylist, error)
	GetPlaylistByUserIDAndName(userID string, name string) (*dbuser.UserPlaylist, error)
	GetFavoritePlaylist(userID string) (*dbuser.UserPlaylist, error)
}

type userPlaylistRepository struct {
	dbReader database.DB
	dbWriter database.DB
	clock    clock.Clock
	randOp   rand.Rand
}

func NewUserPlaylistRepository() UserPlaylistRepository {
	onceUserPlaylistRepo.Do(func() {
		userPlaylistRepo = &userPlaylistRepository{
			dbReader: container.DBPoolUser().Slave().Unsafe(),
			dbWriter: container.DBPoolUser().Master().Unsafe(),
			clock:    clock.New(),
			randOp:   rand.New(),
		}
	})
	return userPlaylistRepo
}

func NewUserPlaylistRepositoryWithDB(dbReader database.DB, dbWriter database.DB) UserPlaylistRepository {
	return &userPlaylistRepository{
		dbReader: dbReader,
		dbWriter: dbWriter,
		clock:    clock.New(),
		randOp:   rand.New(),
	}
}

func (r *userPlaylistRepository) CreatePlaylist(userID string, name string) (*dbuser.UserPlaylist, error) {
	ID := r.randOp.UUID()
	createdAt := r.clock.Now()
	userPlaylist := &dbuser.UserPlaylist{
		ID:        ID,
		UserID:    userID,
		Name:      name,
		CreatedAt: createdAt,
	}
	dbFields := database.GetDBFields(userPlaylist)

	insertSQL := `INSERT INTO user_playlists (` + dbFields.String() + `) VALUES (` + dbFields.NamedString() + `);`

	if _, err := r.dbWriter.NamedExec(insertSQL, userPlaylist); err != nil {
		return nil, err
	}

	return userPlaylist, nil
}

func (r *userPlaylistRepository) GetPlaylistByUserIDAndName(userID string, name string) (*dbuser.UserPlaylist, error) {
	userPlaylist := &dbuser.UserPlaylist{}
	if err := r.dbReader.Get(userPlaylist, "SELECT * FROM user_playlists WHERE user_id = $1 AND name = $2", userID, name); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	return userPlaylist, nil
}

func (r *userPlaylistRepository) GetFavoritePlaylist(userID string) (*dbuser.UserPlaylist, error) {
	return r.GetPlaylistByUserIDAndName(userID, "favorite")
}
