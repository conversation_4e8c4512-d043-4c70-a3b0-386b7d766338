// Code generated by MockGen. DO NOT EDIT.
// Source: package_billing_order_repository.go

// Package user is a generated GoMock package.
package user

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockPackageBillingOrderRepository is a mock of PackageBillingOrderRepository interface.
type MockPackageBillingOrderRepository struct {
	ctrl     *gomock.Controller
	recorder *MockPackageBillingOrderRepositoryMockRecorder
}

// MockPackageBillingOrderRepositoryMockRecorder is the mock recorder for MockPackageBillingOrderRepository.
type MockPackageBillingOrderRepositoryMockRecorder struct {
	mock *MockPackageBillingOrderRepository
}

// NewMockPackageBillingOrderRepository creates a new mock instance.
func NewMockPackageBillingOrderRepository(ctrl *gomock.Controller) *MockPackageBillingOrderRepository {
	mock := &MockPackageBillingOrderRepository{ctrl: ctrl}
	mock.recorder = &MockPackageBillingOrderRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPackageBillingOrderRepository) EXPECT() *MockPackageBillingOrderRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockPackageBillingOrderRepository) Create(order *dbuser.PackageBillingOrder) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", order)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockPackageBillingOrderRepositoryMockRecorder) Create(order interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockPackageBillingOrderRepository)(nil).Create), order)
}

// GetByBillingOrderNumber mocks base method.
func (m *MockPackageBillingOrderRepository) GetByBillingOrderNumber(billingOrderNumber string) (*dbuser.PackageBillingOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByBillingOrderNumber", billingOrderNumber)
	ret0, _ := ret[0].(*dbuser.PackageBillingOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByBillingOrderNumber indicates an expected call of GetByBillingOrderNumber.
func (mr *MockPackageBillingOrderRepositoryMockRecorder) GetByBillingOrderNumber(billingOrderNumber interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByBillingOrderNumber", reflect.TypeOf((*MockPackageBillingOrderRepository)(nil).GetByBillingOrderNumber), billingOrderNumber)
}

// GetByBillingOrderNumbers mocks base method.
func (m *MockPackageBillingOrderRepository) GetByBillingOrderNumbers(billingOrderNumbers []string) ([]*dbuser.PackageBillingOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByBillingOrderNumbers", billingOrderNumbers)
	ret0, _ := ret[0].([]*dbuser.PackageBillingOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByBillingOrderNumbers indicates an expected call of GetByBillingOrderNumbers.
func (mr *MockPackageBillingOrderRepositoryMockRecorder) GetByBillingOrderNumbers(billingOrderNumbers interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByBillingOrderNumbers", reflect.TypeOf((*MockPackageBillingOrderRepository)(nil).GetByBillingOrderNumbers), billingOrderNumbers)
}

// ListByUserID mocks base method.
func (m *MockPackageBillingOrderRepository) ListByUserID(userID string) ([]*dbuser.PackageBillingOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByUserID", userID)
	ret0, _ := ret[0].([]*dbuser.PackageBillingOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByUserID indicates an expected call of ListByUserID.
func (mr *MockPackageBillingOrderRepositoryMockRecorder) ListByUserID(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByUserID", reflect.TypeOf((*MockPackageBillingOrderRepository)(nil).ListByUserID), userID)
}
