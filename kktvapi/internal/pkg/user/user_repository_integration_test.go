package user

import (
	_ "embed"
	"os"
	"testing"
	"time"

	"github.com/jmoiron/sqlx"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/dbtest"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/rand"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gopkg.in/guregu/null.v3"
)

type UserRepositoryTestSuite struct {
	dbtest.Suite
	r *require.Assertions

	ctrl      *gomock.Controller
	mockClock *clock.MockClock
	repo      UserRepository
}

func TestUserRepositoryIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(UserRepositoryTestSuite))
}

func (suite *UserRepositoryTestSuite) SetupTest() {
	suite.Suite.SetupTest()
	tx := suite.GetTransaction()

	suite.ctrl = gomock.NewController(suite.T())
	suite.mockClock = clock.NewMockClock(suite.ctrl)

	suite.repo = &userRepository{
		dbReader: tx,
		dbWriter: tx,
		clock:    suite.mockClock,
		randOp:   rand.New(),
	}
	suite.r = suite.Require()
}

func (suite *UserRepositoryTestSuite) TearDownTest() {
	suite.Suite.TearDownTest()
}

func (suite *UserRepositoryTestSuite) SetupSuite() {
	suite.Suite.SetupSuite(os.Getenv("TEST_DB_USER_DSN"))
}

func init() {
	os.Setenv("TEST_DB_USER_DSN", "postgres://foo:bar@localhost:5432/kktv_users?sslmode=disable")
}

func (suite *UserRepositoryTestSuite) TestWithTx() {
	suite.Run("get user repo with tx", func() {
		suite.r.Implements((*UserRepository)(nil), suite.repo.WithTx(&sqlx.Tx{}))
	})
}

func (suite *UserRepositoryTestSuite) TestGetUsersByEmail() {
	suite.givenUsers()

	testcases := []struct {
		name  string
		email string
		then  func([]*dbuser.User, error)
	}{
		{
			name:  "given email with uppercase then got user",
			email: "<EMAIL>",
			then: func(users []*dbuser.User, err error) {
				suite.r.NoError(err)
				suite.r.NotZero(users)
				suite.r.Len(users, 1)
				suite.r.Equal("kktv-api-unittest-0001", users[0].ID)
				kkboxObj := users[0].MediaSource["kkbox"]
				kkboxSub := kkboxObj.(map[string]interface{})["sub"]
				suite.r.EqualValues("testSubJosiE", kkboxSub)

				family := users[0].MediaSource["family"].([]interface{})
				suite.r.ElementsMatch([]string{"a167686a-ae58-43f2-93a9-67f0e580efe1", "0420e345-df37-4c23-ad22-184d85c9fb72"}, family)
			},
		},
		{
			name:  "given email then got users with duplicate email",
			email: "<EMAIL>",
			then: func(users []*dbuser.User, err error) {
				suite.r.NoError(err)
				suite.r.NotZero(users)
				suite.r.NotNil(users[0].EmailVerifiedAt) // assert order
			},
		},
		{
			name:  "find revoked user then got empty slice",
			email: "<EMAIL>",
			then: func(users []*dbuser.User, err error) {
				suite.r.NoError(err)
				suite.r.Zero(users)
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.then(suite.repo.GetUsersByEmail(tc.email))
		})
	}
}

func (suite *UserRepositoryTestSuite) TestGetUsersByPhone() {
	suite.givenUsers()

	testcases := []struct {
		name  string
		phone string
		then  func([]*dbuser.User, error)
	}{
		{
			name:  "given phone starts with +886 then got user",
			phone: "+886955667788",
			then: func(users []*dbuser.User, err error) {
				suite.r.NoError(err)
				suite.r.NotZero(users)
				suite.r.Len(users, 1)
				suite.r.Equal("kktv-api-unittest-0006", users[0].ID)
			},
		},
		{
			name:  "given phone starts with 886 then got user with phone +886955667788 stored in db",
			phone: "886955667788",
			then: func(users []*dbuser.User, err error) {
				suite.r.NoError(err)
				suite.r.NotZero(users)
				suite.r.Len(users, 1)
				suite.r.Equal("kktv-api-unittest-0006", users[0].ID)
			},
		},
		{
			name:  "given phone starts with 0 then got user with phone +886955667788 stored in db",
			phone: "0955667788",
			then: func(users []*dbuser.User, err error) {
				suite.r.NoError(err)
				suite.r.NotZero(users)
				suite.r.Len(users, 1)
				suite.r.Equal("kktv-api-unittest-0006", users[0].ID)
			},
		},
		{
			name:  "find revoked user then got empty slice",
			phone: "0977777777",
			then: func(users []*dbuser.User, err error) {
				suite.r.NoError(err)
				suite.r.Zero(users)
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.then(suite.repo.GetUsersByPhone(tc.phone))
		})
	}
}

func (suite *UserRepositoryTestSuite) TestGetActiveByID() {
	suite.givenUsers()

	testcases := []struct {
		name   string
		userID string
		then   func(*dbuser.User, error)
	}{
		{
			name:   "found active user",
			userID: "kktv-api-unittest-0001",
			then: func(user *dbuser.User, err error) {
				suite.r.NoError(err)
				suite.r.Equal("<EMAIL>", user.Email.String)
				suite.r.Equal(dbuser.Membership{{Role: dbuser.MemberRoleExpired}}, user.Membership)
			},
		},
		{
			name:   "find revoked user THEN got nil",
			userID: "kktv-api-unittest-0002",
			then: func(user *dbuser.User, err error) {
				suite.r.NoError(err)
				suite.r.Nil(user)
			},
		},
		{
			name:   "find not exist THEN got nil",
			userID: "not-exist",
			then: func(user *dbuser.User, err error) {
				suite.r.NoError(err)
				suite.r.Nil(user)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.then(suite.repo.GetActiveByID(tc.userID))
		})
	}
}

func (suite *UserRepositoryTestSuite) TestUpdateByFields() {
	suite.givenUsers()

	testcases := []struct {
		name   string
		userID string
		fields map[dbuser.UsersField]interface{}
		then   func(bool, error)
	}{
		{
			name:   "update password, email and phone for exist user",
			userID: "kktv-api-unittest-0001",
			fields: map[dbuser.UsersField]interface{}{
				dbuser.UsersFieldPassword: "new-password",
				dbuser.UsersFieldEmail:    "<EMAIL>",
				dbuser.UsersFieldPhone:    "+886912345678",
			},
			then: func(affected bool, err error) {
				suite.r.NoError(err)
				suite.r.Equal(true, affected)
				user, err := suite.repo.GetActiveByID("kktv-api-unittest-0001")
				suite.r.NoError(err)
				suite.r.Equal("new-password", user.Password.String)
				suite.r.Equal("+886912345678", user.Phone.String)
				suite.r.Equal("<EMAIL>", user.Email.String)
			},
		},
		{
			name:   "update id",
			userID: "kktv-api-unittest-0002",
			fields: map[dbuser.UsersField]interface{}{
				dbuser.UsersField("id"): "kktv-api-unittest-1000",
			},
			then: func(affected bool, err error) {
				suite.r.NoError(err)
				suite.r.Equal(true, affected)

				user, err := suite.repo.GetByUserID("kktv-api-unittest-1000")
				suite.r.NoError(err)
				suite.r.Equal("<EMAIL>", user.Email.String)
			},
		},
		{
			name:   "update role, type and membership for exist user",
			userID: "kktv-api-unittest-0003",
			fields: map[dbuser.UsersField]interface{}{
				dbuser.UserFieldRole:       dbuser.RolePremium,
				dbuser.UserFieldType:       dbuser.TypePrime,
				dbuser.UserFieldMembership: dbuser.Membership{{Role: dbuser.MemberRolePrime}},
			},
			then: func(affected bool, err error) {
				suite.r.NoError(err)
				suite.r.Equal(true, affected)

				user, err := suite.repo.GetActiveByID("kktv-api-unittest-0003")
				suite.r.NoError(err)
				suite.r.EqualValues(dbuser.RolePremium, user.Role)
				suite.r.EqualValues(dbuser.TypePrime, user.Type)
				suite.r.Equal(dbuser.Membership{{Role: dbuser.MemberRolePrime}}, user.Membership)
			},
		},
		{
			name:   "update password for not exist user THEN no error",
			userID: "not-exist",
			fields: map[dbuser.UsersField]interface{}{
				dbuser.UsersFieldPassword: "new-password",
			},
			then: func(affected bool, err error) {
				suite.r.NoError(err)
				suite.r.Equal(false, affected)
			},
		},
		{
			name:   "update non-exist column THEN got error",
			userID: "kktv-api-unittest-0001",
			fields: map[dbuser.UsersField]interface{}{
				dbuser.UsersField("kktv"): "kktv is the best",
			},
			then: func(affected bool, err error) {
				suite.r.Error(err)
				suite.r.Equal(false, affected)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.then(suite.repo.UpdateByFields(tc.userID, tc.fields))
		})
	}
}

func (suite *UserRepositoryTestSuite) TestCreate() {
	now := time.Date(2020, 4, 29, 0, 0, 0, 0, datetimer.LocationTaipei)

	suite.givenUsers()
	suite.mockClock.EXPECT().Now().Return(now).AnyTimes()

	anUserWithRequiredFields := func() *dbuser.User {
		return &dbuser.User{
			Password:       null.StringFrom("new-password"),
			Email:          null.StringFrom("<EMAIL>"),
			Phone:          null.StringFrom("+886912345678"),
			Role:           dbuser.RoleFreeTrial.String(),
			Type:           dbuser.TypeGeneral.String(),
			CreatedBy:      null.StringFrom(dbuser.UserCreatedByWeb.String()),
			OriginProvider: null.StringFrom(dbuser.UserOriginProviderKKTV.String()),
			Membership:     dbuser.Membership{{Role: dbuser.MemberRoleFreeTrial}},
		}
	}

	testcases := []struct {
		name    string
		newUser *dbuser.User
		then    func(created *dbuser.User, err error)
	}{
		{
			name:    "create success with all required fields",
			newUser: anUserWithRequiredFields(),
			then: func(created *dbuser.User, err error) {
				suite.r.NoError(err)

				user, err := suite.repo.GetActiveByID(created.ID)
				suite.r.NoError(err)
				suite.r.Equal("new-password", user.Password.String)
				suite.r.Equal("+886912345678", user.Phone.String)
				suite.r.Equal("<EMAIL>", user.Email.String)
				suite.r.Equal(dbuser.UserMediaSource{}, user.MediaSource)
				suite.r.EqualValues(dbuser.TypeGeneral, user.Type)
				suite.r.EqualValues(dbuser.RoleFreeTrial, user.Role)
				suite.r.Equal(dbuser.Membership{{Role: dbuser.MemberRoleFreeTrial}}, user.Membership)
			},
		},
		{
			name: "create fail when required fields are missing",
			newUser: &dbuser.User{
				Password: null.StringFrom("new-password"),
				Type:     dbuser.TypeGeneral.String(),
			},
			then: func(created *dbuser.User, err error) {
				suite.r.Error(err)
				suite.Nil(created)
			},
		},
		{
			name: "create fail when user has ID",
			newUser: func() *dbuser.User {
				user := anUserWithRequiredFields()
				user.ID = "test-id"
				return user
			}(),
			then: func(created *dbuser.User, err error) {
				suite.r.Error(err)
				suite.Nil(created)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			created, err := suite.repo.Create(tc.newUser)
			tc.then(created, err)
		})
	}
}

//go:embed testdata/user_repository/UserRepo.sql
var dataForUserRepo string

func (suite *UserRepositoryTestSuite) givenUsers() {
	if _, err := suite.GetTransaction().Exec(dataForUserRepo); err != nil {
		suite.Require().Fail("fail to insert test data", err)
	}
}
