//go:generate mockgen -source payment_info_repository.go -destination payment_info_repository_mock.go -package user
package user

import (
	"database/sql"
	"errors"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

var (
	paymentInfoRepo     PaymentInfoRepository
	oncePaymentInfoRepo sync.Once
)

type PaymentInfoRepository interface {
	GetPaymentInfoByUserID(uid string) (*dbuser.PaymentInfo, error)
	GetByUserID(userID string) (*dbuser.PaymentInfo, error)
	GetByMODSubscriberID(modSubscriberID string) (*dbuser.PaymentInfo, error)
	Upsert(paymentInfo *dbuser.PaymentInfo) (err error)
	WithTx(tx database.Tx) PaymentInfoRepository
}

type paymentInfoRepository struct {
	dbReader database.DB
	dbWriter database.DB
}

func NewPaymentInfoRepository() PaymentInfoRepository {
	oncePaymentInfoRepo.Do(func() {
		paymentInfoRepo = &paymentInfoRepository{
			dbReader: container.DBPoolUser().Slave().Unsafe(),
			dbWriter: container.DBPoolUser().Master().Unsafe(),
		}
	})
	return paymentInfoRepo
}

func NewPaymentInfoRepositoryWith(db database.DB) PaymentInfoRepository {
	return &paymentInfoRepository{
		dbWriter: db,
		dbReader: db,
	}
}

func (r *paymentInfoRepository) WithTx(tx database.Tx) PaymentInfoRepository {
	return &paymentInfoRepository{
		dbReader: tx,
		dbWriter: tx,
	}
}

func (r *paymentInfoRepository) GetPaymentInfoByUserID(uid string) (*dbuser.PaymentInfo, error) {
	payment := new(dbuser.PaymentInfo)
	if err := r.dbReader.Get(payment,
		`SELECT user_id, email, phone, payment_type,
			credit_card_6no, credit_card_4no, credit_card_token_value,
			credit_card_token_term, iap_receipt_data, iap_receipt_data_hash,
    	iap_latest_expires_date, iap_latest_transaction_id, caring_code,
			recipient, recipient_address, carrier_type, carrier_value,
			telecom_mp_id, tstar_order_id, tstar_contract_id, mod_subscriber_id,
			mod_subscriber_area, iab_receipt_data, iab_order_id,
			iab_latest_order_id, iab_latest_expires_date,
			created_at, updated_at, family_id
		FROM payment_info
		WHERE user_id = $1`,
		uid); err != nil {
		return nil, err
	}
	return payment, nil
}

func (r *paymentInfoRepository) GetByUserID(userID string) (*dbuser.PaymentInfo, error) {
	paymentInfo := new(dbuser.PaymentInfo)
	if err := r.dbReader.Get(paymentInfo, `SELECT * FROM payment_info WHERE user_id = $1`, userID); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return paymentInfo, nil
}

func (r *paymentInfoRepository) GetByMODSubscriberID(modSubscriberID string) (*dbuser.PaymentInfo, error) {
	paymentInfo := new(dbuser.PaymentInfo)
	if err := r.dbReader.Get(paymentInfo, `SELECT * FROM payment_info WHERE mod_subscriber_id = $1`, modSubscriberID); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return paymentInfo, nil
}

func (r *paymentInfoRepository) Upsert(paymentInfo *dbuser.PaymentInfo) error {
	_, err := r.dbWriter.NamedExec(
		`INSERT INTO payment_info (
 			user_id, email, phone, credit_card_6no, credit_card_4no,
			credit_card_token_value, credit_card_token_term, iap_receipt_data,
 			iap_receipt_data_hash, iap_latest_transaction_id, iap_latest_expires_date,
			payment_type, caring_code, recipient, recipient_address, carrier_type,
			carrier_value, telecom_mp_id, mod_subscriber_id, mod_subscriber_area,
			iab_receipt_data, iab_order_id, iab_latest_order_id, iab_latest_expires_date
		) VALUES (
			:user_id, :email, :phone, :credit_card_6no, :credit_card_4no,
			:credit_card_token_value, :credit_card_token_term, :iap_receipt_data,
			:iap_receipt_data_hash, :iap_latest_transaction_id, :iap_latest_expires_date,
			:payment_type, :caring_code, :recipient, :recipient_address, :carrier_type,
			:carrier_value, :telecom_mp_id, :mod_subscriber_id, :mod_subscriber_area,
			:iab_receipt_data, :iab_order_id, :iab_latest_order_id, :iab_latest_expires_date
		) ON CONFLICT (user_id)
		DO UPDATE SET
 			email = :email,
 			phone = :phone,
 			credit_card_6no = :credit_card_6no,
 			credit_card_4no = :credit_card_4no,
 			credit_card_token_value = :credit_card_token_value,
 			credit_card_token_term = :credit_card_token_term ,
 			iap_receipt_data = :iap_receipt_data,
 			iap_receipt_data_hash = :iap_receipt_data_hash ,
 			iap_latest_transaction_id = :iap_latest_transaction_id ,
 			iap_latest_expires_date = :iap_latest_expires_date ,
 			payment_type = :payment_type ,
 			caring_code = :caring_code ,
 			recipient = :recipient ,
 			recipient_address = :recipient_address ,
 			carrier_type = :carrier_type ,
 			carrier_value = :carrier_value ,
 			telecom_mp_id = :telecom_mp_id ,
 			mod_subscriber_id = :mod_subscriber_id ,
 			mod_subscriber_area = :mod_subscriber_area ,
 			iab_receipt_data = :iab_receipt_data ,
 			iab_order_id = :iab_order_id ,
 			iab_latest_order_id = :iab_latest_order_id ,
 			iab_latest_expires_date = :iab_latest_expires_date,
 			updated_at = NOW()
 		WHERE payment_info.user_id = :user_id;`,
		paymentInfo)
	return err
}
