// Code generated by MockGen. DO NOT EDIT.
// Source: unsubscribe_repository.go

// Package user is a generated GoMock package.
package user

import (
	reflect "reflect"

	database "github.com/KKTV/kktv-api-v3/pkg/database"
	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockUnsubscribeRepository is a mock of UnsubscribeRepository interface.
type MockUnsubscribeRepository struct {
	ctrl     *gomock.Controller
	recorder *MockUnsubscribeRepositoryMockRecorder
}

// MockUnsubscribeRepositoryMockRecorder is the mock recorder for MockUnsubscribeRepository.
type MockUnsubscribeRepositoryMockRecorder struct {
	mock *MockUnsubscribeRepository
}

// NewMockUnsubscribeRepository creates a new mock instance.
func NewMockUnsubscribeRepository(ctrl *gomock.Controller) *MockUnsubscribeRepository {
	mock := &MockUnsubscribeRepository{ctrl: ctrl}
	mock.recorder = &MockUnsubscribeRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsubscribeRepository) EXPECT() *MockUnsubscribeRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockUnsubscribeRepository) Create(unsubscribe *dbuser.Unsubscribe) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", unsubscribe)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockUnsubscribeRepositoryMockRecorder) Create(unsubscribe interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockUnsubscribeRepository)(nil).Create), unsubscribe)
}

// WithTx mocks base method.
func (m *MockUnsubscribeRepository) WithTx(tx database.Tx) UnsubscribeRepository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(UnsubscribeRepository)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockUnsubscribeRepositoryMockRecorder) WithTx(tx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockUnsubscribeRepository)(nil).WithTx), tx)
}
