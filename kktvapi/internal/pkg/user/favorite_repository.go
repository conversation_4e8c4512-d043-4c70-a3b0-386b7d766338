//go:generate mockgen -source favorite_repository.go -destination favorite_repository_mock.go -package user
package user

import (
	"errors"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
)

type FavoriteRepository interface {
	// GetTitleIDsByUserID returns the list of titles that the user has marked as favorite
	GetTitleIDsByUserID(userID string) ([]string, error)
}

var (
	favoriteRepo     FavoriteRepository
	onceFavoriteRepo sync.Once
)

type favoriteRepository struct {
	cacheReader cache.Cacher
}

func NewFavoriteRepository() FavoriteRepository {
	onceFavoriteRepo.Do(func() {
		favoriteRepo = &favoriteRepository{
			cacheReader: cache.New(container.CachePoolUser().Slave()),
		}
	})
	return favoriteRepo
}

func (r *favoriteRepository) GetTitleIDsByUserID(userID string) ([]string, error) {
	data, err := r.cacheReader.ZRevRange(key.UserFavoriteTitles(userID), 0, -1)
	if errors.Is(err, cache.ErrCacheMiss) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	var titleIDs []string
	for _, titleID := range data {
		titleIDs = append(titleIDs, string(titleID))
	}
	return titleIDs, nil
}
