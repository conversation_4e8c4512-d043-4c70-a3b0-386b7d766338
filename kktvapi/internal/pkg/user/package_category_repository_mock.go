// Code generated by MockGen. DO NOT EDIT.
// Source: kktvapi/internal/pkg/user/package_category_repository.go

// Package user is a generated GoMock package.
package user

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockPackageCategoryRepository is a mock of PackageCategoryRepository interface.
type MockPackageCategoryRepository struct {
	ctrl     *gomock.Controller
	recorder *MockPackageCategoryRepositoryMockRecorder
}

// MockPackageCategoryRepositoryMockRecorder is the mock recorder for MockPackageCategoryRepository.
type MockPackageCategoryRepositoryMockRecorder struct {
	mock *MockPackageCategoryRepository
}

// NewMockPackageCategoryRepository creates a new mock instance.
func NewMockPackageCategoryRepository(ctrl *gomock.Controller) *MockPackageCategoryRepository {
	mock := &MockPackageCategoryRepository{ctrl: ctrl}
	mock.recorder = &MockPackageCategoryRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPackageCategoryRepository) EXPECT() *MockPackageCategoryRepositoryMockRecorder {
	return m.recorder
}

// GetByCategories mocks base method.
func (m *MockPackageCategoryRepository) GetByCategories(categories []string) (*dbuser.PackageCategories, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCategories", categories)
	ret0, _ := ret[0].(*dbuser.PackageCategories)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCategories indicates an expected call of GetByCategories.
func (mr *MockPackageCategoryRepositoryMockRecorder) GetByCategories(categories interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCategories", reflect.TypeOf((*MockPackageCategoryRepository)(nil).GetByCategories), categories)
}
