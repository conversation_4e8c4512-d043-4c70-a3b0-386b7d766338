// Code generated by MockGen. DO NOT EDIT.
// Source: favorite_repository.go

// Package user is a generated GoMock package.
package user

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockFavoriteRepository is a mock of FavoriteRepository interface.
type MockFavoriteRepository struct {
	ctrl     *gomock.Controller
	recorder *MockFavoriteRepositoryMockRecorder
}

// MockFavoriteRepositoryMockRecorder is the mock recorder for MockFavoriteRepository.
type MockFavoriteRepositoryMockRecorder struct {
	mock *MockFavoriteRepository
}

// NewMockFavoriteRepository creates a new mock instance.
func NewMockFavoriteRepository(ctrl *gomock.Controller) *MockFavoriteRepository {
	mock := &MockFavoriteRepository{ctrl: ctrl}
	mock.recorder = &MockFavoriteRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFavoriteRepository) EXPECT() *MockFavoriteRepositoryMockRecorder {
	return m.recorder
}

// GetTitleIDsByUserID mocks base method.
func (m *MockFavoriteRepository) GetTitleIDsByUserID(userID string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTitleIDsByUserID", userID)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTitleIDsByUserID indicates an expected call of GetTitleIDsByUserID.
func (mr *MockFavoriteRepositoryMockRecorder) GetTitleIDsByUserID(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTitleIDsByUserID", reflect.TypeOf((*MockFavoriteRepository)(nil).GetTitleIDsByUserID), userID)
}
