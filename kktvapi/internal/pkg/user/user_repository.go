//go:generate mockgen -source user_repository.go -destination user_repository_mock.go -package user
package user

import (
	"database/sql"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/rand"
	"github.com/lib/pq"
	"gopkg.in/guregu/null.v3"
)

var (
	userRepo     UserRepository
	onceUserRepo sync.Once
)

type UserRepository interface {
	WithTx(tx database.Tx) UserRepository
	GetByUserID(uid string) (*dbuser.User, error)
	GetByID(uid string) (*dbuser.User, error)
	GetUsersByEmail(email string) ([]*dbuser.User, error)
	GetUsersByPhone(phone string) ([]*dbuser.User, error)
	Update(user *dbuser.User) (err error)
	UpdateByFields(userID string, fields map[dbuser.UsersField]interface{}) (bool, error)
	GetActiveByID(userID string) (*dbuser.User, error)
	Create(user *dbuser.User) (*dbuser.User, error)
	GetByModSubscriberID(modID string) (*dbuser.User, error)
	ListFamilyMembersByHostUserID(userID string) ([]*dbuser.User, error)
	BatchUpdateByFields(userIDs []string, fields map[dbuser.UsersField]interface{}) (int64, error)
}

type userRepository struct {
	dbReader database.DB
	dbWriter database.DB
	clock    clock.Clock
	randOp   rand.Rand
}

func NewUserRepository() UserRepository {
	onceUserRepo.Do(func() {
		userRepo = &userRepository{
			dbReader: container.DBPoolUser().Slave().Unsafe(),
			dbWriter: container.DBPoolUser().Master().Unsafe(),
			clock:    clock.New(),
			randOp:   rand.New(),
		}
	})
	return userRepo
}

func NewUserRepositoryWith(db database.DB) UserRepository {
	return &userRepository{
		dbWriter: db,
		dbReader: db,
		clock:    clock.New(),
		randOp:   rand.New(),
	}
}

func (r *userRepository) WithTx(tx database.Tx) UserRepository {
	return &userRepository{
		dbWriter: tx,
		dbReader: tx,
		clock:    r.clock,
		randOp:   r.randOp,
	}
}

// Deprecated: use GetByID instead
func (r *userRepository) GetByUserID(uid string) (*dbuser.User, error) {
	user := new(dbuser.User)
	if err := r.dbReader.Get(user,
		`SELECT
			u.id, u.email, u.phone, u.gender, u.birthday, u.avatar_url, u.name, u.media_source,
			u.expired_at,
 			u.created_at, u.role, u.auto_renew, u.type, u.created_by, u.kkid
		FROM users u
		WHERE u.id = $1`,
		uid); err != nil {
		return nil, err
	}
	return user, nil
}

// GetUsersByEmail returns users with duplicate email
func (r *userRepository) GetUsersByEmail(email string) (users []*dbuser.User, err error) {
	if err = r.dbReader.Select(&users,
		`SELECT u.*
		FROM users u
		WHERE u.email = $1
		  AND u.revoked_at IS NULL ORDER BY u.email_verified_at`,
		strings.ToLower(email)); err != nil {
		return
	}

	return
}

// GetUsersByPhone returns users with duplicate phone
func (r *userRepository) GetUsersByPhone(phone string) (users []*dbuser.User, err error) {
	if err = r.dbReader.Select(&users,
		`SELECT u.*
		FROM users u
		WHERE u.phone = ANY ($1)
		  AND u.revoked_at IS NULL ORDER BY u.phone_verified_at`,
		pq.Array(getTaiwanPhoneNumberFormats(phone))); err != nil {
		return
	}

	return
}

func (r *userRepository) Update(user *dbuser.User) error {
	_, err := r.dbWriter.NamedExec(
		`UPDATE users
		SET role = 'premium',
			expired_at = :expired_at,
			auto_renew = :auto_renew,
			updated_at = NOW()
		WHERE id = :id`,
		user)
	return err
}

func (r *userRepository) UpdateByFields(userID string, fields map[dbuser.UsersField]interface{}) (bool, error) {
	if len(fields) == 0 {
		return false, nil
	}
	namedArgs := map[string]interface{}{
		"user_id": userID, // in case the fields include `id`
	}
	q := `UPDATE users SET updated_at = NOW()`
	for k, v := range fields {
		q += fmt.Sprintf(`, %s = :%s`, k, k)
		namedArgs[k.String()] = v
	}
	q += ` WHERE id = :user_id`

	result, err := r.dbWriter.NamedExec(q, namedArgs)
	if err != nil {
		return false, err
	}
	affected, _ := result.RowsAffected()
	return affected > 0, nil
}

func (r *userRepository) BatchUpdateByFields(userIDs []string, fields map[dbuser.UsersField]interface{}) (int64, error) {
	if len(fields) == 0 {
		return 0, nil
	}
	namedArgs := map[string]interface{}{}
	q := `UPDATE users SET updated_at = NOW()`
	for k, v := range fields {
		q += fmt.Sprintf(`, %s = :%s`, k, k)
		namedArgs[k.String()] = v
	}
	q += ` WHERE id = ANY(:user_ids)`
	namedArgs["user_ids"] = pq.Array(userIDs)

	result, err := r.dbWriter.NamedExec(q, namedArgs)
	if err != nil {
		return 0, err
	}
	affected, _ := result.RowsAffected()
	return affected, nil
}

func (r *userRepository) GetActiveByID(userID string) (*dbuser.User, error) {
	user := new(dbuser.User)
	if err := r.dbReader.Get(user,
		`SELECT u.*
		FROM users u
		WHERE u.id = $1 AND u.revoked_at IS NULL`,
		userID); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return user, nil
}

func (r *userRepository) GetByID(userID string) (*dbuser.User, error) {
	user := new(dbuser.User)
	if err := r.dbReader.Get(user, `SELECT u.* FROM users u WHERE u.id = $1`,
		userID); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return user, nil
}

func (r *userRepository) Create(user *dbuser.User) (*dbuser.User, error) {
	if user.ID != "" {
		return nil, fmt.Errorf("%w: user id must be empty", kktverror.ErrInvalidParameter)
	}
	user.ID = r.randOp.UUID()
	now := r.clock.Now()
	user.CreatedAt = null.TimeFrom(now)
	user.UpdatedAt = null.TimeFrom(now)

	dbFields := database.GetDBFields(user)

	insertSQL := `INSERT INTO users (` + dbFields.String() + `) VALUES (` + dbFields.NamedString() + `);`

	log.Debug("user repository: create").Str("sql", insertSQL).Send()
	_, err := r.dbWriter.NamedExec(insertSQL, user)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (r *userRepository) GetByModSubscriberID(modID string) (*dbuser.User, error) {
	user := new(dbuser.User)
	if err := r.dbReader.Get(user,
		`SELECT u.*
		FROM users u
		JOIN payment_info pi ON u.id = pi.user_id
		WHERE pi.mod_subscriber_id = $1`,
		modID); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return user, nil
}

func (r *userRepository) ListFamilyMembersByHostUserID(userID string) ([]*dbuser.User, error) {
	users := make([]*dbuser.User, 0)
	if err := r.dbReader.Select(&users,
		`SELECT u.* FROM payment_info p JOIN users u ON u.id = p.user_id WHERE p.family_id = $1 AND u.revoked_at IS NULL`, userID); err != nil {
		return nil, err
	}
	return users, nil
}

// getTaiwanPhoneNumberFormats returns phone numbers start with +886, 886, 0 if the argument is a taiwan's phone number
func getTaiwanPhoneNumberFormats(phone string) (phones []string) {
	const twPhoneNumberLen = 9
	phones = []string{phone}
	number := regexp.MustCompile(`^0|^\+886|^886`).ReplaceAllString(phone, "")
	prefixes := []string{"+886", "886", "0"}

	if len(number) != twPhoneNumberLen {
		return
	}
	for _, prefix := range prefixes {
		phones = append(phones, prefix+number)
	}

	return
}
