// Code generated by MockGen. DO NOT EDIT.
// Source: user_playlist_repository.go

// Package user is a generated GoMock package.
package user

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockUserPlaylistRepository is a mock of UserPlaylistRepository interface.
type MockUserPlaylistRepository struct {
	ctrl     *gomock.Controller
	recorder *MockUserPlaylistRepositoryMockRecorder
}

// MockUserPlaylistRepositoryMockRecorder is the mock recorder for MockUserPlaylistRepository.
type MockUserPlaylistRepositoryMockRecorder struct {
	mock *MockUserPlaylistRepository
}

// NewMockUserPlaylistRepository creates a new mock instance.
func NewMockUserPlaylistRepository(ctrl *gomock.Controller) *MockUserPlaylistRepository {
	mock := &MockUserPlaylistRepository{ctrl: ctrl}
	mock.recorder = &MockUserPlaylistRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserPlaylistRepository) EXPECT() *MockUserPlaylistRepositoryMockRecorder {
	return m.recorder
}

// CreatePlaylist mocks base method.
func (m *MockUserPlaylistRepository) CreatePlaylist(userID, name string) (*dbuser.UserPlaylist, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePlaylist", userID, name)
	ret0, _ := ret[0].(*dbuser.UserPlaylist)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePlaylist indicates an expected call of CreatePlaylist.
func (mr *MockUserPlaylistRepositoryMockRecorder) CreatePlaylist(userID, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePlaylist", reflect.TypeOf((*MockUserPlaylistRepository)(nil).CreatePlaylist), userID, name)
}

// GetFavoritePlaylist mocks base method.
func (m *MockUserPlaylistRepository) GetFavoritePlaylist(userID string) (*dbuser.UserPlaylist, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFavoritePlaylist", userID)
	ret0, _ := ret[0].(*dbuser.UserPlaylist)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFavoritePlaylist indicates an expected call of GetFavoritePlaylist.
func (mr *MockUserPlaylistRepositoryMockRecorder) GetFavoritePlaylist(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFavoritePlaylist", reflect.TypeOf((*MockUserPlaylistRepository)(nil).GetFavoritePlaylist), userID)
}

// GetPlaylistByUserIDAndName mocks base method.
func (m *MockUserPlaylistRepository) GetPlaylistByUserIDAndName(userID, name string) (*dbuser.UserPlaylist, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlaylistByUserIDAndName", userID, name)
	ret0, _ := ret[0].(*dbuser.UserPlaylist)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlaylistByUserIDAndName indicates an expected call of GetPlaylistByUserIDAndName.
func (mr *MockUserPlaylistRepositoryMockRecorder) GetPlaylistByUserIDAndName(userID, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlaylistByUserIDAndName", reflect.TypeOf((*MockUserPlaylistRepository)(nil).GetPlaylistByUserIDAndName), userID, name)
}
