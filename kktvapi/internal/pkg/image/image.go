package image

import "fmt"

var (
	imageHost   = "https://test-images.kktv.com.tw"
	imageBucket = "kktv-test-images"
)

func Init(env string) {
	switch env {
	case "test", "dev", "stage":
		imageHost = "https://test-images.kktv.com.tw"
		imageBucket = "kktv-test-images"
	default:
		imageHost = "https://images.kktv.com.tw"
		imageBucket = "kktv-prod-images"
	}
}

func ColdStartBanner() string {
	return fmt.Sprintf("%s/%s", imageHost, "coldstart/Recomandation_Banner.png")
}

func SignupSurveyBanner() string {
	return fmt.Sprintf("%s/%s", imageHost, "survey/Signup_Survey_Banner.png")
}

func PrimeBanner() string {
	return fmt.Sprintf("%s/%s", imageHost, "prime/Prime_Banner.png")
}

func GetBucket() string {
	return imageBucket
}

func GetImageHost() string {
	return imageHost
}
