package request

import (
	"net/http"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/platform"
	"github.com/go-zoo/bone"
)

// GetDeviceType return device type from request path. if not found, return platform.DeviceTypeUnknown
func GetDeviceType(r *http.Request) platform.DeviceType {
	dt := bone.GetValue(r, "deviceType")
	deviceType := platform.DeviceTypeWeb
	if dt == "a" {
		deviceType = platform.DeviceTypeMobileApp
	}
	return deviceType
}
