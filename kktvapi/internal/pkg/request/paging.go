package request

import (
	"net/http"

	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
)

type Paging struct {
	Page     int `schema:"page"`
	PageSize int `schema:"page_size"`
}

func (p *Paging) GetRange(total int) (from, to int) {
	to = p.PageSize * p.Page
	from = to - p.PageSize
	if from < 0 {
		from = 0
	} else if from > total {
		from = total
	}
	if to > total {
		to = total
	}
	return
}

func (p *Paging) ScanFromRequest(r *http.Request, maxPageSize int) error {
	if err := httpreq.Scan(p, r.URL.Query()); err != nil {
		return err
	}
	if p.PageSize > maxPageSize || p.PageSize <= 0 {
		p.PageSize = maxPageSize
	}
	if p.Page <= 0 {
		p.Page = 1
	}
	return nil
}
