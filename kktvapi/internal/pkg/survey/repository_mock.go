// Code generated by MockGen. DO NOT EDIT.
// Source: repository.go

// Package survey is a generated GoMock package.
package survey

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// GetByIdentifier mocks base method.
func (m *MockRepository) GetByIdentifier(identifier dbuser.SurveyIdentifier) (*dbuser.Survey, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIdentifier", identifier)
	ret0, _ := ret[0].(*dbuser.Survey)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIdentifier indicates an expected call of GetByIdentifier.
func (mr *MockRepositoryMockRecorder) GetByIdentifier(identifier interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIdentifier", reflect.TypeOf((*MockRepository)(nil).GetByIdentifier), identifier)
}
