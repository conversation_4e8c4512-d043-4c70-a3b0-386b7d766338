//go:generate mockgen -source repository.go -destination repository_mock.go -package survey
package survey

import (
	"database/sql"
	"errors"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"sync"
)

var (
	surveyRepo     Repository
	onceSurveyRepo sync.Once
)

type Repository interface {
	GetByIdentifier(identifier dbuser.SurveyIdentifier) (*dbuser.Survey, error)
}

type repository struct {
	dbReader database.DB
}

func NewRepository() Repository {
	onceSurveyRepo.Do(func() {
		surveyRepo = &repository{
			dbReader: container.DBPoolUser().Slave().Unsafe(),
		}
	})
	return surveyRepo
}

func (r *repository) GetByIdentifier(identifier dbuser.SurveyIdentifier) (*dbuser.Survey, error) {
	survey := new(dbuser.Survey)
	if err := r.dbReader.Get(survey,
		`SELECT * FROM surveys WHERE identifier = $1`,
		identifier,
	); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return survey, nil
}
