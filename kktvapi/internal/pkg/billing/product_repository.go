package billing

import (
	"encoding/json"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/billing"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/duke-git/lancet/v2/slice"
)

type ProductRepository interface {
	UpdateProductList(products []billing.Product) error
}

var (
	productRepo     ProductRepository
	onceProductRepo sync.Once
)

type productRepository struct {
	cacheReader cache.Cacher
	cacheWriter cache.Cacher
}

func NewProductRepository() ProductRepository {
	onceProductRepo.Do(func() {
		pool := container.CachePoolUser()
		productRepo = &productRepository{
			cacheReader: cache.New(pool.Slave()),
			cacheWriter: cache.New(pool.Master()),
		}
	})
	return productRepo
}

func (r *productRepository) UpdateProductList(products []billing.Product) error {
	var err error
	billingProductsKey := key.BillingProducts()
	hashCmd := []interface{}{}

	productIds := []string{}
	for _, product := range products {
		productIds = append(productIds, product.Identifier)
		p, _ := json.Marshal(product)
		hashCmd = append(hashCmd, product.Identifier, string(p))
	}
	err = r.cacheWriter.HmSet(billingProductsKey, hashCmd)
	if err != nil {
		return err
	}

	m, err := r.cacheReader.HGetAll(billingProductsKey)
	if err != nil {
		return err
	}

	for productId, _ := range m {
		if !slice.Contain(productIds, productId) {
			r.cacheWriter.HDel(billingProductsKey, productId)
		}
	}

	return err
}
