// Code generated by MockGen. DO NOT EDIT.
// Source: repository.go

// Package event is a generated GoMock package.
package event

import (
	reflect "reflect"
	time "time"

	model "github.com/KKTV/kktv-api-v3/kkapp/model"
	gomock "github.com/golang/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// ListAvailable mocks base method.
func (m *MockRepository) ListAvailable(timing time.Time) ([]model.EventItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAvailable", timing)
	ret0, _ := ret[0].([]model.EventItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAvailable indicates an expected call of ListAvailable.
func (mr *MockRepositoryMockRecorder) ListAvailable(timing interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAvailable", reflect.TypeOf((*MockRepository)(nil).ListAvailable), timing)
}
