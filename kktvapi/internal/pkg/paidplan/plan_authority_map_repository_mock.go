// Code generated by MockGen. DO NOT EDIT.
// Source: plan_authority_map_repository.go

// Package paidplan is a generated GoMock package.
package paidplan

import (
	reflect "reflect"

	authority "github.com/KKTV/kktv-api-v3/pkg/model/authority"
	gomock "github.com/golang/mock/gomock"
)

// MockPlanAuthorityMapRepository is a mock of PlanAuthorityMapRepository interface.
type MockPlanAuthorityMapRepository struct {
	ctrl     *gomock.Controller
	recorder *MockPlanAuthorityMapRepositoryMockRecorder
}

// MockPlanAuthorityMapRepositoryMockRecorder is the mock recorder for MockPlanAuthorityMapRepository.
type MockPlanAuthorityMapRepositoryMockRecorder struct {
	mock *MockPlanAuthorityMapRepository
}

// NewMockPlanAuthorityMapRepository creates a new mock instance.
func NewMockPlanAuthorityMapRepository(ctrl *gomock.Controller) *MockPlanAuthorityMapRepository {
	mock := &MockPlanAuthorityMapRepository{ctrl: ctrl}
	mock.recorder = &MockPlanAuthorityMapRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPlanAuthorityMapRepository) EXPECT() *MockPlanAuthorityMapRepositoryMockRecorder {
	return m.recorder
}

// ListAuthoritiesByGenre mocks base method.
func (m *MockPlanAuthorityMapRepository) ListAuthoritiesByGenre(genre string) ([]authority.Authority, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAuthoritiesByGenre", genre)
	ret0, _ := ret[0].([]authority.Authority)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAuthoritiesByGenre indicates an expected call of ListAuthoritiesByGenre.
func (mr *MockPlanAuthorityMapRepositoryMockRecorder) ListAuthoritiesByGenre(genre interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAuthoritiesByGenre", reflect.TypeOf((*MockPlanAuthorityMapRepository)(nil).ListAuthoritiesByGenre), genre)
}
