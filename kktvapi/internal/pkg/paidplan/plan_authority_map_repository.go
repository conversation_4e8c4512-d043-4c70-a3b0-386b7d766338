//go:generate mockgen -source plan_authority_map_repository.go -destination plan_authority_map_repository_mock.go -package paidplan
package paidplan

import (
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/collection"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/authority"
)

type PlanAuthorityMapRepository interface {
	ListAuthoritiesByGenre(genre string) ([]authority.Authority, error)
}

type planAuthorityMapRepo struct {
	userDBReader database.DB // FIXME: userDBReader is not used until it really read something from the database
}

func NewPlanAuthMapRepositoryWith(userDBReader database.DB) PlanAuthorityMapRepository {
	return &planAuthorityMapRepo{
		userDBReader: userDBReader,
	}
}

var (
	mapping = map[string]authority.Authority{
		collection.GenreNameAnime: authority.PlanAnimePlay,
	}
)

func (p *planAuthorityMapRepo) ListAuthoritiesByGenre(genre string) ([]authority.Authority, error) {
	result := make([]authority.Authority, 0)
	if a, ok := mapping[genre]; ok {
		result = append(result, a)
	}
	return result, nil
}
