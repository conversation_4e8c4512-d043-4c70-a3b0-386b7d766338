//go:generate mockgen -source repository.go -destination repository_mock.go -package appauth
package appauth

import (
	"database/sql"
	"errors"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

var (
	repo     Repository
	onceRepo sync.Once
)

const (
	ttlAuthedApp = 24 * time.Hour
)

type Repository interface {
	GetActiveByAppID(appID string) (*dbuser.AuthedApp, error)
}

func NewRepository() Repository {
	onceRepo.Do(func() {
		repo = &repository{
			userDBReader:    container.DBPoolUser().Slave().Unsafe(),
			userCacheReader: cache.New(container.CachePoolUser().Slave()),
			userCacheWriter: cache.New(container.CachePoolUser().Master()),
		}
	})
	return repo
}

type repository struct {
	userDBReader    database.DB
	userCacheReader cache.Cacher
	userCacheWriter cache.Cacher
}

func (r *repository) GetActiveByAppID(appID string) (*dbuser.AuthedApp, error) {
	app := new(dbuser.AuthedApp)
	if err := r.userCacheReader.Get(key.UserDataAuthedApp(appID), app); err == nil {
		return app, nil
	} else if !errors.Is(err, cache.ErrCacheMiss) && err != nil {
		log.Warn("appauth repo: fail to get cache").Str("app_id", appID).Err(err).Send()
	}

	if err := r.userDBReader.Get(app,
		`SELECT * FROM authed_apps WHERE app_id = $1 AND status = $2`,
		appID, dbuser.AuthedAppStatusActive,
	); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	if err := r.userCacheWriter.Set(key.UserDataAuthedApp(appID), app, ttlAuthedApp); err != nil {
		log.Warn("appauth repo: fail to set cache").Str("app_id", appID).Err(err).Send()
	}

	return app, nil
}
