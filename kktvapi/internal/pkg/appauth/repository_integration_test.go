package appauth

import (
	_ "embed"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/dbtest"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"os"
	"testing"
	"time"
)

type RepositoryTestSuite struct {
	dbtest.Suite
	ctrl          *gomock.Controller
	repo          Repository
	mockUserCache *cache.MockCacher
}

func TestRepositoryIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(RepositoryTestSuite))
}

func (suite *RepositoryTestSuite) SetupSuite() {
	suite.Suite.SetupSuite(os.Getenv("TEST_DB_USER_DSN"))
}

func (suite *RepositoryTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockUserCache = cache.NewMockCacher(suite.ctrl)
	suite.Suite.SetupTest()

	tx := suite.GetTransaction()
	suite.repo = &repository{
		userDBReader:    tx,
		userCacheReader: suite.mockUserCache,
		userCacheWriter: suite.mockUserCache,
	}
}

func (suite *RepositoryTestSuite) TearDownTest() {
	suite.Suite.TearDownTest()
}

func init() {
	_ = os.Setenv("TEST_DB_USER_DSN", "postgres://foo:bar@localhost:5432/kktv_users?sslmode=disable")
}

var (
	existRecord1 = &dbuser.AuthedApp{
		AppID:        "test_app_id_001",
		AppSecret:    "test_app_secret_001",
		RedirectURIs: []string{"http://localhost:3000", "http://localhost:4000"},
		Status:       dbuser.AuthedAppStatusActive,
	}
)

func (suite *RepositoryTestSuite) TestGetActiveByAppID() {
	suite.givenAuthedApps()

	cacheKey := key.UserDataAuthedApp(existRecord1.AppID)
	authedAppType := gomock.AssignableToTypeOf(&dbuser.AuthedApp{})
	testcases := []struct {
		name   string
		appID  string
		given  func()
		assert func(app *dbuser.AuthedApp, err error)
	}{
		{
			name:  "should found authed app from cache",
			appID: existRecord1.AppID,
			given: func() {
				suite.mockUserCache.EXPECT().Get(cacheKey, authedAppType).Return(nil)
			},
			assert: func(app *dbuser.AuthedApp, err error) {
				suite.NoError(err)
				suite.NotNil(app)
			},
		},
		{
			name:  "should found authed app",
			appID: existRecord1.AppID,
			given: func() {
				suite.mockUserCache.EXPECT().Get(cacheKey, authedAppType).Return(cache.ErrCacheMiss)
				suite.mockUserCache.EXPECT().Set(cacheKey, authedAppType, gomock.AssignableToTypeOf(time.Hour)).Return(nil)
			},
			assert: func(app *dbuser.AuthedApp, err error) {
				suite.NoError(err)
				suite.assertAuthedAppEqual(existRecord1, app)
			},
		},
		{
			name:  "should not found authed app",
			appID: "test_app_id_002",
			given: func() {
				suite.mockUserCache.EXPECT().Get(key.UserDataAuthedApp("test_app_id_002"), authedAppType).Return(cache.ErrCacheMiss)
			},
			assert: func(app *dbuser.AuthedApp, err error) {
				suite.NoError(err)
				suite.Nil(app)
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			if tc.given != nil {
				tc.given()
			}

			app, err := suite.repo.GetActiveByAppID(tc.appID)
			tc.assert(app, err)
		})
	}

}

func (suite *RepositoryTestSuite) assertAuthedAppEqual(expect *dbuser.AuthedApp, actual *dbuser.AuthedApp) {
	suite.NotNil(actual)
	suite.Equal(expect.AppID, actual.AppID)
	suite.Equal(expect.AppSecret, actual.AppSecret)
	suite.Equal(expect.Status, actual.Status)
	suite.ElementsMatch(expect.RedirectURIs, actual.RedirectURIs)
}

//go:embed testdata/repository/AuthedApp.sql
var dataForAppAuthRepo string

func (suite *RepositoryTestSuite) givenAuthedApps() {
	if _, err := suite.GetTransaction().Exec(dataForAppAuthRepo); err != nil {
		suite.Require().Fail("failed to insert data for app auth repo", err)
	}
}
