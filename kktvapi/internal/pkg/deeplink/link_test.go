package deeplink

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTitlePage(t *testing.T) {
	titleID := "00000429"
	testcases := []struct {
		name   string
		env    string
		expect string
	}{
		{
			name:   "WHEN env is test",
			env:    "test",
			expect: "https://test-web.kktv.com.tw/titles/00000429",
		},
		{
			name:   "WHEN env is prod",
			env:    "prod",
			expect: "https://www.kktv.me/titles/00000429",
		},
		{
			name:   "WHEN env is not set",
			env:    "",
			expect: "https://www.kktv.me/titles/00000429",
		},
	}
	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			Init(tc.env)

			actual := TitlePage(titleID)

			assert.Equal(t, tc.expect, actual)
		})
	}
}

func TestTitleList(t *testing.T) {
	Init("")
	titleListID := "xxxxxx"
	actual := TitleListPage(titleListID)
	assert.Equal(t, "https://www.kktv.me/titleList/xxxxxx", actual)
}

func TestCollectionPage(t *testing.T) {
	Init("")
	testcases := []struct {
		name           string
		collectionType string
		collectionName string
		exp            string
		queries        map[string]string
	}{
		{
			name:           "no queries",
			collectionType: "genre",
			collectionName: "親子",
			exp:            "https://www.kktv.me/browse/genre/%E8%A6%AA%E5%AD%90",
		},
		{
			name:           "country = Taiwan",
			collectionType: "genre",
			collectionName: "親子",
			queries: map[string]string{
				"country": "Taiwan",
				"sort":    "hot",
				"theme":   "寓教於樂",
			},
			exp: "https://www.kktv.me/browse/genre/%E8%A6%AA%E5%AD%90?country=Taiwan&sort=hot&theme=%E5%AF%93%E6%95%99%E6%96%BC%E6%A8%82",
		},
		{
			name:           "slash in the CollectionName, SHOULD be escaped",
			collectionType: "figure",
			collectionName: "Poni-Pachet SY/HOBIBOX",
			exp:            "https://www.kktv.me/browse/figure/Poni-Pachet%20SY%2FHOBIBOX",
		},
	}
	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			actual := CollectionPage(tc.collectionType, tc.collectionName, tc.queries)
			assert.Equal(t, tc.exp, actual)
		})
	}

}

func TestTitleListPage(t *testing.T) {
	Init("")
	testcases := []struct {
		name    string
		shareID string
		exp     string
		param   *TitleListPageParam
	}{
		{
			name:    "no queries",
			shareID: "expire-soon",
			exp:     "https://www.kktv.me/titleList/expire-soon",
		},
		{
			name:    "filter = genre:親子, is share link",
			shareID: "expire-soon-genre:親子",
			param: &TitleListPageParam{
				Queries:     map[string]string{"filter": "test"},
				IsShareLink: true,
				Caption:     "莫名其妙的標題",
			},
			exp: "https://www.kktv.me/titleList/expire-soon-genre:%E8%A6%AA%E5%AD%90?filter=test&utm_campaign=%E8%8E%AB%E5%90%8D%E5%85%B6%E5%A6%99%E7%9A%84%E6%A8%99%E9%A1%8C&utm_medium=share&utm_source=official",
		},
	}
	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			actual := TitleListPageWithParam(tc.shareID, tc.param)
			assert.Equal(t, tc.exp, actual)
		})
	}
}

func TestTimelinePage(t *testing.T) {
	Init("")
	testcases := []struct {
		name    string
		shareID string
		exp     string
		param   *TitleListPageParam
	}{
		{
			name:    "no queries",
			shareID: "anime-airing",
			exp:     "https://www.kktv.me/timeline/anime-airing",
		},
		{
			name:    "anime-airing with caption 123",
			shareID: "anime-airing",
			exp:     "https://www.kktv.me/timeline/anime-airing?utm_campaign=123&utm_medium=share&utm_source=official",
			param: &TitleListPageParam{
				Caption:     "123",
				IsShareLink: true,
			},
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			actual := TimelinePageWithParam(tc.shareID, tc.param)
			assert.Equal(t, tc.exp, actual)
		})
	}
}

func TestContinuePlay(t *testing.T) {
	Init("")
	t.Run("offset > 0", func(t *testing.T) {
		actual := ContinuePlay("01060429", 100)
		assert.Equal(t, "https://www.kktv.me/play/01060429?offset=100", actual)
	})

	t.Run("WHEN offset = 0, url should not contain offset", func(t *testing.T) {
		actual := ContinuePlay("01060429", 0)
		assert.Equal(t, "https://www.kktv.me/play/01060429", actual)
	})
}

func TestFeaturedPage(t *testing.T) {
	actual := FeaturedPage("genre:親子")
	assert.Equal(t, "https://www.kktv.me/featured?browse=genre%3A%E8%A6%AA%E5%AD%90", actual)
}
