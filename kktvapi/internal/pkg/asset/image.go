package asset

import "strings"

func ImageURL(bucketURL string) string {
	if bucketURL != "" {
		// do have image
		if strings.Contains(bucketURL, "kktv-prod-images") {
			// prod image bucket
			bucketURL = strings.Replace(bucketURL, "s3://kktv-prod-images", "https://images.kktv.com.tw", 1)
		} else {
			// test image bucket
			bucketURL = strings.Replace(bucketURL, "s3://kktv-test-images", "https://test-images.kktv.com.tw", 1)
		}
	}
	return bucketURL
}
