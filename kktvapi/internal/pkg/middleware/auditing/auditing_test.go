package auditing

import (
	"context"
	"net/http"
	"net/http/httptest"
	"sync"
	"sync/atomic"
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/auditing"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAuditing(t *testing.T) {
	r := require.New(t)
	type Order struct {
		ID     string
		Status string
	}
	ctrl := gomock.NewController(t)
	mockRepo := auditing.NewMockLogRepository(ctrl)
	auditor := NewAuditor(mockRepo)

	tests := []struct {
		name      string
		given     func() *sync.WaitGroup
		request   *http.Request
		handlerFn http.Handler
	}{
		{
			name: "should invoke insert audit log WHEN using LogByUser",
			given: func() *sync.WaitGroup {
				wg := sync.WaitGroup{}
				wg.Add(1)
				mockRepo.EXPECT().Insert(gomock.Any()).DoAndReturn(func(log *dbuser.AuditLog) error {
					plog.Debug("insert log").Interface("log", log).Send()
					r.Equal(dbuser.AuditModifierTypeUser, log.ModifierType)
					r.Equal("order-01", log.TargetID)
					r.Equal("order", log.TargetType)
					r.Equal("user", log.AssociatedType.String)
					r.Equal("user-id-josie", log.AssociatedID.String)
					// assert the set modifier from the JWT user
					r.Equal("user-id-josie", log.ModifierID.String)

					r.Equal(&dbuser.AuditLogDetail{Record: Order{ID: "order-01", Status: "pending"}}, log.Detail)

					wg.Done()
					return nil
				})
				return &wg
			},
			request: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/", nil)
				ctx := context.WithValue(req.Context(), "user", model.JwtUser{Sub: "user-id-josie"})
				return req.WithContext(ctx)
			}(),
			handlerFn: auditor.LogByUser(
				func() http.HandlerFunc {
					return func(w http.ResponseWriter, r *http.Request) {
						plog.Debug("handler: update order").Send()

						ctx := r.Context()
						au := ctx.Value(middleware.KeyAuditing).(*Auditing)
						au.TargetCreated("order", "order-01").
							DetailWhole(Order{ID: "order-01", Status: "pending"}).
							Associated("user-id-josie", "user")
						au.Keep = true

					}
				}()),
		},
		{
			name: "should invoke insert audit log WHEN using LogByConsole",
			given: func() *sync.WaitGroup {
				wg := sync.WaitGroup{}
				wg.Add(1)
				mockRepo.EXPECT().Insert(gomock.Any()).DoAndReturn(func(log *dbuser.AuditLog) error {
					plog.Debug("insert log").Interface("log", log).Send()
					r.Equal(dbuser.AuditModifierTypeConsole, log.ModifierType)
					r.Equal(dbuser.AuditModActionDel, log.ModAction)
					r.Equal("order-01", log.TargetID)
					r.Equal("order", log.TargetType)
					// assert the set modifier from the JWT console user
					r.Equal("<EMAIL>", log.ModifierID.String)
					r.Equal("CS (admin)", log.ModifierName.String)

					wg.Done()
					return nil
				})
				return &wg
			},
			request: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/", nil)
				ctx := context.WithValue(req.Context(), "user", dbmeta.ConsoleUser{Email: "<EMAIL>", Name: "CS", Roles: "admin"})
				return req.WithContext(ctx)
			}(),
			handlerFn: auditor.LogByConsole(
				func() http.HandlerFunc {
					return func(w http.ResponseWriter, r *http.Request) {
						ctx := r.Context()
						au := ctx.Value(middleware.KeyAuditing).(*Auditing)
						au.TargetDeleted("order", "order-01")
						au.Keep = true
					}
				}()),
		},
		{
			name:  "should not insert log WHEN auditing is not kept",
			given: func() *sync.WaitGroup { return nil },
			request: func() *http.Request {
				return httptest.NewRequest(http.MethodGet, "/", nil)
			}(),
			handlerFn: auditor.LogByConsole(
				func() http.HandlerFunc {
					return func(w http.ResponseWriter, r *http.Request) {
						ctx := r.Context()
						au := ctx.Value(middleware.KeyAuditing).(*Auditing)
						au.TargetCreated("order", "order-01").
							DetailWhole(Order{ID: "order-01", Status: "pending"}).
							Associated("user-id-josie", "user")
					}
				}()),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wg := tt.given()
			req := tt.request
			rr := httptest.NewRecorder()
			tt.handlerFn.ServeHTTP(rr, req)
			if wg != nil {
				wg.Wait()
			}
		})
	}
}

func TestSetAuditLog(t *testing.T) {
	tests := []struct {
		name       string
		ctx        context.Context
		modFn      func(check *atomic.Int64) []Modification
		thenAssert func(check *atomic.Int64)
	}{
		{
			name: "modification have been executed",
			ctx: func() context.Context {
				return context.WithValue(context.Background(), middleware.KeyAuditing,
					&Auditing{
						LogBuilder: auditing.NewLogBuilder(),
					})
			}(),
			modFn: func(check *atomic.Int64) []Modification {
				return []Modification{
					func(builder *Auditing) {
						check.Add(1)
						check.Add(1)
					},
					func(builder *Auditing) {
						check.Add(2)
					},
				}
			},
			thenAssert: func(check *atomic.Int64) {
				assert.Equal(t, int64(4), check.Load())
			},
		},
		{
			name: "no modification",
			ctx: func() context.Context {
				return context.WithValue(context.Background(), middleware.KeyAuditing,
					&Auditing{
						LogBuilder: auditing.NewLogBuilder(),
					})
			}(),
			modFn: func(check *atomic.Int64) []Modification {
				return []Modification{}
			},
			thenAssert: func(check *atomic.Int64) {
				assert.Equal(t, int64(0), check.Load())
			},
		},
		{
			name: "no auditing in context",
			ctx: func() context.Context {
				return context.Background()
			}(),
			modFn: func(check *atomic.Int64) []Modification {
				return []Modification{
					func(builder *Auditing) {
						check.Add(2)
					},
				}
			},
			thenAssert: func(check *atomic.Int64) {
				assert.Equal(t, int64(0), check.Load())
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var fnCheck atomic.Int64
			Log(tt.ctx, tt.modFn(&fnCheck)...)
			tt.thenAssert(&fnCheck)
		})
	}
}
