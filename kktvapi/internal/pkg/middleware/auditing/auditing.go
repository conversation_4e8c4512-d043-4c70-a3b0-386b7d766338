package auditing

import (
	"context"
	"fmt"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/auditing"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

type Auditor struct {
	auditingRepo auditing.LogRepository
}

func NewAuditor(auditingRepo auditing.LogRepository) Auditor {
	return Auditor{
		auditingRepo: auditingRepo,
	}
}

type Auditing struct {
	auditing.LogBuilder
	Keep bool
}

// LogByUser depends on the kkmiddleware.JWTAuth, which utilize the console user info for auditing
// once this middleware is applied, the console user ID is put into auditing
func (au *Auditor) LogByUser(next http.Handler) http.Handler {
	return au.log(func(builder *Auditing, ctx context.Context) {
		builder.ByUser()
		if user, ok := ctx.Value("user").(model.JwtUser); ok {
			builder.ModifierID(user.Sub)
		}
	}, next)
}

// LogByConsole depends on the kkmiddleware.JWTConsoleAuth, which utilize the client user info for auditing
// once this middleware is applied, the console user ID and name are fetched and put into auditing
func (au *Auditor) LogByConsole(next http.Handler) http.Handler {
	return au.log(func(builder *Auditing, ctx context.Context) {
		builder.ByConsole()
		if user, ok := ctx.Value("user").(dbmeta.ConsoleUser); ok {
			builder.Modifier(user.Email, fmt.Sprintf("%s (%s)", user.Name, user.Roles))
		}
	}, next)
}

func (au *Auditor) log(modification func(*Auditing, context.Context), next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		audit := &Auditing{
			LogBuilder: auditing.NewLogBuilder(),
			// default not to keep,
			// let invoker determines if it should Keep the AuditLog or not in the end of the session
			Keep: false,
		}
		ctx := r.Context()
		modification(audit, ctx)
		ctx = context.WithValue(ctx, middleware.KeyAuditing, audit)
		r = r.WithContext(ctx)

		plog.Debug("middleware auditing: before handlers serving").Send()
		next.ServeHTTP(w, r)
		plog.Debug("middleware auditing: after handlers serving").Send()

		if !audit.Keep {
			return
		}
		logs := audit.Build()
		validLogs := make([]*dbuser.AuditLog, 0, len(logs))
		for _, log := range logs {
			if log.IsValidLog() {
				validLogs = append(validLogs, log)
			} else {
				plog.Warn("middleware: auditing: trying to insert an invalid log").Interface("log", log).Send()
			}
		}
		if len(validLogs) == 0 {
			return
		}
		go func() {
			if err := au.auditingRepo.Insert(validLogs...); err != nil {
				plog.Warn("middleware: auditing: insert logs failed").Err(err).Interface("logs", validLogs).Send()
			}
		}()
	})
}

// Modification is an Option pattern, which is designed to update fields in Auditing
type Modification func(builder *Auditing)

func Log(ctx context.Context, mods ...Modification) *Auditing {
	if len(mods) == 0 {
		return nil
	}
	au, ok := ctx.Value(middleware.KeyAuditing).(*Auditing)
	if !ok {
		return nil
	}
	for _, mod := range mods {
		mod(au)
	}
	return au
}

func KeepLog() Modification {
	return func(au *Auditing) {
		au.Keep = true
	}
}
