package middleware

import (
	"net/http"
	"strings"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/apidoc"
	"github.com/KKTV/kktv-api-v3/pkg/auth"
	"github.com/KKTV/kktv-api-v3/pkg/log"
)

type APIDocAuthenticator struct {
	jwtAuth auth.JWTAuth
}

func NewAPIDocAuthenticator(jwtAuth auth.JWTAuth) *APIDocAuthenticator {
	return &APIDocAuthenticator{
		jwtAuth: jwtAuth,
	}
}

func (a *APIDocAuthenticator) Auth(next http.Handler, loginPath string) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// bypass when path is for authentication
		if strings.HasPrefix(r.URL.Path, "/auth/doc/") {
			next.ServeHTTP(w, r)
			return
		}
		cookie, err := r.<PERSON>("doc_token")
		if err != nil {
			log.Debug("APIDocAuthenticator.Auth: fail to get cookie").Err(err).Send()
			redirect(w, r, loginPath)
			return
		}

		claims := &apidoc.DocClaims{}
		token, err := a.jwtAuth.ParseToken(cookie.Value, claims)
		if err != nil || !token.Valid {
			redirect(w, r, loginPath)
			return
		}

		next.ServeHTTP(w, r)
	})
}

func redirect(w http.ResponseWriter, r *http.Request, loginPath string) {
	http.Redirect(w, r, loginPath, http.StatusTemporaryRedirect)
}
