package appauth

import (
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/appauth"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/golang/mock/gomock"
	"github.com/jinzhu/copier"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gopkg.in/guregu/null.v3"
)

type AuthTestSuite struct {
	suite.Suite
	r    *require.Assertions
	ctrl *gomock.Controller

	mockRepo *appauth.MockRepository
	auth     *Auth
}

func TestAuthTestSuite(t *testing.T) {
	suite.Run(t, new(AuthTestSuite))
}

func (suite *AuthTestSuite) SetupTest() {
	suite.r = suite.Require()
	suite.ctrl = gomock.NewController(suite.T())

	suite.mockRepo = appauth.NewMockRepository(suite.ctrl)
	suite.auth = NewAuth(suite.mockRepo)
}

func (suite *AuthTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func (suite *AuthTestSuite) TestBasicAuth() {
	testcases := []struct {
		name       string
		appID      string
		appSecret  string
		wantStatus int
		given      func()
	}{
		{
			name:       "empty authorization",
			wantStatus: http.StatusUnauthorized,
		},
		{
			name:       "invalid appID",
			appID:      "invalidAppID",
			appSecret:  "invalidAppSecret",
			wantStatus: http.StatusUnauthorized,
			given: func() {
				suite.mockRepo.EXPECT().GetActiveByAppID("invalidAppID").Return(nil, nil)
			},
		},
		{
			name:       "invalid appSecret",
			appID:      "validAppID",
			appSecret:  "invalidAppSecret",
			wantStatus: http.StatusUnauthorized,
			given: func() {
				app := &dbuser.AuthedApp{
					AppID:     "validAppID",
					AppSecret: "validAppSecret",
				}
				suite.mockRepo.EXPECT().GetActiveByAppID("validAppID").Return(app, nil)
			},
		},
		{
			name:       "unknown error",
			appID:      "validAppID",
			appSecret:  "validAppSecret",
			wantStatus: http.StatusInternalServerError,
			given: func() {
				suite.mockRepo.EXPECT().GetActiveByAppID("validAppID").Return(nil, errors.New("exceeded limit"))
			},
		},
		{
			name:       "valid appID and appSecret",
			appID:      "validAppID",
			appSecret:  "validAppSecret",
			wantStatus: http.StatusOK,
			given: func() {
				app := &dbuser.AuthedApp{
					AppID:     "validAppID",
					AppSecret: "validAppSecret",
				}
				suite.mockRepo.EXPECT().GetActiveByAppID("validAppID").Return(app, nil)
			},
		},
	}

	for _, tc := range testcases {
		suite.T().Run(tc.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", "/vendors/feversocial/api", nil)
			suite.r.NoError(err)

			if tc.appID != "" && tc.appSecret != "" {
				req.SetBasicAuth(tc.appID, tc.appSecret)
			}
			if tc.given != nil {
				tc.given()
			}

			handlerFunc := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				if _, ok := r.Context().Value(middleware.KeyAuthedApp).(*dbuser.AuthedApp); ok {
					render.JSON(w, http.StatusOK, nil)
					return
				}
				render.JSON(w, http.StatusBadRequest, nil)
			})

			rr := httptest.NewRecorder()
			suite.auth.BasicAuth(handlerFunc).ServeHTTP(rr, req)

			suite.r.Equal(tc.wantStatus, rr.Code)
		})
	}
}

func (suite *AuthTestSuite) TestJWTAuth() {
	const (
		validToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJ2YWxpZEFwcCIsImV4cCI6NDgyNzk1NzI0NSwiaWF0IjoxNzAzODE5NjQ1LCJpc3MiOiJra3R2Iiwic3ViIjoidmFsaWRBcHBJRCJ9.oivLuLgRQ8gNw8D_4pp6MnqKt6t2g60EfZo8cNtpjQI"
	)

	authedApp := &dbuser.AuthedApp{
		Name:      "validApp",
		AppID:     "validAppID",
		AppSecret: "validAppSecret",
		SignKey:   null.StringFrom("testSignKey"),
	}

	testcases := []struct {
		name       string
		token      string
		wantStatus int
		given      func()
	}{
		{
			name:       "empty authorization",
			wantStatus: http.StatusUnauthorized,
		},
		{
			name:       "invalid token",
			token:      "invalidToken",
			wantStatus: http.StatusUnauthorized,
		},
		{
			name:       "expired token",
			token:      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJ2YWxpZEFwcCIsImV4cCI6MTcwMzczMzI4NCwiaWF0IjoxNzAzODE5Njg0LCJpc3MiOiJra3R2Iiwic3ViIjoidmFsaWRBcHBJRCJ9.yT7qx_aVWFFMOWRq7rwuFyW78rrS6L-6TiKBaH9QxR0",
			wantStatus: http.StatusUnauthorized,
		},
		{
			name:       "valid token but failed to get app from repo",
			token:      validToken,
			wantStatus: http.StatusInternalServerError,
			given: func() {
				suite.mockRepo.EXPECT().GetActiveByAppID(authedApp.AppID).Return(nil, errors.New("program_limit_exceeded"))
			},
		},
		{
			name:       "valid token but app is not found",
			token:      validToken,
			wantStatus: http.StatusUnauthorized,
			given: func() {
				suite.mockRepo.EXPECT().GetActiveByAppID(authedApp.AppID).Return(nil, nil)
			},
		},
		{
			name:       "valid token but sign key of app is mismatched",
			token:      validToken,
			wantStatus: http.StatusUnauthorized,
			given: func() {
				var app dbuser.AuthedApp
				_ = copier.Copy(&app, authedApp)
				app.SignKey = null.StringFrom("otherSignKey")
				suite.mockRepo.EXPECT().GetActiveByAppID(app.AppID).Return(&app, nil)
			},
		},
		{
			name:       "valid token and app is active",
			token:      validToken,
			wantStatus: http.StatusOK,
			given: func() {
				suite.mockRepo.EXPECT().GetActiveByAppID(authedApp.AppID).Return(authedApp, nil)
			},
		},
	}

	for _, tc := range testcases {
		suite.T().Run(tc.name, func(t *testing.T) {
			rr := httptest.NewRecorder()
			req, err := http.NewRequest("POST", "/vendors/feversocial/api", nil)
			suite.r.NoError(err)
			req.Header.Set("Authorization", "Bearer "+tc.token)
			handlerFunc := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				if _, ok := r.Context().Value(middleware.KeyAuthedApp).(*dbuser.AuthedApp); ok {
					render.JSON(w, http.StatusOK, nil)
					return
				}
				render.JSON(w, http.StatusBadRequest, nil)
			})
			if tc.given != nil {
				tc.given()
			}

			suite.auth.JWTAuth(handlerFunc).ServeHTTP(rr, req)
			suite.r.Equal(tc.wantStatus, rr.Code)
		})
	}
}
