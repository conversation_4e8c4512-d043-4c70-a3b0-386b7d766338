package appauth

import (
	"context"
	"net/http"
	"strings"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/appauth"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/auth"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"gopkg.in/dgrijalva/jwt-go.v3"
)

type Auth struct {
	authedAppRepo appauth.Repository
}

func NewAuth(appRepo appauth.Repository) *Auth {
	return &Auth{
		authedAppRepo: appRepo,
	}
}

func (a *Auth) BasicAuth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		appID, appSecret, ok := r.BasicAuth()
		if !ok {
			render.JSONUnauthorized(w, respInvalidCredentials)
			return
		}

		authedApp, err := a.authedAppRepo.GetActiveByAppID(appID)
		if err != nil {
			plog.Error("middlewareAppAuth: BasicAuth: failed to authedAppRepo.GetActiveByAppID").
				Str("app_id", appID).
				Str("app_secret", appSecret).
				Err(err).
				Send()
			render.JSONInternalServerErr(w, respUnknownError)
			return
		}
		if authedApp == nil || authedApp.AppSecret != appSecret {
			render.JSONUnauthorized(w, respInvalidCredentials)
			return
		}

		ctx := r.Context()
		ctx = context.WithValue(ctx, middleware.KeyAuthedApp, authedApp)
		r = r.WithContext(ctx)
		next.ServeHTTP(w, r)
	})
}

func (a *Auth) JWTAuth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		tokenString := strings.TrimPrefix(r.Header.Get("Authorization"), "Bearer ")
		if tokenString == "" {
			render.JSONUnauthorized(w, respInvalidCredentials)
			return
		}

		jwtAuth := auth.NewJWTAuth(jwt.SigningMethodHS256, nil)
		unverifiedToken, err := jwtAuth.ParseUnverifiedToken(tokenString, &jwt.StandardClaims{})
		if err != nil {
			render.JSONUnauthorized(w, respInvalidCredentials)
			return
		}

		claims, ok := unverifiedToken.Token.Claims.(*jwt.StandardClaims)
		if !ok {
			render.JSONUnauthorized(w, respInvalidCredentials)
			return
		}

		authedApp, err := a.authedAppRepo.GetActiveByAppID(claims.Subject)
		if err != nil {
			plog.Error("middlewareAppAuth: jwtAuth: failed to authedAppRepo.GetActiveByAppID").
				Str("app_id", claims.Subject).
				Err(err).
				Send()
			render.JSONInternalServerErr(w, respUnknownError)
			return
		} else if authedApp == nil {
			render.JSONUnauthorized(w, respInvalidApp)
			return
		}
		if err = unverifiedToken.VerifySignature([]byte(authedApp.SignKey.String)); err != nil {
			render.JSONUnauthorized(w, respInvalidCredentials)
			return
		}

		ctx := r.Context()
		ctx = context.WithValue(ctx, middleware.KeyAuthedApp, authedApp)
		r = r.WithContext(ctx)
		next.ServeHTTP(w, r)
	})
}
