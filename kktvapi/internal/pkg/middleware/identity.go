package middleware

import (
	"context"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/KKTV/kktv-api-v3/pkg/render"
)

type Identity struct {
	UserID   string
	DeviceID string
}

func Identifier() func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		fn := func(w http.ResponseWriter, r *http.Request) {
			var userID string
			user := r.Context().Value("user")
			if user != nil {
				if userModel, ok := user.(model.JwtUser); ok && !userModel.IsGuest() {
					userID = userModel.Sub
				}
			}
			deviceID := r.Header.Get(httpreq.HeaderDeviceID)

			if userID != "" || deviceID != "" {
				ctx := context.WithValue(r.Context(), KeyIdentity, Identity{
					UserID:   userID,
					DeviceID: deviceID,
				})
				newReq := r.WithContext(ctx)
				next.ServeHTTP(w, newReq)
				return
			}
			resp := rest.Error("no any identity is given", "401")
			render.JSON(w, http.StatusUnauthorized, resp)
			return
		}
		return http.HandlerFunc(fn)
	}
}
