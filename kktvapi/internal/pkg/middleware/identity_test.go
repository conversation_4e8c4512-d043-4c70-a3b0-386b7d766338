package middleware

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"github.com/go-zoo/bone"
	"github.com/justinas/alice"
)

type IdentifierTestSuite struct {
	suite.Suite
	req *require.Assertions
	app *bone.Mux
}

func TestIdentifierTestSuite(t *testing.T) {
	suite.Run(t, new(IdentifierTestSuite))
}

func (suite *IdentifierTestSuite) SetupTest() {
	suite.req = suite.Require()
	suite.app = bone.New()

	chain := alice.New(Identifier())
	handlerFunc := func(writer http.ResponseWriter, request *http.Request) {
		if idty, ok := request.Context().Value(KeyIdentity).(Identity); ok {
			render.JSON(writer, http.StatusOK, idty)
			return
		}
		render.Text(writer, http.StatusBadRequest, "")
	}
	suite.app.Get("/test", chain.ThenFunc(handlerFunc))
}

func (suite *IdentifierTestSuite) TestIdentifier() {
	testcases := []struct {
		name       string
		given      func() *http.Request
		thenAssert func(statusCode int, body string)
	}{
		{
			name: "WHEN user login and deviceID is given",
			given: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/test", nil)
				req.Header.Set(httpreq.HeaderDeviceID, "472c8bfe-9de4-5683-9d87-1d6741f6d34f")
				ctx := req.Context()
				ctx = context.WithValue(ctx, "user", model.JwtUser{
					Sub: "test-user-id",
				})
				return req.WithContext(ctx)
			},
			thenAssert: func(statusCode int, body string) {
				suite.Equal(http.StatusOK, statusCode)
				suite.JSONEq(`{"DeviceID":"472c8bfe-9de4-5683-9d87-1d6741f6d34f", "UserID":"test-user-id"}`, body)
			},
		},
		{
			name: "WHEN only deviceID is given",
			given: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/test", nil)
				req.Header.Set(httpreq.HeaderDeviceID, "472c8bfe-9de4-5683-9d87-1d6741f6d34f")
				return req
			},
			thenAssert: func(statusCode int, body string) {
				suite.Equal(http.StatusOK, statusCode)
				suite.JSONEq(`{"DeviceID":"472c8bfe-9de4-5683-9d87-1d6741f6d34f", "UserID":""}`, body)
			},
		},
		{
			name: "WHEN have no neither deviceID or user login",
			given: func() *http.Request {
				req := httptest.NewRequest(http.MethodGet, "/test", nil)
				return req
			},
			thenAssert: func(statusCode int, body string) {
				suite.Equal(http.StatusUnauthorized, statusCode)
				suite.JSONEq(`{"error":{"code":"401", "message":"no any identity is given"}, "data":null}`, body)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			req := tc.given()

			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			tc.thenAssert(rr.Code, rr.Body.String())
		})
	}

}
