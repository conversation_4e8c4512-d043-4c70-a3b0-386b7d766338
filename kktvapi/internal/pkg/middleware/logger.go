package middleware

import (
	"bytes"
	"io"
	"net/http"

	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	zlog "github.com/rs/zerolog/log"
)

func RequestLogger() func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		fn := func(w http.ResponseWriter, r *http.Request) {
			logger := zlog.Logger
			buf, err := io.ReadAll(r.Body)
			if err != nil {
				logger.Error().Msgf("Error reading request body: %v", err.Error())
				next.ServeHTTP(w, r)
				return
			}

			logger.Info().
				Str("method", r.Method).
				Stringer("url", r.URL).
				Interface("params", r.URL.Query()).
				Str("token", r.Header.Get("Authorization")).
				Bytes("body", buf).
				Str("user_agent", r.<PERSON>r<PERSON>()).
				Str("ip", r.<PERSON><PERSON>ddr).
				Str("referer", r.<PERSON>()).
				Str("X-Forwarded-For", r.Header.Get("X-Forwarded-For")).
				Str("X-Device-ID", r.Header.Get(httpreq.HeaderDeviceID)).
				Str("X-KKTV-Platform", r.Header.Get(httpreq.HeaderPlatform)).
				Str("X-KKTV-App-Version", r.Header.Get(httpreq.HeaderAppVersion)).
				Send()

			reader := io.NopCloser(bytes.NewBuffer(buf))
			r.Body = reader
			next.ServeHTTP(w, r)
		}
		return http.HandlerFunc(fn)
	}
}
