package tracing

import (
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"

	"github.com/getsentry/sentry-go"
)

func Initialize(version string) error {
	// init sentry
	err := sentry.Init(sentry.ClientOptions{
		Dsn:              "https://<EMAIL>/4505605086183424",
		Debug:            config.Debug,
		AttachStacktrace: true,
		Release:          version,
		Environment:      config.Env,
		TracesSampleRate: 1.0,
	})
	return err
}

func WithScope(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		errCtx := map[string]string{
			"UserAgent": r.User<PERSON>(),
			"Referer":   r.<PERSON>(),
		}
		requestCtx := sentry.Context{
			"Method": r.Method,
			"URL":    r.URL,
			"Query":  r.URL.RawQuery,
		}
		userCtx := sentry.User{
			IPAddress: r.RemoteAddr,
		}

		if jwtUser, ok := r.Context().Value(middleware.KeyUser).(model.JwtUser); ok && !jwtUser.IsGuest() {
			userCtx.ID = jwtUser.Sub
			userCtx.Segment = jwtUser.Role
		}

		sentry.ConfigureScope(func(scope *sentry.Scope) {
			scope.SetTags(errCtx)
			scope.SetContext("Request", requestCtx)
			scope.SetUser(userCtx)
		})

		next.ServeHTTP(w, r)
	})
}
