package middleware

import (
	"context"
	"net/http"
	"strings"

	"github.com/KKTV/kktv-api-v3/kkapp/kkmiddleware"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	"github.com/KKTV/kktv-api-v3/pkg/version"
)

type HideLustContentMiddleware struct {
	metaCacheReader cache.Cacher
	platformMaps    map[string]string
}

func NewHideLustContentMiddleware(metaCacheReader cache.Cacher) *HideLustContentMiddleware {
	return &HideLustContentMiddleware{
		metaCacheReader: metaCacheReader,
		platformMaps: map[string]string{
			"tvos":       "appletv",
			"ios":        "ios",
			"ipados":     "ios",
			"android":    "android",
			"android tv": "androidtv",
		},
	}
}

func (m *HideLustContentMiddleware) Handle(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		hideLustContent := false

		appPlatform := httpreq.GetPlatform(r)
		appVer := r.Header.Get(httpreq.HeaderAppVersion)

		defer func() {
			log.Debug("HideLustContentMiddleware").
				Str("platform", appPlatform).
				Str("version", appVer).
				Bool("hide_lust_content", hideLustContent).
				Send()

			ctx := context.WithValue(r.Context(), KeyHideLustContent, hideLustContent)
			r = r.WithContext(ctx)
			next.ServeHTTP(w, r)
		}()

		if appPlatform == "" || appVer == "" { // skip if version info is missing
			return
		}

		var conf cachemeta.ContentControlConfig
		cKey := key.MetaGetServiceGeneralConfig()
		if err := m.metaCacheReader.HGet(cKey, key.MetaServiceGeneralConfigHashKeys.ContentControl, &conf); err != nil {
			log.Warn("HideLustContentMiddleware: failed to get content_control config").
				Str("cache_key", cKey).
				Err(err).
				Send()
			return
		}

		switch conf.HideLustContent.HiddenBy {
		case cachemeta.HideLustHiddenByReviewVersion:
			serviceStatus, ok := r.Context().Value(kkmiddleware.ServiceStatusContextKey).(*model.ServiceStatus)
			if !ok {
				return
			}

			platformKey := m.platformMaps[appPlatform]
			app, found := serviceStatus.AppVersion[platformKey]
			if !found {
				return
			}

			res, err := version.Compare(appVer, app.ReviewVersion)
			if err == nil {
				hideLustContent = res == 0
			}
		case cachemeta.HideLustHiddenByPlatforms:
			for _, platform := range conf.HideLustContent.Platforms {
				// case-insensitive comparison
				if strings.ToLower(platform) == appPlatform {
					hideLustContent = true
					break
				}
			}
		default:
			log.Warn("HideLustContentMiddleware: unknown HiddenByType in content_control config").
				Interface("config", conf).
				Send()
		}

	})
}
