package auth

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/auth"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/stretchr/testify/assert"
	"gopkg.in/dgrijalva/jwt-go.v3"
)

func TestJWTAuth(t *testing.T) {
	type expectedResponse struct {
		Status int
		Body   string
	}

	var (
		iat            = time.Now()
		exp            = iat.Add(24 * time.Hour)
		standardClaims = jwt.StandardClaims{
			IssuedAt:  iat.Unix(),
			ExpiresAt: exp.Unix(),
			Audience:  "kktv.com",
			Issuer:    "kktv",
			Subject:   "josie",
		}
		generateJWT = func(claims *modelmw.AccessTokenClaims) string {
			jwtAuth := auth.NewJWTAuth(jwt.SigningMethodHS256, []byte(config.JWTAuthSignedString))
			tokenString, _ := jwtAuth.GenerateToken(*claims)
			return tokenString
		}
	)

	var (
		assertJwtUserExists = func(r *http.Request, claims *modelmw.AccessTokenClaims) {
			jwtUser, ok := r.Context().Value(middleware.KeyUser).(model.JwtUser)
			assert.True(t, ok)

			expectedJwtUser := model.JwtUser{
				Iat:            claims.IssuedAt,
				Exp:            claims.ExpiresAt,
				Aud:            claims.Audience,
				Iss:            claims.Issuer,
				Sub:            claims.Subject,
				Role:           claims.Role.String(),
				Type:           claims.Type.String(),
				HasBoughtPrime: claims.HasBoughtPrime,
			}
			assert.Equal(t, expectedJwtUser, jwtUser)
		}
		assertAccessUserExists = func(r *http.Request, claims *modelmw.AccessTokenClaims) {
			accessUser, ok := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
			assert.True(t, ok)
			assert.Equal(t, claims.Subject, accessUser.UserID)

			expectedMembership := dbuser.ComposeMembership(claims.Role, claims.Type)
			if len(claims.Memberships) > 0 && claims.Memberships[0].Role != "" {
				expectedMembership = claims.Memberships
			}
			assert.Equal(t, expectedMembership, accessUser.Memberships)
		}
	)

	testcases := []struct {
		name         string
		claims       *modelmw.AccessTokenClaims
		thenAssert   func(r *http.Request, claims *modelmw.AccessTokenClaims)
		expectedResp expectedResponse
	}{
		{
			name: "GIVEN invalid token THEN return unauthorized",
			expectedResp: expectedResponse{
				Status: http.StatusUnauthorized,
				Body:   `{"error":{"code":"401","message":"invalid token"},"data":null}`,
			},
		},
		{
			name: "GIVEN expired token THEN return unauthorized",
			claims: &modelmw.AccessTokenClaims{
				StandardClaims: jwt.StandardClaims{
					IssuedAt:  iat.Unix(),
					ExpiresAt: exp.Add(-30 * time.Hour).Unix(),
					Audience:  standardClaims.Audience,
					Issuer:    standardClaims.Issuer,
					Subject:   standardClaims.Subject,
				},
				Role:           dbuser.RoleFreeTrial,
				Type:           dbuser.TypeGeneral,
				HasBoughtPrime: true,
			},
			expectedResp: expectedResponse{
				Status: http.StatusUnauthorized,
				Body:   `{"error":{"code":"401","message":"invalid token"},"data":null}`,
			},
		},
		{
			name: "GIVEN valid token with guest user THEN got jwt user and access user",
			claims: &modelmw.AccessTokenClaims{
				StandardClaims: jwt.StandardClaims{
					IssuedAt:  standardClaims.IssuedAt,
					ExpiresAt: standardClaims.ExpiresAt,
					Audience:  standardClaims.Audience,
					Issuer:    standardClaims.Issuer,
					Subject:   "guest:josie",
				},
				Role:           dbuser.RoleGuest,
				Type:           dbuser.TypeGeneral,
				HasBoughtPrime: false,
			},
			thenAssert: func(r *http.Request, claims *modelmw.AccessTokenClaims) {
				assertJwtUserExists(r, claims)
				assertAccessUserExists(r, claims)
			},
			expectedResp: expectedResponse{
				Status: http.StatusOK,
			},
		},
		{
			name: "GIVEN valid token without membership THEN got jwt user and access user",
			claims: &modelmw.AccessTokenClaims{
				StandardClaims: standardClaims,
				Role:           dbuser.RoleExpired,
				Type:           dbuser.TypeGeneral,
				HasBoughtPrime: true,
			},
			thenAssert: func(r *http.Request, claims *modelmw.AccessTokenClaims) {
				assertJwtUserExists(r, claims)
				assertAccessUserExists(r, claims)
			},
			expectedResp: expectedResponse{
				Status: http.StatusOK,
			},
		},
		{
			name: "GIVEN valid token with membership that role is not included THEN got jwt user and access user",
			claims: &modelmw.AccessTokenClaims{
				StandardClaims: standardClaims,
				Role:           dbuser.RolePremium,
				Type:           dbuser.TypePrime,
				HasBoughtPrime: true,
				Memberships:    dbuser.Membership{},
			},
			thenAssert: func(r *http.Request, claims *modelmw.AccessTokenClaims) {
				assertJwtUserExists(r, claims)
				assertAccessUserExists(r, claims)
			},
			expectedResp: expectedResponse{
				Status: http.StatusOK,
			},
		},
		{
			name: "GIVEN valid token with membership that role is included THEN got jwt user and access user",
			claims: &modelmw.AccessTokenClaims{
				StandardClaims: standardClaims,
				Role:           dbuser.RolePremium,
				Type:           dbuser.TypePrime,
				HasBoughtPrime: true,
				Memberships:    dbuser.Membership{{Role: dbuser.MemberRolePremium}},
			},
			thenAssert: func(r *http.Request, claims *modelmw.AccessTokenClaims) {
				assertJwtUserExists(r, claims)
				assertAccessUserExists(r, claims)
			},
			expectedResp: expectedResponse{
				Status: http.StatusOK,
			},
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", "/", nil)
			if err != nil {
				t.Fatal(err)
			}

			tokenString := "invalid_token"
			if tc.claims != nil {
				tokenString = generateJWT(tc.claims)
			}

			req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", tokenString))
			handlerFunc := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				if tc.thenAssert != nil {
					tc.thenAssert(r, tc.claims)
				}
			})

			rr := httptest.NewRecorder()
			JWTAuth(handlerFunc).ServeHTTP(rr, req)

			assert.Equal(t, tc.expectedResp, expectedResponse{
				Status: rr.Code,
				Body:   rr.Body.String(),
			})
		})
	}
}
