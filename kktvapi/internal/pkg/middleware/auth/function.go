package auth

import (
	"errors"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/pkg/auth"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"gopkg.in/dgrijalva/jwt-go.v3"
)

func parseTokenStringToUser(tokenString string) (model.JwtUser, modelmw.AccessUser, error) {
	jwtAuth := auth.NewJWTAuth(jwt.SigningMethodHS256, []byte(config.JWTAuthSignedString))
	token, err := jwtAuth.ParseToken(tokenString, &modelmw.AccessTokenClaims{})
	if err != nil {
		return model.JwtUser{}, modelmw.AccessUser{}, err
	}

	claims, ok := token.Claims.(*modelmw.AccessTokenClaims)
	if !ok {
		return model.JwtUser{}, modelmw.AccessUser{}, errors.New("invalid token")
	}

	jwtUser := model.JwtUser{
		Iat:            claims.IssuedAt,
		Exp:            claims.ExpiresAt,
		Aud:            claims.Audience,
		Iss:            claims.Issuer,
		Sub:            claims.Subject,
		Role:           claims.Role.String(),
		Type:           claims.Type.String(),
		HasBoughtPrime: claims.HasBoughtPrime,
	}
	membership := dbuser.Membership{}
	if claims.Memberships != nil {
		membership = claims.Memberships
	}

	accessUser := modelmw.AccessUser{
		UserID:      claims.Subject,
		Memberships: membership,
	}

	// [BEGIN] FIXME: when permission determination doesn't rely on role and type, remove this block
	// backward compatible for the legacy token without membership
	isLegacyToken := !jwtUser.IsGuest() && (len(accessUser.Memberships) <= 0 || accessUser.Memberships[0].Role == "")
	if isLegacyToken {
		accessUser.Memberships = dbuser.ComposeMembership(claims.Role, claims.Type)
	}
	// [END]

	return jwtUser, accessUser, nil
}
