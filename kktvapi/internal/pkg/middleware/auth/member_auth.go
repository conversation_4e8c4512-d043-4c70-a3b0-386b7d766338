package auth

import (
	"context"
	"net/http"
	"strings"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/render"
)

func MemberAuth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		tokenString := strings.TrimPrefix(r.Header.Get("Authorization"), "Bearer ")
		jwtUser, accessUser, err := parseTokenStringToUser(tokenString)
		if err != nil {
			render.JSONUnauthorized(w, respInvalidToken)
			return
		}
		if jwtUser.IsGuest() {
			render.JSONUnauthorized(w, respUserNotLogin)
			return
		}

		ctx := r.Context()
		ctx = context.WithValue(ctx, middleware.KeyUser, jwtUser)
		ctx = context.WithValue(ctx, middleware.KeyAccessUser, accessUser)
		r = r.WithContext(ctx)
		next.ServeHTTP(w, r)
	})
}
