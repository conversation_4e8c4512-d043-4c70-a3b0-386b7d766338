package auth

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/auth"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/stretchr/testify/assert"
	"gopkg.in/dgrijalva/jwt-go.v3"
)

func TestGuestAuth(t *testing.T) {
	var (
		iat            = time.Now()
		exp            = iat.Add(24 * time.Hour)
		standardClaims = jwt.StandardClaims{
			IssuedAt:  iat.Unix(),
			ExpiresAt: exp.Unix(),
			Audience:  "kktv.com",
			Issuer:    "kktv",
			Subject:   "guest:josie",
		}
		generateJWT = func(claims *modelmw.AccessTokenClaims) string {
			jwtAuth := auth.NewJWTAuth(jwt.SigningMethodHS256, []byte(config.JWTAuthSignedString))
			tokenString, _ := jwtAuth.GenerateToken(*claims)
			return tokenString
		}
	)

	var (
		assertJwtUserExists = func(r *http.Request, claims *modelmw.AccessTokenClaims) {
			jwtUser, ok := r.Context().Value(middleware.KeyUser).(model.JwtUser)
			assert.True(t, ok)

			expectedJwtUser := model.JwtUser{
				Iat:            claims.IssuedAt,
				Exp:            claims.ExpiresAt,
				Aud:            claims.Audience,
				Iss:            claims.Issuer,
				Sub:            claims.Subject,
				Role:           claims.Role.String(),
				Type:           claims.Type.String(),
				HasBoughtPrime: claims.HasBoughtPrime,
			}
			assert.Equal(t, expectedJwtUser, jwtUser)
		}
		assertAccessUserExists = func(r *http.Request, claims *modelmw.AccessTokenClaims) {
			accessUser, ok := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
			assert.True(t, ok)
			assert.Equal(t, claims.Subject, accessUser.UserID)
			assert.Len(t, accessUser.Memberships, 0)
		}
	)

	testcases := []struct {
		name       string
		claims     *modelmw.AccessTokenClaims
		thenAssert func(r *http.Request, claims *modelmw.AccessTokenClaims)
	}{
		{
			name: "GIVEN valid token THEN got jwt user and access user",
			claims: &modelmw.AccessTokenClaims{
				StandardClaims: standardClaims,
				Role:           dbuser.RoleGuest,
				Type:           dbuser.TypeGeneral,
				HasBoughtPrime: false,
			},
			thenAssert: func(r *http.Request, claims *modelmw.AccessTokenClaims) {
				assertJwtUserExists(r, claims)
				assertAccessUserExists(r, claims)
			},
		},
		{
			name: "GIVEN no token THEN proceed without jwt user and access user",
			thenAssert: func(r *http.Request, claims *modelmw.AccessTokenClaims) {
				jwtUser, ok := r.Context().Value(middleware.KeyUser).(model.JwtUser)
				assert.False(t, ok)
				assert.Empty(t, jwtUser)

				accessUser, ok := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
				assert.False(t, ok)
				assert.Empty(t, accessUser)
			},
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", "/", nil)
			if err != nil {
				t.Fatal(err)
			}

			tokenString := "invalid_token"
			if tc.claims != nil {
				tokenString = generateJWT(tc.claims)
			}

			req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", tokenString))
			handlerFunc := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				if tc.thenAssert != nil {
					tc.thenAssert(r, tc.claims)
				}
			})

			rr := httptest.NewRecorder()
			GuestAuth(handlerFunc).ServeHTTP(rr, req)
		})
	}
}
