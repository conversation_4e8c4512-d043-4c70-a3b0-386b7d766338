//go:generate mockgen -source repository.go -destination repository_mock.go -package refreshtoken
package refreshtoken

import (
	"database/sql"
	"errors"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/rand"
	"gopkg.in/guregu/null.v3"
)

type Repository interface {
	WithTx(tx database.Tx) Repository
	Create(refreshToken *dbuser.RefreshToken) (*dbuser.RefreshToken, error)
	GetByTokenID(tokenID string) (*dbuser.RefreshToken, error)
}

type repository struct {
	dbReader database.DB
	dbWriter database.DB
	clock    clock.Clock
	random   rand.Rand
}

var (
	refreshTokenRepo     Repository
	onceRefreshTokenRepo sync.Once
)

func NewRepository() Repository {
	onceRefreshTokenRepo.Do(func() {
		refreshTokenRepo = &repository{
			dbReader: container.DBPoolUser().Slave().Unsafe(),
			dbWriter: container.DBPoolUser().Master().Unsafe(),
			clock:    clock.New(),
			random:   rand.New(),
		}
	})
	return refreshTokenRepo
}

func NewRepositoryWithTx(db database.DB) Repository {
	refreshTokenRepo = &repository{
		dbWriter: db,
		dbReader: db,
		clock:    clock.New(),
		random:   rand.New(),
	}
	return refreshTokenRepo
}

func (r *repository) WithTx(tx database.Tx) Repository {
	return &repository{
		dbReader: tx,
		dbWriter: tx,
		clock:    r.clock,
		random:   r.random,
	}
}

func (r *repository) Create(refreshToken *dbuser.RefreshToken) (*dbuser.RefreshToken, error) {
	now := r.clock.Now()
	refreshToken.CreatedAt = null.TimeFrom(now)

	dbFields := database.GetDBFields(refreshToken)
	sql := `INSERT INTO refresh_tokens (` + dbFields.String() + `) VALUES (` + dbFields.NamedString() + `);`
	log.Debug("refresh token repository: create").Str("sql", sql)

	if _, err := r.dbWriter.NamedExec(sql, refreshToken); err != nil {
		return nil, err
	}

	return refreshToken, nil
}

func (r *repository) GetByTokenID(tokenID string) (*dbuser.RefreshToken, error) {
	refreshToken := new(dbuser.RefreshToken)
	if err := r.dbReader.Get(refreshToken,
		`SELECT
			token_id, refresh_token,
			created_at, expired_at
		FROM refresh_tokens
		WHERE token_id = $1`,
		tokenID); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return refreshToken, nil
}
