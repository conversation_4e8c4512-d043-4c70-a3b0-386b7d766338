// Code generated by MockGen. DO NOT EDIT.
// Source: kktvapi/internal/pkg/refreshtoken/repository.go

// Package refreshtoken is a generated GoMock package.
package refreshtoken

import (
	reflect "reflect"

	database "github.com/KKTV/kktv-api-v3/pkg/database"
	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRepository) Create(refreshToken *dbuser.RefreshToken) (*dbuser.RefreshToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", refreshToken)
	ret0, _ := ret[0].(*dbuser.RefreshToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRepositoryMockRecorder) Create(refreshToken interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRepository)(nil).Create), refreshToken)
}

// GetByTokenID mocks base method.
func (m *MockRepository) GetByTokenID(tokenID string) (*dbuser.RefreshToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByTokenID", tokenID)
	ret0, _ := ret[0].(*dbuser.RefreshToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByTokenID indicates an expected call of GetByTokenID.
func (mr *MockRepositoryMockRecorder) GetByTokenID(tokenID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByTokenID", reflect.TypeOf((*MockRepository)(nil).GetByTokenID), tokenID)
}

// WithTx mocks base method.
func (m *MockRepository) WithTx(tx database.Tx) Repository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(Repository)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockRepositoryMockRecorder) WithTx(tx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockRepository)(nil).WithTx), tx)
}
