package ematic

import (
	"github.com/KKTV/createsend-go/ematicagent"

	"github.com/KKTV/kktv-api-v3/pkg/log"
)

var (
	EmaticClient *ematicagent.AgentAPI
	ematicclient = "42fc2a5ded861731"
	ematicapi    = "60W2qMSVYO23Rv4BXEys+cZCTpPRntXvlxcxHQD7EtjwBj3fu9UVhpLiNFJeF2+LGx3XruxQ9pcsdKfObFIyri2Yg60I57iTHZEIyvgXpIr+4lr8fglAdEU1bleci/l8qfQr23sala4/UCULDdeZNQ=="
	ematicListID = "2bd4b4301c729d4cdf5357c8bd285c53"
)

func Init(env string) {
	var err error
	EmaticClient, err = ematicagent.NewAgentAPI(ematicclient, ematicapi)
	if err != nil {
		log.Error("init ematic error").Err(err).Send()
	}
	//if no error EmaticClient is nil
	if EmaticClient == nil {
		log.Error("ematic is nil").Send()
		return
	}
	EmaticClient.SetListID(ematicListID)
	switch env {
	case "test", "dev", "stage":
		EmaticClient.Debug = true
	default:
		EmaticClient.Debug = false
	}
}

func GetClient() *ematicagent.AgentAPI {
	if EmaticClient != nil {
		return EmaticClient
	}
	return nil
}
