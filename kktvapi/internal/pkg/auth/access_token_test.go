package auth

import (
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	mwmodel "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/stretchr/testify/assert"
	"gopkg.in/dgrijalva/jwt-go.v3"
)

func TestGenerate(t *testing.T) {
	at := NewAccessToken()
	now := time.Now()
	exp := now.Add(5 * time.Minute)
	userInfo := &UserInfo{
		ID:             "test-user-id",
		Role:           dbuser.RoleFreeTrial,
		Type:           dbuser.TypeGeneral,
		HasBoughtPrime: true,
		Membership:     dbuser.MembershipFreeTrial,
	}

	token, err := at.Generate(userInfo, now, exp)
	assert.NoError(t, err)
	assert.NotEmpty(t, token)

	parsedToken, err := jwt.ParseWithClaims(token, &mwmodel.AccessTokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(config.JWTAuthSignedString), nil
	})
	assert.NoError(t, err)
	assert.True(t, parsedToken.Valid)

	if claims, ok := parsedToken.Claims.(*mwmodel.AccessTokenClaims); ok {
		assert.NotEmpty(t, claims.Id)
		assert.Equal(t, now.Unix(), claims.IssuedAt)
		assert.Equal(t, exp.Unix(), claims.ExpiresAt)
		assert.Equal(t, userInfo.ID, claims.Subject)
		assert.Equal(t, userInfo.Role, claims.Role)
		assert.Equal(t, userInfo.Type, claims.Type)
		assert.Equal(t, userInfo.HasBoughtPrime, claims.HasBoughtPrime)

		expectedMembership := dbuser.MembershipFreeTrial
		assert.Equal(t, expectedMembership, claims.Memberships)
	} else {
		t.Fail()
	}
}
