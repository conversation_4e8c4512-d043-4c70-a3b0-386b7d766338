//go:generate mockgen -source access_token.go -destination access_token_mock.go -package auth
package auth

import (
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/pkg/auth"
	"github.com/KKTV/kktv-api-v3/pkg/model/authority"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	mwmodel "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/rand"
	"gopkg.in/dgrijalva/jwt-go.v3"
)

type AccessToken interface {
	Generate(userInfo *UserInfo, now time.Time, exp time.Time) (string, error)
	GenerateForApp(userID string, authorities []authority.Authority, appName string, now time.Time, exp time.Time) (string, error)
}

type accessToken struct {
	jwtAuth auth.JWTAuth
	rand    rand.Rand
}

type UserInfo struct {
	ID             string
	Role           dbuser.Role
	Type           dbuser.Type
	HasBoughtPrime bool
	Membership     dbuser.Membership
}

func NewAccessTokenIssuer(signedString string) AccessToken {
	return &accessToken{
		jwtAuth: auth.NewJWTAuth(jwt.SigningMethodHS256, []byte(config.JWTAuthSignedString)),
		rand:    rand.New(),
	}
}

func NewAccessToken() AccessToken {
	return &accessToken{
		jwtAuth: auth.NewJWTAuth(jwt.SigningMethodHS256, []byte(config.JWTAuthSignedString)),
		rand:    rand.New(),
	}
}

func (a *accessToken) Generate(userInfo *UserInfo, now time.Time, exp time.Time) (string, error) {
	claims := mwmodel.AccessTokenClaims{
		StandardClaims: jwt.StandardClaims{
			Id:        a.rand.XidString(),
			IssuedAt:  now.Unix(),
			ExpiresAt: exp.Unix(),
			Audience:  AudienceKKTV,
			Issuer:    IssuerKKTV,
			Subject:   userInfo.ID,
		},
		// TODO use real membership from userInfo when it's ready
		Memberships:    userInfo.Membership,
		Role:           userInfo.Role,
		Type:           userInfo.Type,
		HasBoughtPrime: userInfo.HasBoughtPrime,
	}
	return a.jwtAuth.GenerateToken(claims)
}

func (a *accessToken) GenerateForApp(userID string, authorities []authority.Authority, appName string, now time.Time, exp time.Time) (string, error) {
	claims := mwmodel.AppAccessTokenClaims{
		StandardClaims: jwt.StandardClaims{
			Id:        a.rand.XidString(),
			IssuedAt:  now.Unix(),
			ExpiresAt: exp.Unix(),
			Audience:  appName,
			Issuer:    IssuerKKTV,
			Subject:   userID,
		},
		Authorities: authorities,
	}
	return a.jwtAuth.GenerateToken(claims)
}
