// Code generated by MockGen. DO NOT EDIT.
// Source: access_token.go

// Package auth is a generated GoMock package.
package auth

import (
	reflect "reflect"
	time "time"

	authority "github.com/KKTV/kktv-api-v3/pkg/model/authority"
	gomock "github.com/golang/mock/gomock"
)

// MockAccessToken is a mock of AccessToken interface.
type MockAccessToken struct {
	ctrl     *gomock.Controller
	recorder *MockAccessTokenMockRecorder
}

// MockAccessTokenMockRecorder is the mock recorder for MockAccessToken.
type MockAccessTokenMockRecorder struct {
	mock *MockAccessToken
}

// NewMockAccessToken creates a new mock instance.
func NewMockAccessToken(ctrl *gomock.Controller) *MockAccessToken {
	mock := &MockAccessToken{ctrl: ctrl}
	mock.recorder = &MockAccessTokenMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccessToken) EXPECT() *MockAccessTokenMockRecorder {
	return m.recorder
}

// Generate mocks base method.
func (m *MockAccessToken) Generate(userInfo *UserInfo, now, exp time.Time) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Generate", userInfo, now, exp)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Generate indicates an expected call of Generate.
func (mr *MockAccessTokenMockRecorder) Generate(userInfo, now, exp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Generate", reflect.TypeOf((*MockAccessToken)(nil).Generate), userInfo, now, exp)
}

// GenerateForApp mocks base method.
func (m *MockAccessToken) GenerateForApp(userID string, authorities []authority.Authority, appName string, now, exp time.Time) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateForApp", userID, authorities, appName, now, exp)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateForApp indicates an expected call of GenerateForApp.
func (mr *MockAccessTokenMockRecorder) GenerateForApp(userID, authorities, appName, now, exp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateForApp", reflect.TypeOf((*MockAccessToken)(nil).GenerateForApp), userID, authorities, appName, now, exp)
}
