// Code generated by MockGen. DO NOT EDIT.
// Source: repository.go

// Package productpackage is a generated GoMock package.
package productpackage

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// GetByBillingID mocks base method.
func (m *MockRepository) GetByBillingID(billingID string) (*dbuser.ProductPackage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByBillingID", billingID)
	ret0, _ := ret[0].(*dbuser.ProductPackage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByBillingID indicates an expected call of GetByBillingID.
func (mr *MockRepositoryMockRecorder) GetByBillingID(billingID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByBillingID", reflect.TypeOf((*MockRepository)(nil).GetByBillingID), billingID)
}

// GetByID mocks base method.
func (m *MockRepository) GetByID(id int64) (*dbuser.ProductPackage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", id)
	ret0, _ := ret[0].(*dbuser.ProductPackage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockRepositoryMockRecorder) GetByID(id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockRepository)(nil).GetByID), id)
}

// GetByIDs mocks base method.
func (m *MockRepository) GetByIDs(ids []int) ([]*dbuser.ProductPackage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIDs", ids)
	ret0, _ := ret[0].([]*dbuser.ProductPackage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIDs indicates an expected call of GetByIDs.
func (mr *MockRepositoryMockRecorder) GetByIDs(ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIDs", reflect.TypeOf((*MockRepository)(nil).GetByIDs), ids)
}

// GetByProductID mocks base method.
func (m *MockRepository) GetByProductID(productID int64) (*dbuser.ProductPackage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByProductID", productID)
	ret0, _ := ret[0].(*dbuser.ProductPackage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByProductID indicates an expected call of GetByProductID.
func (mr *MockRepositoryMockRecorder) GetByProductID(productID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByProductID", reflect.TypeOf((*MockRepository)(nil).GetByProductID), productID)
}
