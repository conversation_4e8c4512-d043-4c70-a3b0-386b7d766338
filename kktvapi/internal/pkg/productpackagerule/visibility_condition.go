package productpackagerule

import (
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

type VisibilityCondition struct {
	Package          *dbuser.ProductPackage
	RulesWithTargets []RulesWithTarget

	SatisfiedTarget *dbuser.PackageTarget
}

type RulesWithTarget struct {
	Rule   *AndRule
	Target *dbuser.PackageTarget
}

func NewVisibilityCondition(pkg *dbuser.ProductPackage, userTargetIdentities []dbuser.PackageTargetIdentity, userLatestPackageID int64) *VisibilityCondition {
	RulesWithTargets := BuildRulesFromPackage(pkg, userTargetIdentities, int64(userLatestPackageID))
	return &VisibilityCondition{
		Package:          pkg,
		RulesWithTargets: RulesWithTargets,
	}
}

func BuildRulesFromPackage(pkg *dbuser.ProductPackage, userTargetIdentities []dbuser.PackageTargetIdentity, userLatestPackageID int64) []RulesWithTarget {
	RulesWithTargets := []RulesWithTarget{}

	if pkg.Targets == nil {
		return RulesWithTargets
	}
	for _, target := range *pkg.Targets {
		var requiredTargetIdentities []dbuser.PackageTargetIdentity
		for _, idStr := range target.Condition.Identities {
			requiredTargetIdentities = append(requiredTargetIdentities, dbuser.PackageTargetIdentity(idStr))
		}
		identityRule := NewIdentityRule(requiredTargetIdentities, userTargetIdentities)

		andRule := NewAndRule(identityRule)

		if target.Condition.LatestPackages != nil {
			operator := target.Condition.LatestPackages.Operator
			if operator == "exclude" {
				ExcludeRule := NewExcludeRule(target.Condition.LatestPackages.IDs, userLatestPackageID)
				andRule.Add(ExcludeRule)
			} else if operator == "include" {
				IncludeRule := NewIncludeRule(target.Condition.LatestPackages.IDs, userLatestPackageID)
				andRule.Add(IncludeRule)
			}
		}

		RulesWithTargets = append(RulesWithTargets, RulesWithTarget{
			Rule:   andRule,
			Target: &target,
		})
	}

	return RulesWithTargets
}

func (vc *VisibilityCondition) IsSatisfied() bool {
	// no rules represents that the package is visible to all users
	if len(vc.RulesWithTargets) == 0 {
		return true
	}

	for _, ruleWithTarget := range vc.RulesWithTargets {
		if ruleWithTarget.Rule.IsSatisfied() {
			vc.SatisfiedTarget = ruleWithTarget.Target
			return true
		}
	}
	return false
}
