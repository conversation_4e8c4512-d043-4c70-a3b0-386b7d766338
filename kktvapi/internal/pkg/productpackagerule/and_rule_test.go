package productpackagerule

import "testing"

func TestAndRule_IsSatisfied(t *testing.T) {
	tests := []struct {
		name  string
		rules []Rule
		want  bool
	}{
		{
			name: "Given all rules are satisfied, should return true",
			rules: []Rule{
				NewIncludeRule([]string{"1", "2", "3"}, "1"),
				NewIncludeRule([]string{"1", "2", "3"}, "2"),
			},
			want: true,
		},
		{
			name: "Given any rule is not satisfied, should return false",
			rules: []Rule{
				NewIncludeRule([]string{"1", "2", "3"}, "1"),
				NewIncludeRule([]string{"1", "2", "3"}, "4"),
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ar := NewAndRule(tt.rules...)
			if got := ar.IsSatisfied(); got != tt.want {
				t.<PERSON>rf("AndRule.IsSatisfied() = %v, want %v", got, tt.want)
			}
		})
	}
}
