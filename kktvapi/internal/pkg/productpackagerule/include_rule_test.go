package productpackagerule

import "testing"

func TestIncludeRule_IsSatisfied(t *testing.T) {
	tests := []struct {
		name           string
		includedValues []string
		currentValue   string
		want           bool
	}{
		{
			name:           "Given empty included values, should return false",
			includedValues: []string{},
			currentValue:   "1",
			want:           false,
		},
		{
			name:           "Given current value not in included values, should return false",
			includedValues: []string{"1", "2", "3"},
			currentValue:   "4",
			want:           false,
		},
		{
			name:           "Given current value in included values, should return true",
			includedValues: []string{"1", "2", "3"},
			currentValue:   "2",
			want:           true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ir := NewIncludeRule(tt.includedValues, tt.currentValue)
			if got := ir.IsSatisfied(); got != tt.want {
				t.Errorf("IncludeRule.IsSatisfied() = %v, want %v", got, tt.want)
			}
		})
	}
}
