package productpackagerule

import "testing"

func TestExcludeRule_IsSatisfied(t *testing.T) {
	tests := []struct {
		name           string
		excludedValues []string
		currentValue   string
		want           bool
	}{
		{
			name:           "Given empty excluded values, should return true",
			excludedValues: []string{},
			currentValue:   "1",
			want:           true,
		},
		{
			name:           "Given current value not in excluded values, should return true",
			excludedValues: []string{"1", "2", "3"},
			currentValue:   "4",
			want:           true,
		},
		{
			name:           "Given current value in excluded values, should return false",
			excludedValues: []string{"1", "2", "3"},
			currentValue:   "2",
			want:           false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			er := NewExcludeRule(tt.excludedValues, tt.currentValue)
			if got := er.IsSatisfied(); got != tt.want {
				t.Errorf("ExcludeRule.IsSatisfied() = %v, want %v", got, tt.want)
			}
		})
	}
}
