package productpackagerule

import (
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/duke-git/lancet/v2/slice"
)

type IdentityRule struct {
	RequiredIdentities []dbuser.PackageTargetIdentity
	UserIdentities     []dbuser.PackageTargetIdentity
}

func NewIdentityRule(requiredIdentities []dbuser.PackageTargetIdentity, userIdentities []dbuser.PackageTargetIdentity) *IdentityRule {
	return &IdentityRule{
		RequiredIdentities: requiredIdentities,
		UserIdentities:     userIdentities,
	}
}

func (ir *IdentityRule) IsSatisfied() bool {
	if len(ir.RequiredIdentities) == 0 {
		return true
	}

	if intersected := slice.Intersection(ir.RequiredIdentities, ir.UserIdentities); len(intersected) > 0 {
		return true
	}
	return false
}
