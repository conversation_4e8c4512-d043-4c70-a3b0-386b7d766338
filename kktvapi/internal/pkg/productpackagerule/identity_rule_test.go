package productpackagerule

import (
	"testing"

	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/stretchr/testify/assert"
)

func TestIdentityRule_IsSatisfied(t *testing.T) {
	tests := []struct {
		name string
		rule IdentityRule
		want bool
	}{
		{
			name: `Given user has IdentityExpiredPaid, should be satisfied by rule requiring IdentityExpiredPaid`,
			rule: IdentityRule{
				RequiredIdentities: []dbuser.PackageTargetIdentity{dbuser.PackageTargetIdentityPrime, dbuser.PackageTargetIdentityExpiredPurchased},
				UserIdentities:     []dbuser.PackageTargetIdentity{dbuser.PackageTargetIdentityExpiredPurchased},
			},
			want: true,
		},
		{
			name: `Given user has IdentityExpiredPaid, should be satisfied by rule requiring IdentityPrime or IdentityExpiredPaid`,
			rule: IdentityRule{
				RequiredIdentities: []dbuser.PackageTargetIdentity{dbuser.PackageTargetIdentityPrime, dbuser.PackageTargetIdentityExpiredPurchased},
				UserIdentities:     []dbuser.PackageTargetIdentity{dbuser.PackageTargetIdentityExpiredPurchased, dbuser.PackageTargetIdentityFreeTrial},
			},
			want: true,
		},
		{
			name: `Given user has IdentityPrime, should not be satisfied by rule requiring IdentityExpiredPaid`,
			rule: IdentityRule{
				RequiredIdentities: []dbuser.PackageTargetIdentity{dbuser.PackageTargetIdentityExpiredPurchased},
				UserIdentities:     []dbuser.PackageTargetIdentity{dbuser.PackageTargetIdentityPrime},
			},
			want: false,
		},
		{
			name: `Given user has IdentityVip, should not be satisfied by rule requiring IdentityExpiredPaid`,
			rule: IdentityRule{
				RequiredIdentities: []dbuser.PackageTargetIdentity{dbuser.PackageTargetIdentityExpiredPurchased},
				UserIdentities:     []dbuser.PackageTargetIdentity{dbuser.PackageTargetIdentityVip},
			},
			want: false,
		},
		{
			name: `Given user has IdentityGuest, should be satisfied by rule requiring IdentityGuest`,
			rule: IdentityRule{
				RequiredIdentities: []dbuser.PackageTargetIdentity{dbuser.PackageTargetIdentityGuest},
				UserIdentities:     []dbuser.PackageTargetIdentity{dbuser.PackageTargetIdentityGuest},
			},
			want: true,
		},
		{
			name: `Given empty required identities (old package), should always be visible`,
			rule: IdentityRule{
				RequiredIdentities: []dbuser.PackageTargetIdentity{},
				UserIdentities:     []dbuser.PackageTargetIdentity{dbuser.PackageTargetIdentityPrime},
			},
			want: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.rule.IsSatisfied()
			assert.Equal(t, tt.want, got)
		})
	}
}
