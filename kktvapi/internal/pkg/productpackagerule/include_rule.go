package productpackagerule

type IncludeRule[T comparable] struct {
	IncludedValues []T
	CurrentValue   T
}

func (ir *IncludeRule[T]) IsSatisfied() bool {
	if len(ir.IncludedValues) == 0 {
		return false
	}

	for _, includedValue := range ir.IncludedValues {
		if includedValue == ir.CurrentValue {
			return true
		}
	}

	return false
}

func NewIncludeRule[T comparable](includedValues []T, currentValue T) *IncludeRule[T] {
	return &IncludeRule[T]{
		IncludedValues: includedValues,
		CurrentValue:   currentValue,
	}
}
