package redeem

import (
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbredeem"
)

var (
	couponCodeRepo     CouponCodeRepository
	onceCouponCodeRepo sync.Once
)

type CouponCodeRepository interface {
	GetByCode(code string) (*dbredeem.CouponCode, error)
	CountByGroupAndUser(groupID, uid string) (int64, error)
	Update(code *dbredeem.CouponCode) error
}

type couponCodeRepository struct {
	dbReader database.DB
	dbWriter database.DB
}

func NewCouponCodeRepository() CouponCodeRepository {
	onceCouponCodeRepo.Do(func() {
		couponCodeRepo = &couponCodeRepository{
			dbReader: container.DBPoolRedeem().Slave(),
			dbWriter: container.DBPoolRedeem().Master(),
		}
	})
	return couponCodeRepo
}

func (r *couponCodeRepository) GetByCode(code string) (*dbredeem.CouponCode, error) {
	c := new(dbredeem.CouponCode)
	if err := r.dbReader.Get(c,
		`SELECT
			c.id, c.code, c.group_id, c.user_id, c.price, c.price_no_tax,
			c.issue_user_id, device_id, c.created_at, c.updated_at, c.revoked_at
		FROM coupon_codes c
		WHERE c.code = $1`,
		code); err != nil {
		return nil, err
	}
	return c, nil
}

// CountByGroupAndUser count coupon_codes of user and coupon_group for this redeem code
func (r *couponCodeRepository) CountByGroupAndUser(groupID, uid string) (int64, error) {
	var count int64
	err := r.dbReader.Get(&count,
		`SELECT COUNT(1) AS used_count
			FROM coupon_codes
			WHERE group_id = $1
			AND user_id = $2`,
		groupID, uid)
	return count, err
}

func (r *couponCodeRepository) Update(code *dbredeem.CouponCode) error {
	_, err := r.dbWriter.NamedExec(
		`UPDATE coupon_codes
		SET
			user_id = :user_id,
			updated_at = NOW()
		WHERE code = :code`,
		code)
	return err
}
