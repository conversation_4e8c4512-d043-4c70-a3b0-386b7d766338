// Code generated by MockGen. DO NOT EDIT.
// Source: coupon_group_repository.go

// Package redeem is a generated GoMock package.
package redeem

import (
	reflect "reflect"

	dbredeem "github.com/KKTV/kktv-api-v3/pkg/model/dbredeem"
	gomock "github.com/golang/mock/gomock"
)

// MockCouponGroupRepository is a mock of CouponGroupRepository interface.
type MockCouponGroupRepository struct {
	ctrl     *gomock.Controller
	recorder *MockCouponGroupRepositoryMockRecorder
}

// MockCouponGroupRepositoryMockRecorder is the mock recorder for MockCouponGroupRepository.
type MockCouponGroupRepositoryMockRecorder struct {
	mock *MockCouponGroupRepository
}

// NewMockCouponGroupRepository creates a new mock instance.
func NewMockCouponGroupRepository(ctrl *gomock.Controller) *MockCouponGroupRepository {
	mock := &MockCouponGroupRepository{ctrl: ctrl}
	mock.recorder = &MockCouponGroupRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCouponGroupRepository) EXPECT() *MockCouponGroupRepositoryMockRecorder {
	return m.recorder
}

// CountByCampaignGroupAndUser mocks base method.
func (m *MockCouponGroupRepository) CountByCampaignGroupAndUser(campaingGroup, uid string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountByCampaignGroupAndUser", campaingGroup, uid)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountByCampaignGroupAndUser indicates an expected call of CountByCampaignGroupAndUser.
func (mr *MockCouponGroupRepositoryMockRecorder) CountByCampaignGroupAndUser(campaingGroup, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountByCampaignGroupAndUser", reflect.TypeOf((*MockCouponGroupRepository)(nil).CountByCampaignGroupAndUser), campaingGroup, uid)
}

// GetByCode mocks base method.
func (m *MockCouponGroupRepository) GetByCode(code string) (*dbredeem.CouponGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCode", code)
	ret0, _ := ret[0].(*dbredeem.CouponGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCode indicates an expected call of GetByCode.
func (mr *MockCouponGroupRepositoryMockRecorder) GetByCode(code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCode", reflect.TypeOf((*MockCouponGroupRepository)(nil).GetByCode), code)
}
