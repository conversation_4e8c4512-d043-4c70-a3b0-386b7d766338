// Code generated by MockGen. DO NOT EDIT.
// Source: redemption_log_repository.go

// Package redeem is a generated GoMock package.
package redeem

import (
	reflect "reflect"

	dbredeem "github.com/KKTV/kktv-api-v3/pkg/model/dbredeem"
	gomock "github.com/golang/mock/gomock"
)

// MockRedemptionLogRepository is a mock of RedemptionLogRepository interface.
type MockRedemptionLogRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRedemptionLogRepositoryMockRecorder
}

// MockRedemptionLogRepositoryMockRecorder is the mock recorder for MockRedemptionLogRepository.
type MockRedemptionLogRepositoryMockRecorder struct {
	mock *MockRedemptionLogRepository
}

// NewMockRedemptionLogRepository creates a new mock instance.
func NewMockRedemptionLogRepository(ctrl *gomock.Controller) *MockRedemptionLogRepository {
	mock := &MockRedemptionLogRepository{ctrl: ctrl}
	mock.recorder = &MockRedemptionLogRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRedemptionLogRepository) EXPECT() *MockRedemptionLogRepositoryMockRecorder {
	return m.recorder
}

// CountByCodeAndUser mocks base method.
func (m *MockRedemptionLogRepository) CountByCodeAndUser(code, uid string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountByCodeAndUser", code, uid)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountByCodeAndUser indicates an expected call of CountByCodeAndUser.
func (mr *MockRedemptionLogRepositoryMockRecorder) CountByCodeAndUser(code, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountByCodeAndUser", reflect.TypeOf((*MockRedemptionLogRepository)(nil).CountByCodeAndUser), code, uid)
}

// Insert mocks base method.
func (m *MockRedemptionLogRepository) Insert(redemptionLog *dbredeem.RedemptionLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", redemptionLog)
	ret0, _ := ret[0].(error)
	return ret0
}

// Insert indicates an expected call of Insert.
func (mr *MockRedemptionLogRepositoryMockRecorder) Insert(redemptionLog interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockRedemptionLogRepository)(nil).Insert), redemptionLog)
}
