package redeem

import (
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbredeem"
)

var (
	couponGroupRepo     CouponGroupRepository
	onceCouponGroupRepo sync.Once
)

type CouponGroupRepository interface {
	GetByCode(code string) (*dbredeem.CouponGroup, error)
	CountByCampaignGroupAndUser(campaingGroup, uid string) (int64, error)
}

type couponGroupRepository struct {
	dbReader database.DB
}

func NewCouponGroupRepository() CouponGroupRepository {
	onceCouponGroupRepo.Do(func() {
		couponGroupRepo = &couponGroupRepository{
			dbReader: container.DBPoolRedeem().Slave(),
		}
	})
	return couponGroupRepo
}

func (r *couponGroupRepository) GetByCode(code string) (*dbredeem.CouponGroup, error) {
	g := new(dbredeem.CouponGroup)
	if err := r.dbReader.Get(g,
		`SELECT
			g.id, g.prefix, g.price, g.duration,
			g.usage_limit_per_user, g.allow_reuse, g.valid_since,
			g.expires_at, g.price_no_tax, g.description,
			g.free_duration, g.fee, g.channel, g.product_id,
			g.campaign_group
		FROM coupon_codes c
		JOIN coupon_groups g
		ON c.group_id = g.id
		WHERE c.code = $1`,
		code); err != nil {
		return nil, err
	}
	return g, nil
}

func (r *couponGroupRepository) CountByCampaignGroupAndUser(campaingGroup, uid string) (int64, error) {
	var count int64
	err := r.dbReader.Get(&count,
		`SELECT count(id)
			FROM coupon_groups cg
			WHERE id IN (
				SELECT group_id
				FROM coupon_codes cc
				WHERE user_id = $1
			) AND campaign_group = $2;`, uid, campaingGroup)
	return count, err
}
