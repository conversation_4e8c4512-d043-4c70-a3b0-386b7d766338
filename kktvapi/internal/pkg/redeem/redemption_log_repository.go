package redeem

import (
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbredeem"
)

var (
	redemptionLogRepo     RedemptionLogRepository
	onceRedemptionLogRepo sync.Once
)

type RedemptionLogRepository interface {
	Insert(redemptionLog *dbredeem.RedemptionLog) (err error)
	CountByCodeAndUser(code, uid string) (int64, error)
}

type redemptionLogRepository struct {
	dbReader database.DB
	dbWriter database.DB
}

func NewRedemptionLogRepository() RedemptionLogRepository {
	onceRedemptionLogRepo.Do(func() {
		redemptionLogRepo = &redemptionLogRepository{
			dbReader: container.DBPoolRedeem().Slave(),
			dbWriter: container.DBPoolRedeem().Master(),
		}
	})
	return redemptionLogRepo
}

func (r *redemptionLogRepository) Insert(redemptionLog *dbredeem.RedemptionLog) error {
	_, err := r.dbWriter.NamedExec(
		`INSERT INTO redemption_logs (
					user_id,
					code
				) VALUES (
					:user_id, :code
				)`,
		redemptionLog)
	return err
}

// CountByCodeAndUser count redemtion_logs of code and user for "reuse-code"
func (r *redemptionLogRepository) CountByCodeAndUser(code, uid string) (int64, error) {
	var count int64
	err := r.dbReader.Get(&count,
		`SELECT
			COUNT(1) AS redemption_count
		FROM redemption_logs
		WHERE code = $1
		AND user_id = $2`,
		code, uid)
	return count, err
}
