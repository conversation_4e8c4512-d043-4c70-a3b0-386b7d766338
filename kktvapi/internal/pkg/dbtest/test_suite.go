package dbtest

import (
	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/suite"
)

type Suite struct {
	suite.Suite

	db *sqlx.DB
	tx *sqlx.Tx
}

func (suite *Suite) SetupTest() {
	// new a transaction for each test case
	tx, err := suite.db.Beginx()
	if err != nil {
		suite.Fail("fail to begin transaction", err)
	}
	suite.tx = tx
}

func (suite *Suite) TearDownTest() {
	// rollback the transaction after each test
	if suite.tx != nil {
		defer func() {
			if err := suite.tx.Rollback(); err != nil {
				suite.Fail("fail to rollback", err)
			}
		}()
	}
}

func (suite *Suite) SetupSuite(dbDSN string) {
	t := suite.T()
	if dbDSN == "" {
		t.Fatal("cannot get db DSN")
	}
	db := datastore.NewDBPool([]string{dbDSN})
	if db == nil {
		t.Fatalf("fail to build DB connection for %s", dbDSN)
	}
	suite.db = db.Master().Unsafe()
}

func (suite *Suite) GetTransaction() database.Tx {
	return suite.tx
}

// DeleteFromTable cleans the record in given tables
func (suite *Suite) DeleteFromTable(tables ...string) {
	for _, table := range tables {
		if _, err := suite.tx.Exec("DELETE FROM " + table); err != nil {
			suite.Fail("fail to delete tables", err)
		}
	}
}
