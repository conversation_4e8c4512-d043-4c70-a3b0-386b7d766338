//go:generate mockgen -source repository.go -destination repository_mock.go -package announcement
package announcement

import (
	"errors"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
)

var (
	repo     Repository
	onceRepo sync.Once
)

type Repository interface {
	ListAvailable(timing time.Time) ([]model.AnnounceItem, error)
}

type repository struct {
	cacheReader cache.Cacher
}

func NewRepository() Repository {
	onceRepo.Do(func() {
		repo = &repository{
			cacheReader: cache.New(container.CachePoolMeta().Slave()),
		}
	})
	return repo
}

type announceObj struct {
	Announcements []model.AnnounceItem `json:"announcements"`
}

func (t *repository) ListAvailable(timing time.Time) ([]model.AnnounceItem, error) {
	var obj announceObj
	err := t.cacheReader.Get(key.GetSystemAnnouncements(), &obj)
	if errors.Is(err, cache.ErrCacheMiss) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	item := make([]model.AnnounceItem, 0)
	for _, e := range obj.Announcements {
		targetTime := timing.Unix()
		if !(targetTime >= e.StartTime && targetTime <= e.EndTime) {
			continue
		}
		item = append(item, e)
	}
	return item, nil
}
