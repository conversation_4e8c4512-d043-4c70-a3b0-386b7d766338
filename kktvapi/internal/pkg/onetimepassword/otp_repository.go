//go:generate mockgen -source otp_repository.go -destination otp_repository_mock.go -package onetimepassword
package onetimepassword

import (
	"errors"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/cacheuser"
	"github.com/rs/zerolog"
)

var (
	otpRepo     OTPRepository
	onceOTPRepo sync.Once
)

type OTPRepository interface {
	Get(userID string) (*cacheuser.AccountOTP, error)
	Set(userID string, accountOTP cacheuser.AccountOTP, ttl time.Duration) error
	DeleteByUserID(userID string) (err error)
	SetTimes(userID string, ttl time.Duration) error
	GetTimes(userID string) (int, error)
	IncrementTimes(userID string) error
}

type otpRepository struct {
	cacheReader cache.Cacher
	cacheWriter cache.Cacher
	logger      zerolog.Logger
}

func NewOTPRepository() OTPRepository {
	onceOTPRepo.Do(func() {
		pool := container.CachePoolUser()
		otpRepo = &otpRepository{
			cacheReader: cache.New(pool.Slave()),
			cacheWriter: cache.New(pool.Master()),
			logger:      *log.Get(),
		}
	})
	return otpRepo
}

func NewRepositoryWith(cacheReader cache.Cacher, cacheWriter cache.Cacher, logger zerolog.Logger) OTPRepository {
	return &otpRepository{
		cacheReader: cacheReader,
		cacheWriter: cacheWriter,
		logger:      logger,
	}
}

func (repo *otpRepository) Get(userID string) (*cacheuser.AccountOTP, error) {
	key := key.AccountOTP(userID)
	var accountOTP cacheuser.AccountOTP
	if err := repo.cacheReader.Get(key, &accountOTP); errors.Is(err, cache.ErrCacheMiss) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	return &accountOTP, nil
}

func (repo *otpRepository) Set(userID string, accountOTP cacheuser.AccountOTP, ttl time.Duration) error {
	key := key.AccountOTP(userID)

	if err := repo.cacheWriter.Set(key, accountOTP, ttl); err != nil {
		repo.logger.Error().Err(err).Send()
		return err
	}

	return nil
}

func (repo *otpRepository) SetTimes(userID string, ttl time.Duration) error {
	key := key.AccountOTPTimes(userID)

	if err := repo.cacheWriter.Incr(key); err != nil {
		return err
	}

	if err := repo.cacheWriter.SetTTL(key, ttl); err != nil {
		return err
	}

	return nil
}

func (repo *otpRepository) IncrementTimes(userID string) error {
	key := key.AccountOTPTimes(userID)

	if err := repo.cacheWriter.Incr(key); err != nil {
		return err
	}

	return nil
}

func (repo *otpRepository) GetTimes(userID string) (int, error) {
	key := key.AccountOTPTimes(userID)
	var times int
	if err := repo.cacheReader.Get(key, &times); errors.Is(err, cache.ErrCacheMiss) {
		return 0, nil
	} else if err != nil {
		return 0, err
	}

	return times, nil
}

func (repo *otpRepository) DeleteByUserID(userID string) (err error) {
	k := key.AccountOTP(userID)
	if err = repo.cacheWriter.SetTTL(k, time.Duration(0)); err != nil {
		return
	}
	return
}
