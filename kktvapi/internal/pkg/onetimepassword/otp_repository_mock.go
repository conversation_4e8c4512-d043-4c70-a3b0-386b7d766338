// Code generated by MockGen. DO NOT EDIT.
// Source: otp_repository.go

// Package onetimepassword is a generated GoMock package.
package onetimepassword

import (
	reflect "reflect"
	time "time"

	cacheuser "github.com/KKTV/kktv-api-v3/pkg/model/cacheuser"
	gomock "github.com/golang/mock/gomock"
)

// MockOTPRepository is a mock of OTPRepository interface.
type MockOTPRepository struct {
	ctrl     *gomock.Controller
	recorder *MockOTPRepositoryMockRecorder
}

// MockOTPRepositoryMockRecorder is the mock recorder for MockOTPRepository.
type MockOTPRepositoryMockRecorder struct {
	mock *MockOTPRepository
}

// NewMockOTPRepository creates a new mock instance.
func NewMockOTPRepository(ctrl *gomock.Controller) *MockOTPRepository {
	mock := &MockOTPRepository{ctrl: ctrl}
	mock.recorder = &MockOTPRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOTPRepository) EXPECT() *MockOTPRepositoryMockRecorder {
	return m.recorder
}

// DeleteByUserID mocks base method.
func (m *MockOTPRepository) DeleteByUserID(userID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByUserID", userID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByUserID indicates an expected call of DeleteByUserID.
func (mr *MockOTPRepositoryMockRecorder) DeleteByUserID(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByUserID", reflect.TypeOf((*MockOTPRepository)(nil).DeleteByUserID), userID)
}

// Get mocks base method.
func (m *MockOTPRepository) Get(userID string) (*cacheuser.AccountOTP, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", userID)
	ret0, _ := ret[0].(*cacheuser.AccountOTP)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockOTPRepositoryMockRecorder) Get(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockOTPRepository)(nil).Get), userID)
}

// GetTimes mocks base method.
func (m *MockOTPRepository) GetTimes(userID string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTimes", userID)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTimes indicates an expected call of GetTimes.
func (mr *MockOTPRepositoryMockRecorder) GetTimes(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTimes", reflect.TypeOf((*MockOTPRepository)(nil).GetTimes), userID)
}

// IncrementTimes mocks base method.
func (m *MockOTPRepository) IncrementTimes(userID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrementTimes", userID)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrementTimes indicates an expected call of IncrementTimes.
func (mr *MockOTPRepositoryMockRecorder) IncrementTimes(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrementTimes", reflect.TypeOf((*MockOTPRepository)(nil).IncrementTimes), userID)
}

// Set mocks base method.
func (m *MockOTPRepository) Set(userID string, accountOTP cacheuser.AccountOTP, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Set", userID, accountOTP, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// Set indicates an expected call of Set.
func (mr *MockOTPRepositoryMockRecorder) Set(userID, accountOTP, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Set", reflect.TypeOf((*MockOTPRepository)(nil).Set), userID, accountOTP, ttl)
}

// SetTimes mocks base method.
func (m *MockOTPRepository) SetTimes(userID string, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetTimes", userID, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetTimes indicates an expected call of SetTimes.
func (mr *MockOTPRepositoryMockRecorder) SetTimes(userID, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTimes", reflect.TypeOf((*MockOTPRepository)(nil).SetTimes), userID, ttl)
}
