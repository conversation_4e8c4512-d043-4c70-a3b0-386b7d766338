//go:generate mockgen -source authorization_repository.go -destination authorization_repository_mock.go -package oauth
package oauth

import (
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/database"
)

var (
	authorizationRepo     AuthorizationRepository
	onceAuthorizationRepo sync.Once
)

type AuthorizationRepository interface {
	CreateAuthorization(appID, userID, scope, redirectURI string) error
	GetUserIDsByAppID(appID string) ([]string, error)
}

func NewAuthorizationRepository() AuthorizationRepository {
	onceAuthorizationRepo.Do(func() {
		authorizationRepo = &authorizationRepository{
			userDBWriter: &database.Conn{DB: container.DBPoolUser().Master().Unsafe()},
		}
	})
	return authorizationRepo
}

func NewAuthorizationRepositoryWithDB(db database.TxBeginner) AuthorizationRepository {
	return &authorizationRepository{userDBWriter: db}
}

type authorizationRepository struct {
	userDBWriter database.TxBeginner
}

func (r *authorizationRepository) CreateAuthorization(appID, userID, scope, redirectURI string) error {
	txBeginner := r.userDBWriter

	tx, err := txBeginner.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// First, revoke any existing active authorizations for the same user and app
	_, err = tx.Exec(
		`UPDATE oauth_authorizations 
		SET revoked_at = NOW() 
		WHERE app_id = $1 AND user_id = $2 AND revoked_at IS NULL`,
		appID, userID,
	)
	if err != nil {
		return err
	}

	// Then, insert the new authorization
	_, err = tx.Exec(
		`INSERT INTO oauth_authorizations (app_id, user_id, scope, redirect_uri) 
		VALUES ($1, $2, $3, $4)`,
		appID, userID, scope, redirectURI,
	)
	if err != nil {
		return err
	}

	return tx.Commit()
}

func (r *authorizationRepository) GetUserIDsByAppID(appID string) ([]string, error) {
	rows, err := r.userDBWriter.Query(`SELECT user_id FROM oauth_authorizations WHERE app_id = $1 AND revoked_at IS NULL`, appID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var userIDs []string
	for rows.Next() {
		var userID string
		if err := rows.Scan(&userID); err == nil {
			userIDs = append(userIDs, userID)
		}
	}
	return userIDs, nil
}
