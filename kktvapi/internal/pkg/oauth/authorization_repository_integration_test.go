package oauth

import (
	_ "embed"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/testutils"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/suite"
)

type AuthorizationRepositoryTestSuite struct {
	suite.Suite
	testContainer *testutils.Container
	repo          AuthorizationRepository
	db            *sqlx.DB
}

func TestAuthorizationRepositoryIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(AuthorizationRepositoryTestSuite))
}

func (suite *AuthorizationRepositoryTestSuite) SetupSuite() {
	testContainer, dbPool := testutils.SetupTestDatabase(testutils.DBUser)
	suite.testContainer = testContainer
	suite.db = dbPool.Master().Unsafe()
}

func (suite *AuthorizationRepositoryTestSuite) TearDownSuite() {
	if err := suite.testContainer.Close(); err != nil {
		suite.Fail("fail to terminate test container", err)
	}
}

func (suite *AuthorizationRepositoryTestSuite) SetupTest() {
	suite.repo = &authorizationRepository{
		userDBWriter: &database.Conn{DB: suite.db},
	}

	suite.SetupOAuthAuthorizationsTable()
	suite.givenCleanOAuthAuthorizationsTable()
}

func (suite *AuthorizationRepositoryTestSuite) GetTransaction() database.DB {
	return suite.db
}

func (suite *AuthorizationRepositoryTestSuite) TestCreateAuthorization() {
	const (
		appID       = "test-app"
		userID      = "test-user"
		scope       = "user-info:get"
		redirectURI = "https://example.com/callback"
	)

	testcases := []struct {
		name   string
		given  func()
		assert func()
	}{
		{
			name: "WHEN no previous authorizations exist THEN create a new active authorization",
			given: func() {
				// No setup needed
			},
			assert: func() {
				// Verify that one active authorization exists
				var count int
				err := suite.db.Get(&count, `
					SELECT COUNT(*) FROM oauth_authorizations 
					WHERE app_id = $1 AND user_id = $2 AND scope = $3 AND redirect_uri = $4 AND revoked_at IS NULL`,
					appID, userID, scope, redirectURI,
				)
				suite.NoError(err, "Failed to count active authorizations")
				suite.Equal(1, count, "Expected exactly one active authorization")
			},
		},
		{
			name: "WHEN one previous authorization exists THEN revoke it and create a new active one",
			given: func() {
				// Insert a previous authorization
				_, err := suite.db.Exec(`
					INSERT INTO oauth_authorizations (app_id, user_id, scope, redirect_uri, authorized_at)
					VALUES ($1, $2, $3, $4, $5)`,
					appID, userID, scope, redirectURI, time.Now().Add(-1*time.Hour),
				)
				suite.NoError(err, "Failed to insert previous authorization")
			},
			assert: func() {
				// Verify that the previous authorization is revoked
				var revokedCount int
				err := suite.db.Get(&revokedCount, `
					SELECT COUNT(*) FROM oauth_authorizations 
					WHERE app_id = $1 AND user_id = $2 AND revoked_at IS NOT NULL`,
					appID, userID,
				)
				suite.NoError(err, "Failed to count revoked authorizations")
				suite.Equal(1, revokedCount, "Expected exactly one revoked authorization")

				// Verify that one active authorization exists
				var activeCount int
				err = suite.db.Get(&activeCount, `
					SELECT COUNT(*) FROM oauth_authorizations 
					WHERE app_id = $1 AND user_id = $2 AND revoked_at IS NULL`,
					appID, userID,
				)
				suite.NoError(err, "Failed to count active authorizations")
				suite.Equal(1, activeCount, "Expected exactly one active authorization")
			},
		},
		{
			name: "WHEN multiple previous authorizations exist THEN revoke all of them and create a new active one",
			given: func() {
				// Insert multiple previous authorizations
				for i := 0; i < 3; i++ {
					_, err := suite.db.Exec(`
						INSERT INTO oauth_authorizations (app_id, user_id, scope, redirect_uri, authorized_at)
						VALUES ($1, $2, $3, $4, $5)`,
						appID, userID, scope, redirectURI, time.Now().Add(-1*time.Hour*time.Duration(i+1)),
					)
					suite.NoError(err, "Failed to insert previous authorization")
				}
			},
			assert: func() {
				// Verify that all previous authorizations are revoked
				var revokedCount int
				err := suite.db.Get(&revokedCount, `
					SELECT COUNT(*) FROM oauth_authorizations 
					WHERE app_id = $1 AND user_id = $2 AND revoked_at IS NOT NULL`,
					appID, userID,
				)
				suite.NoError(err, "Failed to count revoked authorizations")
				suite.Equal(3, revokedCount, "Expected exactly three revoked authorizations")

				// Verify that one active authorization exists
				var activeCount int
				err = suite.db.Get(&activeCount, `
					SELECT COUNT(*) FROM oauth_authorizations 
					WHERE app_id = $1 AND user_id = $2 AND revoked_at IS NULL`,
					appID, userID,
				)
				suite.NoError(err, "Failed to count active authorizations")
				suite.Equal(1, activeCount, "Expected exactly one active authorization")
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			suite.givenCleanOAuthAuthorizationsTable()
			tc.given()

			err := suite.repo.CreateAuthorization(appID, userID, scope, redirectURI)
			suite.NoError(err)

			tc.assert()
		})
	}
}

func (suite *AuthorizationRepositoryTestSuite) SetupOAuthAuthorizationsTable() {
	_, err := suite.db.Exec(`
		CREATE TABLE IF NOT EXISTS oauth_authorizations (
			id SERIAL PRIMARY KEY,
			app_id VARCHAR(128) NOT NULL,
			user_id VARCHAR(128) NOT NULL,
			authorized_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
			scope VARCHAR(256),
			redirect_uri VARCHAR(512),
			revoked_at TIMESTAMP WITH TIME ZONE
		)
	`)
	suite.NoError(err, "Failed to create oauth_authorizations table")
}

func (suite *AuthorizationRepositoryTestSuite) givenCleanOAuthAuthorizationsTable() {
	_, err := suite.db.Exec("DELETE FROM oauth_authorizations")
	suite.NoError(err, "Failed to clean oauth_authorizations table")
}
