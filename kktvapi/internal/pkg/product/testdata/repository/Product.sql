INSERT INTO public.products ("name", country, price, duration, created_at, updated_at, "payment_type", currency,
                             price_no_tax, tax_rate, item_name, item_unit, auto_renew, active, free_duration,
                             as_subscribe, fee, payment_type_code, purchase_upper_limit_count, external_product_id,
                             deleted_at, sort, discount_duration, discount_price, bundle, category,
                             discount_price_no_tax, discount_fee, fee_rate)
VALUES ('josie.zentest', 'TW', 168.34, '1 mon'::interval, '2017-11-09 13:45:34.331', NULL,
        'credit_card'::payment_type, 'NTD', 0.00, 5, '測試', '組', false, false, '1 mon'::interval, false, 15, '07', 0,
        NULL, NULL, 0, '00:00:00'::interval, 0.00, '{}'::jsonb, NULL, 0.00, 0, 0.00);
INSERT INTO public.products ("name", country, price, duration, created_at, updated_at, "payment_type", currency,
                             price_no_tax, tax_rate, item_name, item_unit, auto_renew, active, free_duration,
                             as_subscribe, fee, payment_type_code, purchase_upper_limit_count, external_product_id,
                             deleted_at, sort, discount_duration, discount_price, bundle, category,
                             discount_price_no_tax, discount_fee, fee_rate)
VALUES ('kktv.sub.promo3day', 'TW', 30.00, '3 days'::interval, '2017-11-13 16:43:08.264', NULL, 'iap'::payment_type,
        'NTD', 0.00, 5, '3 天 30 元', '次', false, true, '00:00:00'::interval, false, 0, '99', 0, NULL, NULL, 0,
        '00:00:00'::interval, 0.00, '{}'::jsonb, 'Organic', 0.00, 0, 0.00);
INSERT INTO public.products ("name", country, price, duration, created_at, updated_at, "payment_type", currency,
                             price_no_tax, tax_rate, item_name, item_unit, auto_renew, active, free_duration,
                             as_subscribe, fee, payment_type_code, purchase_upper_limit_count, external_product_id,
                             deleted_at, sort, discount_duration, discount_price, bundle, category,
                             discount_price_no_tax, discount_fee, fee_rate)
VALUES ('test.mod.duration1month149.225', 'TW', 149.00, '1 mons'::interval, '2018-08-20 05:38:23.542', NULL, 'mod'::payment_type,
        'NTD', 0.00, 0, '多螢版本/單月版 $149', '組', true, true, '00:00:00'::interval, false, 0, '06', 0, '225', NULL, 0,
        '00:00:00'::interval, 0.00, '{}'::jsonb, 'Channel', 0.00, 0, 0.00);
