package product

import (
	_ "embed"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/dbtest"
	"github.com/KKTV/kktv-api-v3/pkg/chtmod"
	"github.com/stretchr/testify/suite"
	"os"
	"testing"
	"time"
)

// database integration test suite for repository
type RepositoryTestSuite struct {
	dbtest.Suite
	repo Repository
}

func TestRepositoryIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(RepositoryTestSuite))
}

func (suite *RepositoryTestSuite) SetupSuite() {
	suite.Suite.SetupSuite(os.Getenv("TEST_DB_USER_DSN"))
}

func (suite *RepositoryTestSuite) SetupTest() {
	suite.Suite.SetupTest()

	tx := suite.GetTransaction()
	suite.repo = &repository{
		dbReader: tx,
	}
}

func (suite *RepositoryTestSuite) TearDownTest() {
	suite.Suite.TearDownTest()
}

func init() {
	_ = os.Setenv("TEST_DB_USER_DSN", "postgres://foo:bar@localhost:5432/kktv_users?sslmode=disable")
}

func (suite *RepositoryTestSuite) TestGetByName() {
	// GIVEN
	suite.DeleteFromTable("orders", "product_packages_to_products", "products")
	suite.givenProducts()

	suite.Run("should found product", func() {
		// WHEN
		productName := "josie.zentest"
		p, err := suite.repo.GetByName(productName)
		// THEN
		suite.NoError(err)
		suite.Equal(productName, p.Name)
		suite.Equal((time.Hour * 24 * 30).Seconds(), p.DurationSec) // `1 mon` in db
		suite.Equal(168.34, p.Price)
		suite.Equal(int64(5), p.TaxRate)
		suite.Equal(int64(15), p.Fee)
		suite.Equal("credit_card", p.PaymentType.String())
	})

	suite.Run("should not found product", func() {
		// WHEN
		productName := "not.exist"
		p, err := suite.repo.GetByName(productName)
		// THEN
		suite.Nil(err)
		suite.Nil(p)
	})

}

func (suite *RepositoryTestSuite) TestGetByMODExternalProductID() {
	// GIVEN
	suite.DeleteFromTable("orders", "product_packages_to_products", "products")
	suite.givenProducts()

	suite.Run("should found mod product", func() {
		// WHEN
		externalProductID := chtmod.MultiScreenProductID
		p, err := suite.repo.GetByMODExternalProductID(externalProductID)
		// THEN
		suite.NoError(err)
		suite.NotNil(p)
		suite.Equal(externalProductID, p.ExternalProductID.String)
		suite.Equal("mod", p.PaymentType.String())
	})

	suite.Run("should not found mod product", func() {
		// WHEN
		externalProductID := "100"
		p, err := suite.repo.GetByMODExternalProductID(externalProductID)
		// THEN
		suite.NoError(err)
		suite.Nil(p)
	})
}

//go:embed testdata/repository/Product.sql
var dataForProductRepo string

func (suite *RepositoryTestSuite) givenProducts() {
	if _, err := suite.GetTransaction().Exec(dataForProductRepo); err != nil {
		suite.Require().Fail("fail to insert test data", err)
	}
}
