// Code generated by MockGen. DO NOT EDIT.
// Source: kktvapi/internal/pkg/product/repository.go

// Package product is a generated GoMock package.
package product

import (
	context "context"
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// GetByMODExternalProductID mocks base method.
func (m *MockRepository) GetByMODExternalProductID(externalProductID string) (*dbuser.Product, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByMODExternalProductID", externalProductID)
	ret0, _ := ret[0].(*dbuser.Product)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByMODExternalProductID indicates an expected call of GetByMODExternalProductID.
func (mr *MockRepositoryMockRecorder) GetByMODExternalProductID(externalProductID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByMODExternalProductID", reflect.TypeOf((*MockRepository)(nil).GetByMODExternalProductID), externalProductID)
}

// GetByName mocks base method.
func (m *MockRepository) GetByName(name string) (*dbuser.Product, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByName", name)
	ret0, _ := ret[0].(*dbuser.Product)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByName indicates an expected call of GetByName.
func (mr *MockRepositoryMockRecorder) GetByName(name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByName", reflect.TypeOf((*MockRepository)(nil).GetByName), name)
}

// GetMODProducts mocks base method.
func (m *MockRepository) GetMODProducts(ctx context.Context) (map[string]interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMODProducts", ctx)
	ret0, _ := ret[0].(map[string]interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMODProducts indicates an expected call of GetMODProducts.
func (mr *MockRepositoryMockRecorder) GetMODProducts(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMODProducts", reflect.TypeOf((*MockRepository)(nil).GetMODProducts), ctx)
}
