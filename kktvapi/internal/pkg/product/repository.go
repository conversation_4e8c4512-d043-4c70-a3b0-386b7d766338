//go:generate mockgen -source repository.go -destination repository_mock.go -package product
package product

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/aws/s3"

	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

type Repository interface {
	GetByName(name string) (*dbuser.Product, error)
	GetByMODExternalProductID(externalProductID string) (*dbuser.Product, error)
	GetMODProducts(ctx context.Context) (map[string]interface{}, error)
}

type repository struct {
	dbReader database.DB
	s3client s3.S3Client
}

func NewRepository() Repository {
	return &repository{
		dbReader: container.DBPoolUser().Slave().Unsafe(),
		s3client: s3.NewS3Client(config.AWSConfig),
	}
}

func NewRepositoryWith(dbReader, dbWriter database.DB) Repository {
	return &repository{
		dbReader: dbReader,
	}
}

func (r *repository) GetByName(name string) (*dbuser.Product, error) {
	var p = new(dbuser.Product)
	if err := r.dbReader.Get(p,
		`SELECT id,
			name,
       		price::money::numeric::float8 as price,
       		price_no_tax::money::numeric::float8 as price_no_tax,
			EXTRACT(EPOCH FROM duration) AS duration,
			payment_type,
			auto_renew,
			as_subscribe,
			bundle,
			tax_rate,
			fee,
			payment_type,
			payment_type_code,
			created_at, updated_at,
			active,
			EXTRACT(EPOCH FROM free_duration) AS free_duration
	FROM products WHERE name = $1`, name); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return p, nil
}

func (r *repository) GetByMODExternalProductID(externalProductID string) (*dbuser.Product, error) {
	p := new(dbuser.Product)
	q := `SELECT 
			id,
       		price::money::numeric::float8 		 AS price,
       		price_no_tax::money::numeric::float8 AS price_no_tax,
			EXTRACT(EPOCH FROM duration) 		 AS duration,
    		EXTRACT(EPOCH FROM free_duration)    AS free_duration,
    		name,
    		item_name,
			payment_type,
			auto_renew,
			as_subscribe,
			bundle,
			tax_rate,
			fee,
			payment_type,
			payment_type_code,
			active,
			external_product_id,
			created_at, 
			updated_at
	FROM products WHERE payment_type = $1 AND external_product_id = $2`

	if err := r.dbReader.Get(p, q, dbuser.PaymentInfoTypeMod, externalProductID); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return p, nil
}

func (r *repository) GetMODProducts(ctx context.Context) (map[string]interface{}, error) {
	key := "v4/mod/products.json"
	body, err := r.s3client.GetObject(ctx, config.AwsS3BucketKKTVApi, key)
	if err != nil {
		return nil, fmt.Errorf("s3client get object: %w", err)
	}
	defer body.Close()

	var products map[string]interface{}
	if err := json.NewDecoder(body).Decode(&products); err != nil {
		return products, fmt.Errorf("json decode: %w", err)
	}
	return products, nil
}
