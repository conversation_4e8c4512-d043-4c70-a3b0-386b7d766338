package user

import (
	"bytes"
	"encoding/json"
	"log"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/consoleapi/internal/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/consoleapi/internal/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/auditing"
	usermodel "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/go-zoo/bone"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"gopkg.in/guregu/null.v3"
)

type HandlerTestSuite struct {
	suite.Suite
	ctrl    *gomock.Controller
	app     *bone.Mux
	repo    *MockRepository
	logRepo *auditing.MockLogRepository
}

func TestHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(HandlerTestSuite))
}

func (suite *HandlerTestSuite) SetupTest() {
	suite.app = bone.New()
	suite.ctrl = gomock.NewController(suite.T())
	suite.repo = NewMockRepository(suite.ctrl)
	suite.logRepo = auditing.NewMockLogRepository(suite.ctrl)
	handler := &Handler{
		repo:    suite.repo,
		logRepo: suite.logRepo,
	}
	suite.app.GetFunc("/v3/console/user/:id", handler.Get)
	suite.app.PostFunc("/v3/console/user:search", handler.Search)
	suite.app.PutFunc("/v3/console/user/:id", handler.Put)
}

func (suite *HandlerTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func (suite *HandlerTestSuite) TestGet() {
	tests := []struct {
		name           string
		userID         string
		mockUser       *dbuser.UserInfo
		mockError      error
		expectedStatus int
		expectedUser   *Info
	}{
		{
			name:           "Success",
			userID:         "123",
			mockUser:       &dbuser.UserInfo{ID: "123"},
			mockError:      nil,
			expectedStatus: http.StatusOK,
			expectedUser:   &Info{ID: "123"},
		},
		{
			name:           "User Not Found",
			userID:         "456",
			mockUser:       nil,
			mockError:      nil,
			expectedStatus: http.StatusNotFound,
			expectedUser:   nil,
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {

			suite.repo.EXPECT().
				GetDetailByID(tt.userID).
				Return(tt.mockUser, tt.mockError).
				Times(1)

			req := httptest.NewRequest(http.MethodGet, "/v3/console/user/"+tt.userID, nil)

			rr := httptest.NewRecorder()

			suite.app.ServeHTTP(rr, req)

			suite.Assert().Equal(tt.expectedStatus, rr.Code)

			var response struct {
				Data *Info `json:"data"`
			}
			_ = json.Unmarshal(rr.Body.Bytes(), &response)

			if tt.expectedUser == nil {
				suite.Assert().Nil(response.Data)
			} else {
				suite.Assert().Equal(tt.expectedUser, response.Data)
			}
		})
	}
}

func (suite *HandlerTestSuite) TestSearch() {
	var (
		user1 = &usermodel.User{Email: null.StringFrom("<EMAIL>"), ID: "josie1"}
		user2 = &usermodel.User{ID: "josie2", RevokedAt: null.TimeFrom(time.Date(2020, 4, 29, 0, 0, 0, 0, time.UTC))}
	)

	testcases := []struct {
		name       string
		req        searchReq
		given      func()
		respCode   int
		assertBody func(body []byte)
	}{
		{
			name: "search by email, BOTH match account email and payment email SHOULD be returned, and the result should be unique",
			req: searchReq{
				Target: "email",
				Email:  "<EMAIL>",
			},
			given: func() {
				suite.repo.EXPECT().ListByEmail("<EMAIL>").Return([]*usermodel.User{{Email: null.StringFrom("<EMAIL>"), ID: "josie1"}}, nil)
				suite.repo.EXPECT().ListByPaymentEmail("<EMAIL>").Return([]*usermodel.User{user1, user2}, nil)
			},
			respCode: http.StatusOK,
			assertBody: func(body []byte) {
				var resp rest.Resp[map[string]interface{}]
				_ = json.Unmarshal(body, &resp)
				users := resp.Data["items"].([]interface{})

				suite.Require().Len(users, 2)
				for i, userID := range []string{"josie1", "josie2"} {
					suite.Require().Equal(userID, users[i].(map[string]interface{})["id"])
				}
			},
		},
		{
			name: "search by phone number",
			req: searchReq{
				Target: "phone",
				Phone:  "**********",
			},
			given: func() {
				suite.repo.EXPECT().ListByPhone("+************").Return([]*usermodel.User{user1}, nil)
			},
			respCode: http.StatusOK,
			assertBody: func(body []byte) {
				var resp rest.Resp[map[string]interface{}]
				_ = json.Unmarshal(body, &resp)
				users := resp.Data["items"].([]interface{})

				suite.Require().Len(users, 1)
				suite.Require().Equal("josie1", users[0].(map[string]interface{})["id"])
			},
		},
		{
			name: "search by phone non-taiwan number, THEN got bad request response",
			req: searchReq{
				Target: "phone",
				Phone:  "+85312345678",
			},
			given:      func() {},
			respCode:   http.StatusBadRequest,
			assertBody: func(body []byte) {},
		},
		{
			name: "search by user id",
			req: searchReq{
				Target: "user_id",
				UserID: "josie1",
				Email:  "<EMAIL>", // email input should be ignored
			},
			given: func() {
				suite.repo.EXPECT().GetByID("josie1").Return(user1, nil)
			},
			respCode: http.StatusOK,
			assertBody: func(body []byte) {
				var resp rest.Resp[map[string]interface{}]
				_ = json.Unmarshal(body, &resp)
				users := resp.Data["items"].([]interface{})

				suite.Require().Len(users, 1)
				suite.Require().Equal("josie1", users[0].(map[string]interface{})["id"])
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			reqBody, _ := json.Marshal(tc.req)
			req := httptest.NewRequest(http.MethodPost, "/v3/console/user:search", bytes.NewReader(reqBody))
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			log.Println(rr.Body.String())
			suite.Require().Equal(tc.respCode, rr.Code)
			tc.assertBody(rr.Body.Bytes())
		})
	}
}

// func (suite *HandlerTestSuite) TestPut() {
// 	testCases := []struct {
// 		name            string
// 		userID          string
// 		inputUser       *ModifyUser
// 		mockGetUser     *usermodel.User
// 		mockGetError    error
// 		mockUpdateError error
// 		expectedStatus  int
// 	}{
// 		{
// 			name:            "Success",
// 			userID:          "123",
// 			inputUser:       &ModifyUser{Name: null.StringFrom("Updated Name")},
// 			mockGetUser:     &usermodel.User{ID: "123", Name: null.StringFrom("Original Name")},
// 			mockGetError:    nil,
// 			mockUpdateError: nil,
// 			expectedStatus:  http.StatusOK,
// 		},
// 		{
// 			name:            "User Not Found",
// 			userID:          "456",
// 			inputUser:       &ModifyUser{ID: "123", Name: null.StringFrom("New Name")},
// 			mockGetUser:     nil,
// 			mockGetError:    nil,
// 			mockUpdateError: nil,
// 			expectedStatus:  http.StatusNotFound,
// 		},
// 		{
// 			name:            "Invalid UserID",
// 			userID:          "",
// 			inputUser:       &ModifyUser{Name: null.StringFrom("New Name")},
// 			mockGetUser:     nil,
// 			mockGetError:    nil,
// 			mockUpdateError: nil,
// 			expectedStatus:  http.StatusBadRequest,
// 		},
// 		{
// 			name:            "Update Error",
// 			userID:          "789",
// 			inputUser:       &ModifyUser{Name: null.StringFrom("Updated Name")},
// 			mockGetUser:     &usermodel.User{ID: "789", Name: null.StringFrom("Original Name")},
// 			mockGetError:    nil,
// 			mockUpdateError: errors.New("update error"),
// 			expectedStatus:  http.StatusInternalServerError,
// 		},
// 	}
// 	for _, tc := range testCases {
// 		suite.Run(tc.name, func() {
// 			// Mock GetByID
// 			if tc.userID != "" {
// 				suite.repo.EXPECT().
// 					GetByID(tc.userID).
// 					Return(tc.mockGetUser, tc.mockGetError).
// 					Times(1)
// 			}

// 			// Mock Update if needed
// 			if tc.mockGetUser != nil {
// 				tc.inputUser.ID = tc.userID
// 				suite.repo.EXPECT().
// 					Update(tc.inputUser).
// 					Return(tc.mockUpdateError).
// 					Times(1)
// 			}
// 			if tc.mockGetUser != nil && tc.mockUpdateError == nil {
// 				originUser := new(ModifyUser)
// 				originUser.CopyFromModel(tc.mockGetUser)
// 				logDiffs, _ := auditing.GetDiffFields(originUser, tc.inputUser)
// 				auditBuilder := auditing.NewLogBuilder().ByConsole().
// 					TargetUpdated("user", tc.inputUser.ID).
// 					DetailDiff(logDiffs...).
// 					Note(tc.inputUser.Reason)

// 				auditLogs := auditBuilder.Build()
// 				suite.logRepo.EXPECT().
// 					Insert(gomock.AssignableToTypeOf(auditLogs)).
// 					Return(nil).
// 					Times(1)
// 			}
// 			// Prepare request body
// 			body, _ := json.Marshal(tc.inputUser)
// 			req := httptest.NewRequest(http.MethodPut, "/v3/console/user/"+tc.userID, bytes.NewBuffer(body))
// 			req.Header.Set("Content-Type", "application/json")

// 			rr := httptest.NewRecorder()

// 			suite.app.ServeHTTP(rr, req)

// 			suite.Assert().Equal(tc.expectedStatus, rr.Code)

// 		})
// 	}

// }
