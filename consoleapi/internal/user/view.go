package user

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"github.com/KKTV/kktv-api-v3/consoleapi/internal/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	usermodel "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/jinzhu/copier"
	"gopkg.in/guregu/null.v3"
)

type Info struct {
	ID                     string               `json:"id"`
	Email                  null.String          `json:"email"`
	Phone                  null.String          `json:"phone"`
	AvatarURL              null.String          `json:"avatarUrl"`
	Name                   null.String          `json:"name"`
	Birthday               null.String          `json:"birthday"`
	Gender                 null.String          `json:"gender"`
	Membership             usermodel.Membership `json:"membership"`
	MediaSource            null.String          `json:"mediaSource"`
	CreatedAt              null.Time            `json:"createdAt"`
	CreatedBy              null.String          `json:"createdBy"`
	ExpiredAt              int64                `json:"expiredAt"`
	AutoRenew              bool                 `json:"auto_renew"`
	ModSubscriberArea      null.String          `json:"mod_subscriber_area"`
	ModSubscriberID        null.String          `json:"mod_subscriber_id"`
	PaymentType            null.String          `json:"payment_type,omitempty"`
	PaymentEmail           null.String          `json:"payment_email,omitempty"`
	CreditCard6No          null.String          `json:"credit_card_6no,omitempty"`
	CreditCard4No          null.String          `json:"credit_card_4no,omitempty"`
	Recipient              null.String          `json:"recipient,omitempty"`
	RecipientAddress       null.String          `json:"recipient_address,omitempty"`
	CarrierType            null.String          `json:"carrier_type,omitempty"`
	CarrierValue           null.String          `json:"carrier_value,omitempty"`
	IabOrderID             null.String          `json:"iab_order_id,omitempty"`
	IabLatestOrderID       null.String          `json:"iab_latest_order_id,omitempty"`
	IabLatestExpiresDate   null.Time            `json:"iab_latest_expires_date,omitempty"`
	IapLatestTransactionID null.String          `json:"iap_latest_transaction_id,omitempty"`
	IapLatestExpiresDate   null.Time            `json:"iap_latest_expires_date,omitempty"`
	KKIDBoundAt            null.Time            `json:"kkid_bound_at"`
	OriginProvider         null.String          `json:"origin_provider"`
	FamilyID               null.String          `json:"family_id,omitempty"`
	RevokedAt              null.Time            `json:"revoked_at,omitempty"`
	HasPassword            bool                 `json:"has_password"`
	EmailVerifiedAt        int64                `json:"email_verified_at,omitempty"`
	PhoneVerifiedAt        int64                `json:"phone_verified_at,omitempty"`
}

func (u *Info) CopyFromModel(m *dbuser.UserInfo) {
	if err := copier.Copy(u, m); err != nil {
		log.Error("user info: copy value from legacy model fail").Err(err).
			Interface("legacy_model", m).
			Send()
	} else {
		if m.Password.Valid && m.Password.String != "" {
			u.HasPassword = true
		}
		if m.ExpiredAt.Valid {
			u.ExpiredAt = m.ExpiredAt.Time.Unix()
		}
		if m.EmailVerifiedAt.Valid {
			u.EmailVerifiedAt = m.EmailVerifiedAt.Time.Unix()
		}
		if m.PhoneVerifiedAt.Valid {
			u.PhoneVerifiedAt = m.PhoneVerifiedAt.Time.Unix()
		}
	}

}

type AuthInfo struct {
	Token                   string `json:"token"`
	ExpiredAt               int64  `json:"expiredAt"`
	RefreshToken            string `json:"refreshToken,omitempty"`
	RefreshTokenExpiresDate int64  `json:"refreshTokenExpiresDate,omitempty"`
}

type ListedUser struct {
	ID          string          `json:"id"`
	Email       null.String     `json:"email"`
	Phone       null.String     `json:"phone"`
	RevokedAt   null.Int        `json:"revoked_at"`
	MediaSource json.RawMessage `json:"media_source"`
	Membership  Memberships     `json:"membership"`
	Role        string          `json:"role"` // Deprecated: replaced by membership
	Type        string          `json:"type"` // Deprecated: replaced by membership
}

func (u *ListedUser) CopyFromModel(m *usermodel.User) {
	if err := copier.Copy(u, m); err != nil {
		log.Error("user info: copy value from legacy model fail").Err(err).
			Interface("legacy_model", m).
			Send()
	} else {
		u.Membership = make(Memberships, len(m.Membership))
		for i := range m.Membership {
			u.Membership[i] = RoleModel{Role: m.Membership[i].Role.String()}
		}
		if m.RevokedAt.Valid {
			u.RevokedAt = null.IntFrom(m.RevokedAt.Time.Unix())
		}
	}
}

const (
	MemberRoleFreeTrial = "freetrial"
	MemberRoleExpired   = "expired"
	MemberRolePremium   = "premium"
	MemberRolePrime     = "prime"
	MemberRolePR        = "pr"
	MemberRolePaidAnime = "paid:anime"
)

const (
	TypeTest    = "test"
	TypePrime   = "prime"
	TypeGeneral = "general"
	TypePR      = "pr"
)

type RoleModel struct {
	Role string `json:"role"`
}

type Memberships []RoleModel

func (m Memberships) ToRoleType() (string, string) {
	var (
		roleStr, typeStr string
	)
	typeStr = "general"
	switch m[0].Role {
	case MemberRoleFreeTrial:
		roleStr = MemberRoleFreeTrial
	case MemberRoleExpired:
		roleStr = MemberRoleExpired
	case MemberRolePremium:
		roleStr = MemberRolePremium
	case MemberRolePrime:
		roleStr = MemberRolePremium
		typeStr = TypePrime
	case MemberRolePR:
		roleStr = MemberRolePremium
		typeStr = TypePR
	case MemberRolePaidAnime:
		roleStr = MemberRolePremium
	}
	return roleStr, typeStr
}

func (m Memberships) Value() (driver.Value, error) {
	if len(m) == 0 {
		return nil, nil
	}
	if jsonValue, err := json.Marshal(m); err != nil {
		return nil, err
	} else {
		return driver.Value(jsonValue), nil
	}
}

func (m *Memberships) Scan(src interface{}) error {
	var source []byte
	membership := make(Memberships, 0)

	switch i := src.(type) {
	case []uint8:
		source = i
	case nil:
		source = []byte("[]")
	default:
		return errors.New("incompatible type for Membership")
	}

	if err := json.Unmarshal(source, &membership); err != nil {
		return err
	}

	*m = membership
	return nil
}

type ModifyUser struct {
	ID         string      `json:"id" db:"id" identity:"true"`
	Membership Memberships `json:"membership" db:"membership"`
	Name       null.String `json:"name" db:"name"`
	Email      null.String `json:"email" db:"email"`
	Phone      null.String `json:"phone" db:"phone"`
	Role       null.String `json:"role" db:"role"`
	Type       null.String `json:"type" db:"type"`
	AutoRenew  bool        `json:"auto_renew" db:"auto_renew"`
	ExpiredAt  string      `json:"expired_at" db:"expired_at"`
	Reason     string      `json:"reason,omitempty"`
}

func (u *ModifyUser) CopyFromModel(m *usermodel.User) {
	if err := copier.Copy(u, m); err != nil {
		log.Error("user info: copy value from legacy model fail").Err(err).
			Interface("legacy_model", m).
			Send()
	} else {
		u.Membership = make(Memberships, len(m.Membership))
		for i := range m.Membership {
			u.Membership[i] = RoleModel{Role: m.Membership[i].Role.String()}
		}
		if m.ExpiredAt.Valid {
			u.ExpiredAt = m.ExpiredAt.Time.UTC().String()
		}
	}
}

type ModifyPaymentInfo struct {
	UserID           string      `db:"user_id" identity:"true" json:"user_id"`
	RecipientAddress null.String `db:"recipient_address" json:"recipient_address"`
	CarrierType      null.String `db:"carrier_type" json:"carrier_type,omitempty"`
	CarrierValue     null.String `db:"carrier_value" json:"carrier_value,omitempty"`
	CaringCode       null.String `db:"caring_code" json:"caring_code,omitempty"`
	Reason           string      `db:"reason" json:"reason"`
}
type EnumItem struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

func GetMemberRoleList() []usermodel.MemberRole {
	return []usermodel.MemberRole{
		usermodel.MemberRoleFreeTrial,
		usermodel.MemberRoleExpired,
		usermodel.MemberRolePremium,
		usermodel.MemberRolePrime,
		usermodel.MemberRolePR,
		usermodel.MemberRolePaidAnime,
	}
}
