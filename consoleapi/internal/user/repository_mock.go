// Code generated by MockGen. DO NOT EDIT.
// Source: repository.go

// Package user is a generated GoMock package.
package user

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/consoleapi/internal/pkg/model/dbuser"
	dbuser0 "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// GetByID mocks base method.
func (m *MockRepository) GetByID(userID string) (*dbuser0.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", userID)
	ret0, _ := ret[0].(*dbuser0.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockRepositoryMockRecorder) GetByID(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockRepository)(nil).GetByID), userID)
}

// GetDetailByID mocks base method.
func (m *MockRepository) GetDetailByID(id string) (*dbuser.UserInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDetailByID", id)
	ret0, _ := ret[0].(*dbuser.UserInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDetailByID indicates an expected call of GetDetailByID.
func (mr *MockRepositoryMockRecorder) GetDetailByID(id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDetailByID", reflect.TypeOf((*MockRepository)(nil).GetDetailByID), id)
}

// GetPaymentInfoByUserID mocks base method.
func (m *MockRepository) GetPaymentInfoByUserID(userID string) (*dbuser0.PaymentInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentInfoByUserID", userID)
	ret0, _ := ret[0].(*dbuser0.PaymentInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentInfoByUserID indicates an expected call of GetPaymentInfoByUserID.
func (mr *MockRepositoryMockRecorder) GetPaymentInfoByUserID(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentInfoByUserID", reflect.TypeOf((*MockRepository)(nil).GetPaymentInfoByUserID), userID)
}

// ListByEmail mocks base method.
func (m *MockRepository) ListByEmail(email string) ([]*dbuser0.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByEmail", email)
	ret0, _ := ret[0].([]*dbuser0.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByEmail indicates an expected call of ListByEmail.
func (mr *MockRepositoryMockRecorder) ListByEmail(email interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByEmail", reflect.TypeOf((*MockRepository)(nil).ListByEmail), email)
}

// ListByID mocks base method.
func (m *MockRepository) ListByID(ids []string) ([]*dbuser.UserInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByID", ids)
	ret0, _ := ret[0].([]*dbuser.UserInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByID indicates an expected call of ListByID.
func (mr *MockRepositoryMockRecorder) ListByID(ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByID", reflect.TypeOf((*MockRepository)(nil).ListByID), ids)
}

// ListByModID mocks base method.
func (m *MockRepository) ListByModID(modID string) ([]*dbuser0.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByModID", modID)
	ret0, _ := ret[0].([]*dbuser0.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByModID indicates an expected call of ListByModID.
func (mr *MockRepositoryMockRecorder) ListByModID(modID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByModID", reflect.TypeOf((*MockRepository)(nil).ListByModID), modID)
}

// ListByPaymentEmail mocks base method.
func (m *MockRepository) ListByPaymentEmail(email string) ([]*dbuser0.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByPaymentEmail", email)
	ret0, _ := ret[0].([]*dbuser0.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByPaymentEmail indicates an expected call of ListByPaymentEmail.
func (mr *MockRepositoryMockRecorder) ListByPaymentEmail(email interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByPaymentEmail", reflect.TypeOf((*MockRepository)(nil).ListByPaymentEmail), email)
}

// ListByPhone mocks base method.
func (m *MockRepository) ListByPhone(phone string) ([]*dbuser0.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByPhone", phone)
	ret0, _ := ret[0].([]*dbuser0.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByPhone indicates an expected call of ListByPhone.
func (mr *MockRepositoryMockRecorder) ListByPhone(phone interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByPhone", reflect.TypeOf((*MockRepository)(nil).ListByPhone), phone)
}

// Search mocks base method.
func (m *MockRepository) Search(ids, emails, phones, modIDs, kkidIdentifiers []string) ([]*dbuser.UserInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Search", ids, emails, phones, modIDs, kkidIdentifiers)
	ret0, _ := ret[0].([]*dbuser.UserInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Search indicates an expected call of Search.
func (mr *MockRepositoryMockRecorder) Search(ids, emails, phones, modIDs, kkidIdentifiers interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Search", reflect.TypeOf((*MockRepository)(nil).Search), ids, emails, phones, modIDs, kkidIdentifiers)
}

// Update mocks base method.
func (m *MockRepository) Update(user *ModifyUser) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", user)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockRepositoryMockRecorder) Update(user interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRepository)(nil).Update), user)
}

// UpdatePaymentInfo mocks base method.
func (m *MockRepository) UpdatePaymentInfo(paymentInfo *ModifyPaymentInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePaymentInfo", paymentInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePaymentInfo indicates an expected call of UpdatePaymentInfo.
func (mr *MockRepositoryMockRecorder) UpdatePaymentInfo(paymentInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePaymentInfo", reflect.TypeOf((*MockRepository)(nil).UpdatePaymentInfo), paymentInfo)
}
