//go:generate mockgen -source repository.go -destination repository_mock.go -package user
package user

import (
	"database/sql"
	"errors"
	"strings"
	"sync"

	"github.com/KKTV/kktv-api-v3/consoleapi/internal/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	usermodel "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/lib/pq"
	"gopkg.in/guregu/null.v3"
)

var (
	repo     Repository
	onceRepo sync.Once
)

type Repository interface {
	// Deprecated: inefficient
	Search(ids, emails, phones, modIDs, kkidIdentifiers []string) ([]*dbuser.UserInfo, error)
	ListByID(ids []string) ([]*dbuser.UserInfo, error)

	GetDetailByID(id string) (*dbuser.UserInfo, error)
	GetByID(userID string) (*usermodel.User, error)
	ListByEmail(email string) ([]*usermodel.User, error)
	ListByPaymentEmail(email string) ([]*usermodel.User, error)
	ListByPhone(phone string) ([]*usermodel.User, error)
	ListByModID(modID string) ([]*usermodel.User, error)
	Update(user *ModifyUser) error
	GetPaymentInfoByUserID(userID string) (*usermodel.PaymentInfo, error)
	UpdatePaymentInfo(paymentInfo *ModifyPaymentInfo) error
}

type repository struct {
	dbReader database.DB
	dbWriter database.DB
}

func NewRepository(dbPoolUser *datastore.DBPool) Repository {
	onceRepo.Do(func() {
		repo = &repository{
			dbReader: dbPoolUser.Slave().Unsafe(),
			dbWriter: dbPoolUser.Master().Unsafe(),
		}
	})
	return repo
}

func (r *repository) ListByID(ids []string) ([]*dbuser.UserInfo, error) {
	q := baseQuery + " WHERE u.id = ANY($1);"

	var users []*dbuser.UserInfo
	if err := r.dbReader.Select(&users, q, pq.Array(ids)); err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}
	return users, nil
}

func (r *repository) Search(ids, emails, phones, modIDs, kkidIdentifiers []string) ([]*dbuser.UserInfo, error) {
	q := `SELECT u.*,
			pay.mod_subscriber_area, pay.mod_subscriber_id,
			pay.payment_type, pay.email as payment_email,
			pay.credit_card_6no, pay.credit_card_4no,
			pay.recipient, pay.recipient_address,
			pay.carrier_type, pay.carrier_value,
			pay.iab_order_id, pay.iab_latest_order_id, pay.iab_latest_expires_date,
			pay.iap_latest_transaction_id, pay.iap_latest_expires_date, pay.family_id
			FROM users u LEFT JOIN payment_info pay ON u.id = pay.user_id
			WHERE
			(
				u.id = ANY($1)
				OR LOWER(u.email) = ANY($2)
				OR u.phone LIKE ANY($3)
				OR u.media_source #>> '{kkbox,identifier}' = ANY($5)
			)
			UNION
			SELECT u.*, 
			pay.mod_subscriber_area, pay.mod_subscriber_id,
			pay.payment_type, pay.email as payment_email,
			pay.credit_card_6no, pay.credit_card_4no,
			pay.recipient, pay.recipient_address,
			pay.carrier_type, pay.carrier_value,
			pay.iab_order_id, pay.iab_latest_order_id, pay.iab_latest_expires_date,
			pay.iap_latest_transaction_id, pay.iap_latest_expires_date, pay.family_id
			FROM users u LEFT JOIN payment_info pay ON
			u.id = pay.user_id
			WHERE
			LOWER(pay.email) = ANY($2)
			OR (pay.payment_type = 'mod' AND pay.mod_subscriber_id = ANY($4))`

	var users []*dbuser.UserInfo
	if err := r.dbReader.Select(&users, q, pq.Array(ids), pq.Array(emails), pq.Array(phones), pq.Array(modIDs), pq.Array(kkidIdentifiers)); err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}
	return users, nil
}

const baseQuery = `SELECT u.*,
      	pay.mod_subscriber_area, pay.mod_subscriber_id, pay.payment_type, pay.email as payment_email, 
		pay.credit_card_6no, pay.credit_card_4no, pay.recipient, pay.recipient_address, pay.carrier_type, pay.carrier_value,
		pay.iab_order_id, pay.iab_latest_order_id, pay.iab_latest_expires_date, pay.iap_latest_transaction_id, pay.iap_latest_expires_date
		FROM users u LEFT JOIN payment_info pay ON
		u.id = pay.user_id`

func (r *repository) GetDetailByID(id string) (*dbuser.UserInfo, error) {
	q := baseQuery + " where u.id = $1;"

	var user dbuser.UserInfo
	if err := r.dbReader.Get(&user, q, id); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *repository) GetByID(userID string) (*usermodel.User, error) {
	q := `SELECT * FROM users WHERE id = $1`
	u := new(usermodel.User)
	if err := r.dbReader.Get(u, q, userID); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return u, nil
}

func (r *repository) ListByEmail(email string) ([]*usermodel.User, error) {
	email = strings.ToLower(email)
	records := make([]*usermodel.User, 0)
	q := `SELECT * FROM users WHERE email = $1`
	if err := r.dbReader.Select(&records, q, email); err != nil {
		return nil, err
	}
	return records, nil
}

func (r *repository) ListByPaymentEmail(email string) ([]*usermodel.User, error) {
	email = strings.ToLower(email)
	records := make([]*usermodel.User, 0)
	q := `SELECT u.* FROM users u JOIN payment_info pay ON u.id = pay.user_id WHERE pay.email = $1`
	if err := r.dbReader.Select(&records, q, email); err != nil {
		return nil, err
	}
	return records, nil
}

func (r *repository) ListByPhone(normalizedPhoneNum string) ([]*usermodel.User, error) {
	records := make([]*usermodel.User, 0)
	q := `SELECT * FROM users WHERE phone = $1`
	if err := r.dbReader.Select(&records, q, normalizedPhoneNum); err != nil {
		return nil, err
	}
	return records, nil
}

func (r *repository) ListByModID(modID string) ([]*usermodel.User, error) {
	records := make([]*usermodel.User, 0)
	q := `SELECT u.* FROM users u JOIN payment_info pay ON u.id = pay.user_id WHERE pay.mod_subscriber_id = $1`
	if err := r.dbReader.Select(&records, q, modID); err != nil {
		return nil, err
	}
	return records, nil
}

func (r *repository) Update(user *ModifyUser) error {
	sql := `UPDATE users SET membership = :membership, name = :name, email = :email, phone = :phone, auto_renew = :auto_renew, expired_at = :expired_at, role = :role, type = :type WHERE id=:id`
	roleStr, typeStr := user.Membership.ToRoleType()
	user.Role = null.StringFrom(roleStr)
	user.Type = null.StringFrom(typeStr)
	_, err := r.dbWriter.NamedExec(sql, &user)
	if err != nil {
		return err
	}
	return nil
}

func (r *repository) GetPaymentInfoByUserID(userID string) (*usermodel.PaymentInfo, error) {
	sql := `SELECT * FROM payment_info  WHERE user_id = $1`
	var paymentInfo usermodel.PaymentInfo
	err := r.dbReader.Get(&paymentInfo, sql, userID)
	if err != nil {
		return nil, err
	}
	return &paymentInfo, nil
}
func (r *repository) UpdatePaymentInfo(paymentInfo *ModifyPaymentInfo) error {
	sql := `UPDATE payment_info SET recipient_address = :recipient_address, carrier_type = :carrier_type, carrier_value = :carrier_value, caring_code = :caring_code  WHERE user_id = :user_id`
	_, err := r.dbWriter.NamedExec(sql, &paymentInfo)
	if err != nil {
		return err
	}
	return nil
}
