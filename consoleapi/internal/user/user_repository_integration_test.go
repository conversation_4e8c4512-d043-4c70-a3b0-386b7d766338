package user

import (
	_ "embed"
	"testing"

	"github.com/KKTV/kktv-api-v3/consoleapi/internal/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	model "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/testutils"
	"github.com/stretchr/testify/suite"
)

type UserRepositoryTestSuite struct {
	suite.Suite
	testContainer *testutils.Container
	repo          Repository
	dbReader      database.DB
	dbWriter      database.DB
}

func TestUserRepositoryIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(UserRepositoryTestSuite))
}

func (suite *UserRepositoryTestSuite) SetupTest() {
	suite.repo = &repository{
		dbReader: suite.dbReader,
		dbWriter: suite.dbWriter,
	}

}

func (suite *UserRepositoryTestSuite) TearDownAllSuite() {
	if err := suite.testContainer.Close(); err != nil {
		suite.Fail("fail to terminate test container", err)
	}
}

func (suite *UserRepositoryTestSuite) SetupSuite() {
	testContainer, dbPool := testutils.SetupTestDatabase(testutils.DBUser)
	suite.testContainer = testContainer

	suite.dbWriter = dbPool.Master().Unsafe()
	suite.dbReader = dbPool.Slave().Unsafe()
}

func (suite *UserRepositoryTestSuite) TestGetByID() {
	suite.givenUsers()

	testcases := []struct {
		name   string
		userID string
		then   func(*dbuser.UserInfo, error)
	}{
		{
			name:   "found user",
			userID: "402b9903-f9c7-4179-b484-1cb9b08a0fef",
			then: func(user *dbuser.UserInfo, err error) {
				suite.Require().NoError(err)
				suite.Require().Equal("<EMAIL>", user.Email.String)
				suite.Require().Equal(model.Membership{{Role: model.MemberRolePremium}}, user.Membership)
			},
		},
		{
			name:   "find not exist THEN got nil",
			userID: "not-exist",
			then: func(user *dbuser.UserInfo, err error) {
				suite.Require().NoError(err)
				suite.Require().Nil(user)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.then(suite.repo.GetDetailByID(tc.userID))
		})
	}
}

//go:embed testdata/UserRepo.sql
var dataForUserRepo string

func (suite *UserRepositoryTestSuite) givenUsers() {
	if _, err := suite.dbWriter.Exec(dataForUserRepo); err != nil {
		suite.Require().Fail("fail to insert test data", err)
	}
}
