package urls

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestImage(t *testing.T) {
	tests := []struct {
		name     string
		path     string
		expected string
	}{
		{
			name:     "path starts with slash",
			path:     "/path/to/images",
			expected: "https://test-images.kktv.com.tw/path/to/images",
		},
		{
			name:     "path starts without slash",
			path:     "path/to/image",
			expected: "https://test-images.kktv.com.tw/path/to/image",
		},
		{
			name:     "empty path",
			path:     "",
			expected: "https://test-images.kktv.com.tw/",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Image(tt.path)
			assert.Equal(t, tt.expected, result)
		})
	}
}
