package dbuser

import (
	model "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"gopkg.in/guregu/null.v3"
)

type UserInfo struct {
	ID                     string           `db:"id" `
	Email                  null.String      `db:"email" `
	Phone                  null.String      `db:"phone" `
	AvatarURL              null.String      `db:"avatar_url" `
	Name                   null.String      `db:"name" `
	Birthday               null.String      `db:"birthday" `
	Gender                 null.String      `db:"gender" `
	Role                   string           `db:"role" `
	Type                   string           `db:"type" `
	MediaSource            null.String      `db:"media_source" `
	CreatedAt              null.Time        `db:"created_at" `
	CreatedBy              null.String      `db:"created_by" `
	ExpiredAt              null.Time        `db:"expired_at" `
	AutoRenew              bool             `db:"auto_renew" `
	Membership             model.Membership `db:"membership"`
	ModSubscriberArea      null.String      `db:"mod_subscriber_area" `
	ModSubscriberID        null.String      `db:"mod_subscriber_id" `
	PaymentType            null.String      `db:"payment_type"`
	PaymentEmail           null.String      `db:"payment_email"`
	CreditCard6No          null.String      `db:"credit_card_6no"`
	CreditCard4No          null.String      `db:"credit_card_4no"`
	Recipient              null.String      `db:"recipient"`
	RecipientAddress       null.String      `db:"recipient_address"`
	CarrierType            null.String      `db:"carrier_type"`
	CarrierValue           null.String      `db:"carrier_value"`
	IabOrderID             null.String      `db:"iab_order_id"`
	IabLatestOrderID       null.String      `db:"iab_latest_order_id"`
	IabLatestExpiresDate   null.Time        `db:"iab_latest_expires_date"`
	IapLatestTransactionID null.String      `db:"iap_latest_transaction_id"`
	IapLatestExpiresDate   null.Time        `db:"iap_latest_expires_date"`
	KKIDBoundAt            null.Time        `db:"kkid_bound_at" `
	OriginProvider         null.String      `db:"origin_provider" `
	FamilyID               null.String      `db:"family_id"`
	RevokedAt              null.Time        `db:"revoked_at"`
	Password               null.String      `db:"password"`
	EmailVerifiedAt        null.Time        `db:"email_verified_at"`
	PhoneVerifiedAt        null.Time        `db:"phone_verified_at"`
}
