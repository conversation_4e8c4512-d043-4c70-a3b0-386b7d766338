package ads

import (
	"encoding/json"
	"net/http"

	"github.com/KKTV/kktv-api-v3/consoleapi/internal/errs"
	"github.com/KKTV/kktv-api-v3/consoleapi/internal/rest"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	"github.com/KKTV/kktv-api-v3/pkg/render"
)

type Handler struct {
	repo Repository
}

func NewHandler(repo Repository) *Handler {
	return &Handler{
		repo: repo,
	}
}

func (h *Handler) List(w http.ResponseWriter, r *http.Request) {
	ads, err := h.repo.List()
	if err != nil {
		log.Error("ConsoleAdsHandler: List: repo get ads list fail").Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
		return
	}
	render.JSONOk(w, rest.Ok(ads))
}
func (h *Handler) Update(w http.ResponseWriter, r *http.Request) {
	ads := new(cachemeta.Ads)

	jsdecoder := json.NewDecoder(r.Body)
	err := jsdecoder.Decode(ads)

	if err != nil {
		render.JSONBadRequest(w, rest.Error(errs.InvalidParameter.Code, errs.InvalidParameter.Message))
		return
	}
	err = h.repo.Update(ads)
	if err != nil {
		log.Error("ConsoleAdsHandler: Update: repo update fail").Err(err).Interface("ads", ads).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
		return
	}

	message := "update ads"
	dbmeta.ConsoleLog(r, "meta", message)
	render.JSONOk(w, nil)
}
