//go:generate mockgen -source repository.go -destination repository_mock.go -package ads
package ads

import (
	"errors"
	"sort"

	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
)

type Repository interface {
	List() (event *cachemeta.Ads, err error)
	Update(event *cachemeta.Ads) (err error)
}

type repository struct {
	cacheReader cache.Cacher
	cacheWriter cache.Cacher
}

func NewRepository(redisPoolMeta *datastore.RedisPool) Repository {
	return &repository{
		cacheReader: cache.New(redisPoolMeta.Slave()),
		cacheWriter: cache.New(redisPoolMeta.Master()),
	}

}

func (r *repository) List() (ads *cachemeta.Ads, err error) {
	adsKey := key.GetMetaAds()

	ads = new(cachemeta.Ads)
	err = r.cacheReader.Get(adsKey, ads)
	if errors.Is(err, cache.ErrCacheMiss) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	sort.Sort(ads.Ads)
	return ads, nil
}

func (r *repository) Update(ads *cachemeta.Ads) (err error) {
	adsKey := key.GetMetaAds()
	_, err = r.cacheWriter.SetWithOptions(adsKey, ads, cache.SetOptions{})
	return err
}
