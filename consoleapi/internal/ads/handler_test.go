package ads

import (
	"encoding/json"
	"log"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/KKTV/kktv-api-v3/consoleapi/internal/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/auditing"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	"github.com/go-zoo/bone"
	gomock "github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type HandlerTestSuite struct {
	suite.Suite
	ctrl    *gomock.Controller
	app     *bone.Mux
	repo    *MockRepository
	logRepo *auditing.MockLogRepository
}

func TestHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(HandlerTestSuite))
}

func (suite *HandlerTestSuite) SetupTest() {
	suite.app = bone.New()
	suite.ctrl = gomock.NewController(suite.T())
	suite.repo = NewMockRepository(suite.ctrl)
	handler := &Handler{
		repo: suite.repo,
	}
	suite.app.GetFunc("/v3/console/ads", handler.List)
	suite.app.PutFunc("/v3/console/ads", handler.Update)
}

func (suite *HandlerTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func (suite *HandlerTestSuite) TestGet() {
	mockAd := new(cachemeta.Ads)
	adsItems := []*cachemeta.AdItem{{ID: "123456"}, {ID: "56789"}}
	mockAd.Ads = adsItems
	testcases := []struct {
		name       string
		given      func()
		respCode   int
		assertBody func(body []byte)
	}{
		{
			name: "Success",
			given: func() {
				suite.repo.EXPECT().List().Return(mockAd, nil)
			},
			respCode: http.StatusOK,
			assertBody: func(body []byte) {
				var resp rest.Resp[map[string]interface{}]
				_ = json.Unmarshal(body, &resp)
				ads := resp.Data["ads"].([]interface{})

				suite.Require().Len(ads, 2)
				for i, ID := range []string{"123456", "56789"} {
					suite.Require().Equal(ID, ads[i].(map[string]interface{})["id"])
				}
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			req := httptest.NewRequest(http.MethodGet, "/v3/console/ads", nil)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			log.Println(rr.Body.String())
			suite.Require().Equal(tc.respCode, rr.Code)
			tc.assertBody(rr.Body.Bytes())
		})
	}
}
