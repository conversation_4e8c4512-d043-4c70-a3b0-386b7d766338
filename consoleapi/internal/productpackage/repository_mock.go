// Code generated by MockGen. DO NOT EDIT.
// Source: repository.go

// Package productpackage is a generated GoMock package.
package productpackage

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRepository) Create(pkg dbuser.ProductPackage) (*dbuser.ProductPackage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", pkg)
	ret0, _ := ret[0].(*dbuser.ProductPackage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRepositoryMockRecorder) Create(pkg interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRepository)(nil).Create), pkg)
}

// ListAll mocks base method.
func (m *MockRepository) ListAll() ([]dbuser.ProductPackage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAll")
	ret0, _ := ret[0].([]dbuser.ProductPackage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAll indicates an expected call of ListAll.
func (mr *MockRepositoryMockRecorder) ListAll() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAll", reflect.TypeOf((*MockRepository)(nil).ListAll))
}

// UpdateByFields mocks base method.
func (m *MockRepository) UpdateByFields(id int64, vals map[dbuser.ProductPackageField]any) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateByFields", id, vals)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateByFields indicates an expected call of UpdateByFields.
func (mr *MockRepositoryMockRecorder) UpdateByFields(id, vals interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateByFields", reflect.TypeOf((*MockRepository)(nil).UpdateByFields), id, vals)
}

// UpsertLayoutByFields mocks base method.
func (m *MockRepository) UpsertLayoutByFields(productPackageID int64, vals map[dbuser.PackageLayoutField]any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertLayoutByFields", productPackageID, vals)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertLayoutByFields indicates an expected call of UpsertLayoutByFields.
func (mr *MockRepositoryMockRecorder) UpsertLayoutByFields(productPackageID, vals interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertLayoutByFields", reflect.TypeOf((*MockRepository)(nil).UpsertLayoutByFields), productPackageID, vals)
}
