package productpackage

import (
	"errors"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/consoleapi/internal/errs"
	"github.com/KKTV/kktv-api-v3/consoleapi/internal/rest"
	"github.com/KKTV/kktv-api-v3/pkg/datatype"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/go-playground/validator/v10"
	"github.com/go-zoo/bone"
	"gopkg.in/guregu/null.v3"
)

type Handler struct {
	repo Repository
}

func NewHandler(repo Repository) *Handler {
	return &Handler{
		repo: repo,
	}
}

func (h *Handler) ListAll(w http.ResponseWriter, r *http.Request) {
	all, err := h.repo.ListAll()
	if err != nil {
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
		return
	}

	viewPackages := make([]packageViewModel, len(all))
	for i, ProductPackage := range all {
		viewPackages[i].ConvertFromModel(ProductPackage)
	}

	resp := rest.Ok(map[string]any{
		"items": viewPackages,
	})
	render.JSONOk(w, resp)
}

func (h *Handler) CreatePackage(w http.ResponseWriter, r *http.Request) {
	var req CreatePackageReq
	if err := httpreq.PayloadBinding(&req, r); err != nil {
		handleValidationError(w, err)
		return
	}

	billingProductIds := dbuser.ProductPackageProductIDs{}
	for _, id := range req.BillingProductIds {
		billingProductIds = append(billingProductIds, id)
	}

	productIDs := datatype.JSONIntArray(req.ProductIDs)
	category := dbuser.ProductPackageCategory(req.Category)

	layout := new(dbuser.PackageLayout)
	if req.PackageLayout != nil {
		layout = &dbuser.PackageLayout{
			Title:           null.NewString(req.PackageLayout.Title, req.PackageLayout.Title != ""),
			Subtitle:        null.NewString(req.PackageLayout.Subtitle, req.PackageLayout.Subtitle != ""),
			TextColor:       null.NewString(req.PackageLayout.TextColor, req.PackageLayout.TextColor != ""),
			CTAText:         null.NewString(req.PackageLayout.CTAText, req.PackageLayout.CTAText != ""),
			Description:     null.NewString(req.PackageLayout.Description, req.PackageLayout.Description != ""),
			BackgroundColor: null.NewString(req.PackageLayout.BackgroundColor, req.PackageLayout.BackgroundColor != ""),
			Terms:           null.NewString(req.PackageLayout.Terms, req.PackageLayout.Terms != ""),
			DisplayMoreBtn:  null.NewBool(req.PackageLayout.DisplayMoreBtn, req.PackageLayout.DisplayMoreBtn),
		}
	}

	var targets *dbuser.PackageTargets
	if len(req.Targets) > 0 {
		packageTargets := dbuser.PackageTargets{}
		for _, target := range req.Targets {
			var latestPackages *dbuser.PackageTargetLatestPackages
			if target.Condition.LatestPackages != nil {
				latestPackages = &dbuser.PackageTargetLatestPackages{
					IDs:      target.Condition.LatestPackages.IDs,
					Operator: target.Condition.LatestPackages.Operator,
				}
			}

			packageTarget := dbuser.PackageTarget{
				Condition: dbuser.PackageTargetCondition{
					LatestPackages: latestPackages,
					Identities:     target.Condition.Identities,
				},
				Display: dbuser.PackageTargetDisplay{
					Title:           target.Display.Title,
					Price:           target.Display.Price,
					Duration:        target.Display.Duration,
					Highlight:       target.Display.Highlight,
					ButtonText:      target.Display.ButtonText,
					Description:     target.Display.Description,
					OriginPriceDesc: target.Display.OriginPriceDesc,
					Label:           target.Display.Label,
				},
			}
			packageTargets = append(packageTargets, packageTarget)
		}
		targets = &packageTargets
	}

	newPackage := dbuser.ProductPackage{
		Platform:          req.Platform,
		Price:             req.Price,
		Duration:          req.Duration,
		Title:             req.Title,
		Description:       req.Description,
		ButtonText:        req.ButtonText,
		Label:             null.NewString(req.Label, req.Label != ""),
		CreatedAt:         time.Now(),
		UpdatedAt:         null.TimeFrom(time.Now()),
		Active:            req.Active,
		Highlight:         req.Highlight,
		AutoRenew:         req.AutoRenew,
		Sort:              req.Sort,
		Promotion:         null.NewString(req.Promotion, req.Promotion != ""),
		Category:          category,
		BillingProductIds: &billingProductIds,
		ProductIDs:        productIDs,
		PayDuration:       null.NewString(req.PayDuration, req.PayDuration != ""),
		Layout:            layout,
		Targets:           targets,
	}

	createdPackage, err := h.repo.Create(newPackage)
	if err != nil {
		log.Error("ConsoleProductPackageHandler: CreatePackage: repo Create failed").
			Interface("package", newPackage).Err(err).Send()
		resp := rest.Error(errs.InternalError.Code, errs.InternalError.Message)
		render.JSONInternalServerErr(w, resp)
		return
	}

	viewPackage := packageViewModel{}
	viewPackage.ConvertFromModel(*createdPackage)

	resp := rest.Ok(map[string]any{
		"package": viewPackage,
	})
	render.JSONOk(w, resp)
}

func handleValidationError(w http.ResponseWriter, err error) {
	var validationErrors validator.ValidationErrors
	if errors.As(err, &validationErrors) {
		var errorMsg strings.Builder
		for _, fieldError := range validationErrors {
			errorMsg.WriteString(fieldError.StructNamespace())
			errorMsg.WriteString(": ")
			errorMsg.WriteString(fieldError.Tag())
			errorMsg.WriteString(", ")
		}
		resp := rest.Error(errs.InvalidParameter.Code, strings.TrimSuffix(errorMsg.String(), ", "))
		render.JSONBadRequest(w, resp)
	} else {
		resp := rest.Error(errs.InvalidParameter.Code, err.Error())
		render.JSONBadRequest(w, resp)
	}
}

func (h *Handler) Update(w http.ResponseWriter, r *http.Request) {
	id := bone.GetValue(r, "productPackageID")
	packageID, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		render.JSONBadRequest(w, rest.Error(errs.ProductPackageNotFound.Code, errs.ProductPackageNotFound.Message))
		return
	}

	var req updateReq
	if err := httpreq.PayloadBinding(&req, r); err != nil {
		handleValidationError(w, err)
		return
	}

	packageValues, layoutValues := req.getUpdateFields()
	if affected, err := h.repo.UpdateByFields(packageID, packageValues); err != nil {
		log.Error("ConsoleProductPackageHandler: Update: repo UpdateByFields failed").Err(err).
			Int64("product_package_id", packageID).Interface("package", req).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
		return
	} else if !affected {
		render.JSONNotFound(w, rest.Error(errs.ProductPackageNotFound.Code, errs.ProductPackageNotFound.Message))
		return
	}

	if len(layoutValues) > 0 {
		if err := h.repo.UpsertLayoutByFields(packageID, layoutValues); err != nil {
			log.Error("ConsoleProductPackageHandler: Update: repo UpsertLayoutByFields failed").
				Int64("package_id", packageID).Interface("layout", layoutValues).Err(err).Send()
			render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
			return
		}
	}

	render.JSONOk(w, rest.Ok(any(nil)))
}
