package productpackage

import (
	_ "embed"
	"testing"

	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/testutils"
	"github.com/stretchr/testify/suite"
)

type RepositoryTestSuite struct {
	suite.Suite
	testContainer *testutils.Container
	repo          Repository
	db            database.DB
}

func TestRepositoryIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(RepositoryTestSuite))
}

func (suite *RepositoryTestSuite) SetupTest() {
	suite.repo = &repository{
		dbReader: suite.db,
		dbWriter: suite.db,
	}

}

func (suite *RepositoryTestSuite) TearDownAllSuite() {
	if err := suite.testContainer.Close(); err != nil {
		suite.Fail("fail to terminate test container", err)
	}
}

func (suite *RepositoryTestSuite) SetupSuite() {
	testContainer, dbPool := testutils.SetupTestDatabase(testutils.DBUser)
	suite.testContainer = testContainer

	suite.db = dbPool.Master().Unsafe()
}

//go:embed testdata/repository/CreateProductPackage.sql
var dataForCreateProductPackage string

func (suite *RepositoryTestSuite) givenProductPackages() {
	tables := []string{"package_layout", "product_packages"}
	for _, table := range tables {
		if _, err := suite.db.Exec("DELETE FROM " + table); err != nil {
			suite.Require().Fail("fail to delete tables", err)
		}
	}

	if _, err := suite.db.Exec(dataForCreateProductPackage); err != nil {
		suite.Require().Fail("fail to insert test data", err)
	}
}

func (suite *RepositoryTestSuite) TestListAll() {
	suite.givenProductPackages()

	testcases := []struct {
		name string
		then func([]dbuser.ProductPackage, error)
	}{
		{
			name: "List all product packages",
			then: func(result []dbuser.ProductPackage, err error) {
				suite.Require().NoError(err)
				suite.Require().Len(result, 2)
				suite.Require().Equal(1, result[0].ID)
				suite.Require().Equal("come buy", result[0].Layout.CTAText.ValueOrZero())

				suite.Require().Equal(2, result[1].ID)

			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			result, err := suite.repo.ListAll()
			tc.then(result, err)
		})
	}
}

func (suite *RepositoryTestSuite) TestUpsertLayoutByFields() {
	const (
		packageIDWithExistedLayout    = 1
		packageIDWithoutExistedLayout = 2
		existedLayoutID               = int64(100)
	)

	suite.givenProductPackages()

	testcases := []struct {
		name             string
		productPackageID int64
		values           map[dbuser.PackageLayoutField]interface{}
		wantErr          bool
		assert           func()
	}{
		{
			name:             "insert WHEN product package id not exists",
			productPackageID: packageIDWithoutExistedLayout,
			values: map[dbuser.PackageLayoutField]interface{}{
				dbuser.PackageLayoutFieldTitle:       "new title",
				dbuser.PackageLayoutFieldCTAText:     "new cta text",
				dbuser.PackageLayoutFieldDescription: "new description",
			},
			assert: func() {
				layout := suite.queryLayoutByProductPackageID(packageIDWithoutExistedLayout)

				suite.Require().Equal("new title", layout.Title.ValueOrZero())
				suite.Require().Equal("new cta text", layout.CTAText.ValueOrZero())
				suite.Require().Equal("new description", layout.Description.ValueOrZero())
				suite.Require().True(layout.Subtitle.IsZero())
				suite.Require().False(layout.DisplayMoreBtn.ValueOrZero())
			},
		},
		{
			name:             "update WHEN target layout exists",
			productPackageID: packageIDWithExistedLayout,
			values: map[dbuser.PackageLayoutField]interface{}{
				dbuser.PackageLayoutFieldDisplayMoreBtn: true,
				dbuser.PackageLayoutFieldCTAText:        "new cta text",
			},
			assert: func() {
				layout := suite.queryLayoutByProductPackageID(packageIDWithExistedLayout)

				suite.Require().True(layout.DisplayMoreBtn.ValueOrZero())
				suite.Require().Equal("new cta text", layout.CTAText.ValueOrZero())
				// original value should not be changed
				suite.Require().Equal(existedLayoutID, layout.ID.Int64)
				suite.Require().Equal("test layout", layout.Title.ValueOrZero())
				suite.Require().Equal("hihi", layout.Description.ValueOrZero())

			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			err := suite.repo.UpsertLayoutByFields(tc.productPackageID, tc.values)
			suite.Require().True(tc.wantErr == (err != nil))
			if tc.assert != nil {
				tc.assert()
			}
		})
	}
}

func (suite *RepositoryTestSuite) queryLayoutByProductPackageID(productPackageID int64) *dbuser.PackageLayout {
	var layout dbuser.PackageLayout
	err := suite.db.Get(&layout, `SELECT * FROM package_layout WHERE product_packages_id = $1`, productPackageID)
	suite.Require().NoError(err)
	return &layout
}
