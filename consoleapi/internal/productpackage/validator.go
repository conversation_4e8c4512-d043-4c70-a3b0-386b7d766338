package productpackage

import (
	"github.com/go-playground/validator/v10"
)

// Validator 接口
type Validator interface {
	Validate(req interface{}) error
}

type ProductPackageValidator struct {
	validate *validator.Validate
}

func NewProductPackageValidator() *ProductPackageValidator {
	validate := validator.New()
	validate.RegisterValidation("required_if_items", requiredIfItems)
	return &ProductPackageValidator{
		validate: validate,
	}
}

func (v *ProductPackageValidator) Validate(req interface{}) error {
	return v.validate.Struct(req)
}

// requiredIfItems is a custom validator that checks if a field is required when Items is not empty
func requiredIfItems(fl validator.FieldLevel) bool {
	// TODO: add validator logic
	return true
}
