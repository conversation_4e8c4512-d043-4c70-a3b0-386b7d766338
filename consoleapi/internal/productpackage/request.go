package productpackage

import (
	"github.com/KKTV/kktv-api-v3/pkg/datatype"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"gopkg.in/guregu/null.v3"
)

type IncExcOperator = dbuser.IncExcOperator

type CreatePackageReq struct {
	updateReq
	Price       string `json:"price"`
	Duration    string `json:"duration"`
	Title       string `json:"title"`
	Description string `json:"description"`
	ButtonText  string `json:"button_text"`
	Label       string `json:"label"`
	Highlight   string `json:"highlight"`
	Promotion   string `json:"promotion"`
}

type updateReqTargetCondition struct {
	LatestPackages *updateReqLatestPackages `json:"latest_packages" validate:"omitempty"`
	Identities     []string                 `json:"identities"`
}

type updateReqLatestPackages struct {
	IDs      []int64        `json:"ids" validate:"required,min=1"`
	Operator IncExcOperator `json:"operator" validate:"required,oneof=include exclude"`
}

type updateReqTargetDisplay struct {
	Title           string      `json:"title" validate:"required,min=1,max=50"`
	Price           float64     `json:"price" validate:"required,min=0"`
	Duration        string      `json:"duration"`
	Highlight       string      `json:"highlight"`
	ButtonText      string      `json:"button_text" validate:"required,min=1,max=30"`
	Description     string      `json:"description"`
	OriginPriceDesc string      `json:"origin_price_desc"`
	Label           null.String `json:"label"`
}

type updateReqPackageTarget struct {
	Condition updateReqTargetCondition `json:"condition" validate:"required"`
	Display   updateReqTargetDisplay   `json:"display" validate:"required"`
}

type updatePackageLayout struct {
	Title           string `json:"title" validate:"required,min=1,max=50"`
	Subtitle        string `json:"subtitle"`
	TextColor       string `json:"text_color"`
	Description     string `json:"description" validate:"required,min=1,max=2000"`
	BackgroundColor string `json:"background_color"`
	Terms           string `json:"terms" validate:"required,min=1"` //方案條款
	DisplayMoreBtn  bool   `json:"display_more_btn"`
	CTAText         string `json:"cta_text"`
}

type updateReq struct {
	Platform          string                   `json:"platform" validate:"required,oneof=web ios android campaign"`
	Active            bool                     `json:"active"`
	PayDuration       string                   `json:"pay_duration" validate:"required"`
	AutoRenew         bool                     `json:"auto_renew"`
	ProductIDs        []int                    `json:"product_ids"`
	BillingProductIds []string                 `json:"billing_product_ids"`
	Sort              int                      `json:"sort"`
	Category          []string                 `json:"category"`
	PackageLayout     *updatePackageLayout     `json:"package_layout"`
	Targets           []updateReqPackageTarget `json:"targets" validate:"dive"`
}

func (r *updateReq) getUpdateFields() (map[dbuser.ProductPackageField]any, map[dbuser.PackageLayoutField]any) {
	billingProductIDs := dbuser.ProductPackageProductIDs(r.BillingProductIds)
	category := dbuser.ProductPackageCategory(r.Category)
	pkg := map[dbuser.ProductPackageField]any{
		dbuser.ProductPackageFieldPlatform:          r.Platform,
		dbuser.ProductPackageFieldActive:            r.Active,
		dbuser.ProductPackageFieldAutoRenew:         r.AutoRenew,
		dbuser.ProductPackageFieldProductIds:        datatype.JSONIntArray(r.ProductIDs),
		dbuser.ProductPackageFieldBillingProductIds: &billingProductIDs,
		dbuser.ProductPackageFieldSort:              r.Sort,
		dbuser.ProductPackageFieldCategory:          &category,
		dbuser.ProductPackageFieldPayDuration:       r.PayDuration,
	}

	if len(r.Targets) > 0 {
		var targets dbuser.PackageTargets
		for _, target := range r.Targets {
			var latestPackages *dbuser.PackageTargetLatestPackages
			if target.Condition.LatestPackages != nil {
				latestPackages = &dbuser.PackageTargetLatestPackages{
					IDs:      target.Condition.LatestPackages.IDs,
					Operator: target.Condition.LatestPackages.Operator,
				}
			}
			packageTarget := dbuser.PackageTarget{
				Condition: dbuser.PackageTargetCondition{
					Identities:     target.Condition.Identities,
					LatestPackages: latestPackages,
				},
				Display: dbuser.PackageTargetDisplay{
					Title:           target.Display.Title,
					Price:           target.Display.Price,
					Duration:        target.Display.Duration,
					Highlight:       target.Display.Highlight,
					ButtonText:      target.Display.ButtonText,
					Description:     target.Display.Description,
					OriginPriceDesc: target.Display.OriginPriceDesc,
					Label:           target.Display.Label,
				},
			}
			targets = append(targets, packageTarget)
		}
		pkg[dbuser.ProductPackageFieldTargets] = &targets
	}

	var layout map[dbuser.PackageLayoutField]any = nil
	if r.PackageLayout != nil {
		layout = map[dbuser.PackageLayoutField]any{
			dbuser.PackageLayoutFieldTitle:           r.PackageLayout.Title,
			dbuser.PackageLayoutFieldSubtitle:        r.PackageLayout.Subtitle,
			dbuser.PackageLayoutFieldTextColor:       r.PackageLayout.TextColor,
			dbuser.PackageLayoutFieldDescription:     r.PackageLayout.Description,
			dbuser.PackageLayoutFieldBackgroundColor: r.PackageLayout.BackgroundColor,
			dbuser.PackageLayoutFieldTerms:           r.PackageLayout.Terms,
			dbuser.PackageLayoutFieldDisplayMoreBtn:  r.PackageLayout.DisplayMoreBtn,
			dbuser.PackageLayoutFieldCTAText:         r.PackageLayout.CTAText,
		}
	}
	return pkg, layout
}
