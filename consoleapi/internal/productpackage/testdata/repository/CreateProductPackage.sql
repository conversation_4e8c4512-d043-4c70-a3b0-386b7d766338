INSERT INTO public.product_packages (id, platform, price, duration, title, description, button_text, "label",
                                     created_at,
                                     updated_at, product_ids, active, highlight, auto_renew, sort, promotion, info,
                                     category, pay_duration, billing_product_ids)
VALUES (1, 'web', 149.00, '', 'test package', '', '', NULL, '2020-07-21 18:19:23.000', '2023-05-10 15:50:25.000', '[150]', true, '',
        true, 202, NULL,
        '{"Guest,Expired-首購,Expired-非首購,FreeTrial,Vip,Prime": {"label": "即將下架", "price": 447, "title": "v.信用卡每季扣", "duration": "季", "displayTo": ["Guest", "Expired-首購", "Expired-非首購", "FreeTrial", "Vip", "Prime"], "highlight": "每季加送 10 天", "button_text": "立即訂閱", "description": "", "originPriceDesc": "V3 Product"}}',
        '["單人獨享"]', '季繳', '[]'),
       (2, 'campaign', 0.00, '', '', '', '', NULL, '2024-03-28 15:50:20.000', '2024-05-24 11:00:05.000', '[243]', true,
        '', true, 1, NULL,
        '{"Guest,Expired-首購,Expired-非首購,FreeTrial,Vip,Prime": {"label": "超優惠", "price": 204, "title": "24FUN月繳", "duration": "", "displayTo": ["Guest", "Expired-首購", "Expired-非首購", "FreeTrial", "Vip", "Prime"], "highlight": "", "button_text": "買", "description": "測promocode", "originPriceDesc": ""}}',
        '["單人獨享"]', '月繳', '[]');



INSERT INTO public.package_layout (id, product_packages_id, title, subtitle, text_color, cta_text, description, image,
                                   background_color,
                                   background_image, terms, display_more_btn, created_at)
VALUES (100, 1, 'test layout', 'sub title', '#fffff', 'come buy', 'hihi', '', '', '', '', true,
        '2020-04-29 18:19:23.000');
