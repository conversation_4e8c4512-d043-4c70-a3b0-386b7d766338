package productpackage

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/consoleapi/internal/rest"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/go-zoo/bone"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"gopkg.in/guregu/null.v3"
)

type HandlerTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller

	mockRepo *MockRepository
	app      *bone.Mux
}

func TestHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(HandlerTestSuite))
}

func (suite *HandlerTestSuite) SetupTest() {
	suite.app = bone.New()
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockRepo = NewMockRepository(suite.ctrl)

	hlr := &Handler{
		repo: suite.mockRepo,
	}
	suite.app.GetFunc("/v3/console/product_packages", hlr.ListAll)
	suite.app.PostFunc("/v3/console/product_packages", hlr.CreatePackage)
	suite.app.PutFunc("/v3/console/packages/:productPackageID", hlr.Update)
}

func (suite *HandlerTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

type listResp struct {
	Err  rest.Err `json:"error"`
	Data struct {
		Items []packageViewModel `json:"items"`
	}
}

func (suite *HandlerTestSuite) TestListAll() {
	testcases := []struct {
		name       string
		given      func()
		thenAssert func(code int, body []byte)
	}{
		{
			name: "WHEN found 1 product package in repository, THEN return 200 OK and the fields of the product package",
			given: func() {
				suite.mockRepo.EXPECT().ListAll().Return([]dbuser.ProductPackage{
					{
						ID:       1,
						Platform: "web",
						Targets: &dbuser.PackageTargets{
							{
								Condition: dbuser.PackageTargetCondition{
									Identities: []string{"Expired首購"},
								},
								Display: dbuser.PackageTargetDisplay{
									Title:      "Hi Josie",
									ButtonText: "Come buy",
									Price:      149,
								},
							},
						},
						Category:    dbuser.ProductPackageCategory{"單人獨享"},
						PayDuration: null.StringFrom("月繳"),
						Layout: &dbuser.PackageLayout{
							ID:                sql.NullInt64{Int64: 12, Valid: true},
							ProductPackagesID: sql.NullInt64{Int64: 1, Valid: true},
						},
					},
				}, nil)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(200, code)
				resp := &listResp{}
				suite.NoError(json.Unmarshal(body, resp))
				suite.Empty(resp.Err)
				suite.Len(resp.Data.Items, 1)
				suite.Equal(1, resp.Data.Items[0].ID)
				suite.Equal("web", resp.Data.Items[0].Platform)
				suite.Equal("月繳", *resp.Data.Items[0].PayDuration)
				suite.Contains("單人獨享", resp.Data.Items[0].Category[0])
				suite.Contains("Hi Josie", resp.Data.Items[0].Targets[0].Display.Title)
				suite.Equal(float64(149), resp.Data.Items[0].Targets[0].Display.Price)
			},
		},
		{
			name: "WHEN no product package found in repository, THEN return 200 OK with empty items",
			given: func() {
				suite.mockRepo.EXPECT().ListAll().Return([]dbuser.ProductPackage{}, nil)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(200, code)
				resp := &listResp{}
				suite.NoError(json.Unmarshal(body, resp))
				suite.Empty(resp.Err)
				suite.Len(resp.Data.Items, 0)
			},
		},
		{
			name: "WHEN package layout is null, THEN return 200 OK with null layout",
			given: func() {
				suite.mockRepo.EXPECT().ListAll().Return([]dbuser.ProductPackage{
					{
						ID:          2,
						Platform:    "web",
						Category:    dbuser.ProductPackageCategory{"多人共享"},
						PayDuration: null.StringFrom("年繳"),
					},
				}, nil)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(200, code)
				resp := &listResp{}
				suite.NoError(json.Unmarshal(body, resp))
				suite.Empty(resp.Err)
				suite.Len(resp.Data.Items, 1)
				suite.Equal(2, resp.Data.Items[0].ID)
				suite.Equal("web", resp.Data.Items[0].Platform)
				suite.Equal("年繳", *resp.Data.Items[0].PayDuration)
				suite.Contains("多人共享", resp.Data.Items[0].Category[0])
				suite.Nil(resp.Data.Items[0].PackageLayout)
			},
		},
		{
			name: "WHEN multiple product packages found, THEN return 200 OK with all items",
			given: func() {
				suite.mockRepo.EXPECT().ListAll().Return([]dbuser.ProductPackage{
					{
						ID:       4,
						Platform: "web",
						Targets: &dbuser.PackageTargets{
							{
								Condition: dbuser.PackageTargetCondition{
									Identities: []string{"Discounted"},
								},
								Display: dbuser.PackageTargetDisplay{
									Title:      "Special Offer",
									ButtonText: "Buy Now",
									Price:      99,
								},
							},
						},
						Category:    dbuser.ProductPackageCategory{"促銷"},
						PayDuration: null.StringFrom("月繳"),
						Layout: &dbuser.PackageLayout{
							ID:                sql.NullInt64{Int64: 34, Valid: true},
							ProductPackagesID: sql.NullInt64{Int64: 4, Valid: true},
							Title:             null.StringFrom("Promo"),
							TextColor:         null.StringFrom("#FFFFFF"),
							DisplayMoreBtn:    null.BoolFrom(true),
							Image:             null.StringFrom("test/josiesmile.jpg"),
							BackgroundImage:   null.StringFrom("test/josiebackground.jpg"),
						},
					},
					{
						ID:       5,
						Platform: "web",
						Targets: &dbuser.PackageTargets{
							{
								Condition: dbuser.PackageTargetCondition{
									Identities: []string{"Regular"},
								},
								Display: dbuser.PackageTargetDisplay{
									Title:      "Standard Package",
									ButtonText: "Subscribe",
									Price:      199,
								},
							},
						},
						Category:    dbuser.ProductPackageCategory{"標準"},
						PayDuration: null.StringFrom("年繳"),
						Layout: &dbuser.PackageLayout{
							ID:                sql.NullInt64{Int64: 56, Valid: true},
							ProductPackagesID: sql.NullInt64{Int64: 5, Valid: true},
							Title:             null.StringFrom("Standard"),
							Subtitle:          null.String{},
							TextColor:         null.StringFrom("#000000"),
						},
					},
				}, nil)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(200, code)
				resp := &listResp{}
				suite.NoError(json.Unmarshal(body, resp))
				suite.Empty(resp.Err)
				suite.Len(resp.Data.Items, 2)

				// Assert first product package
				suite.Equal(4, resp.Data.Items[0].ID)
				suite.Equal("web", resp.Data.Items[0].Platform)
				suite.Equal("月繳", *resp.Data.Items[0].PayDuration)
				suite.Contains("促銷", resp.Data.Items[0].Category[0])
				suite.Contains("Special Offer", resp.Data.Items[0].Targets[0].Display.Title)
				suite.Equal(float64(99), resp.Data.Items[0].Targets[0].Display.Price)
				//check layout.image, layout.background_image should contain the domain
				suite.Equal("https://test-images.kktv.com.tw/test/josiesmile.jpg", resp.Data.Items[0].PackageLayout.Image)
				suite.Equal("https://test-images.kktv.com.tw/test/josiebackground.jpg", resp.Data.Items[0].PackageLayout.BackgroundImage)

				// Assert second product package
				suite.Equal(5, resp.Data.Items[1].ID)
				suite.Equal("web", resp.Data.Items[1].Platform)
				suite.Equal("年繳", *resp.Data.Items[1].PayDuration)
				suite.Contains("標準", resp.Data.Items[1].Category[0])
				suite.Contains("Standard Package", resp.Data.Items[1].Targets[0].Display.Title)
				suite.Equal(float64(199), resp.Data.Items[1].Targets[0].Display.Price)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			req := httptest.NewRequest("GET", "/v3/console/product_packages", nil)
			w := httptest.NewRecorder()
			suite.app.ServeHTTP(w, req)

			body := w.Body.Bytes()
			log.Debug("body").RawJSON("body", body).Send()
			tc.thenAssert(w.Code, body)
		})
	}
}

func (suite *HandlerTestSuite) TestCreatePackage() {
	testcases := []struct {
		name       string
		given      func()
		reqBody    CreatePackageReq
		thenAssert func(code int, body []byte)
	}{
		{
			name: "WHEN valid create package request THEN return 200 OK and data is valid",
			given: func() {
				suite.mockRepo.EXPECT().Create(gomock.Any()).DoAndReturn(func(insert dbuser.ProductPackage) (*dbuser.ProductPackage, error) {
					suite.Equal("web", insert.Platform)
					suite.Equal("月繳", insert.PayDuration.String)
					suite.NotNil(insert.Targets)
					suite.Len(*insert.Targets, 1)
					targeting := (*insert.Targets)[0]
					suite.Equal("Josie comes", targeting.Display.Title)
					suite.Equal(float64(149), targeting.Display.Price)
					suite.Equal("30天", targeting.Display.Duration)
					suite.Equal("199NT", targeting.Display.OriginPriceDesc)

					suite.ElementsMatch([]string{"test-category"}, insert.Category)
					suite.NotNil(insert.Layout)
					suite.Equal("Sample Title", insert.Layout.Title.String)
					suite.Equal("#FFFFFF", insert.Layout.TextColor.String)
					suite.Equal(true, insert.Layout.DisplayMoreBtn.Bool)

					inserted := insert
					inserted.ID = 1
					inserted.Layout.ID = sql.NullInt64{Int64: 1, Valid: true}
					return &inserted, nil
				})
			},
			reqBody: CreatePackageReq{
				updateReq: updateReq{
					Platform:    "web",
					PayDuration: "月繳",
					Category:    []string{"test-category"},
					// 使用新的 Targets 欄位替代已棄用的 Items 欄位
					Targets: []updateReqPackageTarget{
						{
							Condition: updateReqTargetCondition{
								LatestPackages: &updateReqLatestPackages{
									IDs:      []int64{1},
									Operator: "include",
								},
							},
							Display: updateReqTargetDisplay{
								Title:           "Josie comes",
								Price:           149,
								Duration:        "30天",
								Highlight:       "",
								ButtonText:      "Click Here",
								Description:     "Sample Description",
								OriginPriceDesc: "199NT",
							},
						},
					},
					PackageLayout: &updatePackageLayout{
						Title:           "Sample Title",
						Subtitle:        "Sample Subtitle",
						TextColor:       "#FFFFFF",
						CTAText:         "Click Here",
						Description:     "Sample Description",
						BackgroundColor: "#000000",
						Terms:           "Terms and conditions apply",
						DisplayMoreBtn:  true,
					},
				},
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(200, code)
				resp := make(map[string]interface{})
				suite.NoError(json.Unmarshal(body, &resp))
				data := resp["data"].(map[string]interface{})
				pkg := data["package"].(map[string]interface{})

				// 檢查 package_layout 是否為 nil
				suite.NotNil(pkg["package_layout"], "package_layout should not be nil")
				pkgLayout := pkg["package_layout"].(map[string]interface{})

				// assert product package
				suite.Equal(1, int(pkg["id"].(float64)))
				suite.Equal("web", pkg["platform"].(string))
				suite.Equal("月繳", pkg["pay_duration"].(string))

				// assert package layout
				suite.Equal("Sample Title", pkgLayout["title"].(string))
				suite.Equal("Sample Subtitle", pkgLayout["subtitle"].(string))
				suite.Equal("#FFFFFF", pkgLayout["text_color"].(string))
				suite.Equal("Click Here", pkgLayout["cta_text"].(string))
				suite.Equal("Sample Description", pkgLayout["description"].(string))
				suite.Equal("#000000", pkgLayout["background_color"].(string))
				suite.Equal("Terms and conditions apply", pkgLayout["terms"].(string))
				suite.Equal(true, pkgLayout["display_more_btn"].(bool))
			},
		},
		{
			name: "WHEN valid create package request with nil PackageLayout THEN return 200 OK",
			given: func() {
				suite.mockRepo.EXPECT().Create(gomock.Any()).Return(&dbuser.ProductPackage{
					ID:          1,
					Platform:    "web",
					PayDuration: null.NewString("月繳", true),
					Category:    dbuser.ProductPackageCategory{"test-category"},
					CreatedAt:   time.Now(),
					UpdatedAt:   null.Time{Time: time.Now(), Valid: true},
					Layout:      nil,
				}, nil)
			},
			reqBody: CreatePackageReq{
				updateReq: updateReq{
					PayDuration:   "月繳",
					Platform:      "web",
					Category:      []string{"test-category"},
					PackageLayout: nil,
				},
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(200, code)
				resp := make(map[string]interface{})
				suite.NoError(json.Unmarshal(body, &resp))
				data := resp["data"].(map[string]interface{})
				pkg := data["package"].(map[string]interface{})

				suite.Equal(1, int(pkg["id"].(float64)))
				suite.Equal("web", pkg["platform"].(string))
				suite.Equal("月繳", pkg["pay_duration"].(string))
				suite.Nil(pkg["package_layout"])
			},
		},
		{
			name: "WHEN invalid create package request THEN return 400 Bad Request",
			given: func() {
			},
			reqBody: CreatePackageReq{
				updateReq: updateReq{
					Platform: "web",
					Category: nil,
				},
				Price:       "0",
				Title:       "",
				Description: "",
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(400, code)
				resp := make(map[string]interface{})
				suite.NoError(json.Unmarshal(body, &resp))
				suite.Contains(resp["error"].(map[string]interface{})["message"], "CreatePackageReq.updateReq")
			},
		},
		{
			name: "WHEN create package request with Targets field THEN return 200 OK and targets are properly saved",
			given: func() {
				suite.mockRepo.EXPECT().Create(gomock.Any()).DoAndReturn(func(insert dbuser.ProductPackage) (*dbuser.ProductPackage, error) {
					// 驗證基本欄位
					suite.Equal("web", insert.Platform)
					suite.Equal("月繳", insert.PayDuration.String)

					// 驗證 Targets 欄位
					suite.NotNil(insert.Targets)
					suite.Len(*insert.Targets, 1)
					target := (*insert.Targets)[0]

					// 驗證 Condition
					suite.NotNil(target.Condition.LatestPackages)
					suite.ElementsMatch([]int64{1, 2}, target.Condition.LatestPackages.IDs)
					suite.Equal(dbuser.IncExcOperatorInclude, target.Condition.LatestPackages.Operator)

					suite.Equal("特別優惠", target.Display.Title)
					suite.Equal(float64(188), target.Display.Price)
					suite.Equal("30天", target.Display.Duration)
					suite.Equal("限時", target.Display.Highlight)
					suite.Equal("立即訂閱", target.Display.ButtonText)
					suite.Equal("特別方案說明", target.Display.Description)
					suite.Equal("原價199", target.Display.OriginPriceDesc)

					inserted := insert
					inserted.ID = 2
					return &inserted, nil
				})
			},
			reqBody: CreatePackageReq{
				updateReq: updateReq{
					Platform:    "web",
					PayDuration: "月繳",
					Category:    []string{"限時優惠"},
					Targets: []updateReqPackageTarget{
						{
							Condition: updateReqTargetCondition{
								LatestPackages: &updateReqLatestPackages{
									IDs:      []int64{1, 2},
									Operator: "include",
								},
							},
							Display: updateReqTargetDisplay{
								Title:           "特別優惠",
								Price:           188,
								Duration:        "30天",
								Highlight:       "限時",
								ButtonText:      "立即訂閱",
								Description:     "特別方案說明",
								OriginPriceDesc: "原價199",
							},
						},
					},
				},
				Price:       "149",
				Duration:    "30天",
				Title:       "基本方案",
				Description: "基本方案說明",
				ButtonText:  "訂閱",
				Highlight:   "優惠",
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(200, code)
				resp := make(map[string]interface{})
				suite.NoError(json.Unmarshal(body, &resp))
				suite.Nil(resp["error"])
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			reqBody, err := json.Marshal(tc.reqBody)
			suite.NoError(err)

			req := httptest.NewRequest("POST", "/v3/console/product_packages", bytes.NewBuffer(reqBody))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()
			suite.app.ServeHTTP(w, req)

			body := w.Body.Bytes()
			log.Debug("body").RawJSON("body", body).Send()
			tc.thenAssert(w.Code, body)
		})
	}
}

func (suite *HandlerTestSuite) TestUpdatePackage() {
	const (
		id = int64(429)
	)

	testcases := []struct {
		name       string
		given      func(req *updateReq)
		reqBody    updateReq
		returnCode int
		expectBody func(body []byte)
	}{
		{
			name: "WHEN valid update package request THEN repository should be updated and return 200 OK",
			given: func(req *updateReq) {
				suite.mockRepo.EXPECT().UpdateByFields(id, gomock.Any()).Return(true, nil).
					Do(func(id int64, fields map[dbuser.ProductPackageField]interface{}) {
						suite.assertPackageUpdateFieldsMatched(req, fields)
					})
				suite.mockRepo.EXPECT().UpsertLayoutByFields(id, gomock.Any()).Return(nil).
					Do(func(id int64, vals map[dbuser.PackageLayoutField]any) {
						suite.assertLayoutUpdateFieldsMatched(req, vals)
					})
			},
			reqBody: updateReq{
				Platform:    "web",
				ProductIDs:  []int{106, 429},
				Category:    []string{"test-category"},
				AutoRenew:   true,
				Sort:        5,
				PayDuration: "月繳",
				// 使用新的 Targets 欄位替代已棄用的 Items 欄位
				Targets: []updateReqPackageTarget{
					{
						Condition: updateReqTargetCondition{
							LatestPackages: &updateReqLatestPackages{
								IDs:      []int64{1, 2},
								Operator: "include",
							},
						},
						Display: updateReqTargetDisplay{
							Title:           "Sample Title",
							Price:           299,
							Duration:        "月繳",
							Highlight:       "Sample Highlight",
							ButtonText:      "Buy Now",
							Description:     "Sample Description",
							OriginPriceDesc: "原價 $299",
						},
					},
				},
				PackageLayout: &updatePackageLayout{
					Title:           "Sample Title",
					Subtitle:        "Sample Subtitle",
					TextColor:       "#FFFFFF",
					CTAText:         "Click Here",
					Description:     "Sample Description",
					BackgroundColor: "#000000",
					Terms:           "Terms and conditions apply",
					DisplayMoreBtn:  true,
				},
			},
			returnCode: 200,
			expectBody: func(body []byte) {},
		},
		{
			name: "WHEN update package request with Targets field THEN repository should be updated and return 200 OK",
			given: func(req *updateReq) {
				suite.mockRepo.EXPECT().UpdateByFields(id, gomock.Any()).Return(true, nil).
					Do(func(id int64, fields map[dbuser.ProductPackageField]interface{}) {
						suite.assertPackageUpdateFieldsMatched(req, fields)

						// 驗證 Targets 欄位是否已正確處理
						if targetsField, ok := fields[dbuser.ProductPackageFieldTargets]; ok {
							targets := *targetsField.(*dbuser.PackageTargets)
							suite.Len(targets, 1)

							target := targets[0]
							reqTarget := req.Targets[0]

							// 驗證 Condition
							suite.NotNil(target.Condition.LatestPackages)
							suite.ElementsMatch(reqTarget.Condition.LatestPackages.IDs, target.Condition.LatestPackages.IDs)
							suite.Equal(reqTarget.Condition.LatestPackages.Operator, target.Condition.LatestPackages.Operator)

							// 驗證 Display
							suite.Equal(reqTarget.Display.Title, target.Display.Title)
							suite.Equal(reqTarget.Display.Price, target.Display.Price)
							suite.Equal(reqTarget.Display.Duration, target.Display.Duration)
							suite.Equal(reqTarget.Display.Highlight, target.Display.Highlight)
							suite.Equal(reqTarget.Display.ButtonText, target.Display.ButtonText)
							suite.Equal(reqTarget.Display.Description, target.Display.Description)
							suite.Equal(reqTarget.Display.OriginPriceDesc, target.Display.OriginPriceDesc)
						}
					})
			},
			reqBody: updateReq{
				Platform:    "web",
				ProductIDs:  []int{106, 429},
				Category:    []string{"limited-offer"},
				PayDuration: "月繳",
				Targets: []updateReqPackageTarget{
					{
						Condition: updateReqTargetCondition{
							LatestPackages: &updateReqLatestPackages{
								IDs:      []int64{1, 2, 3},
								Operator: "exclude",
							},
						},
						Display: updateReqTargetDisplay{
							Title:           "更新優惠",
							Price:           399,
							Duration:        "60天",
							Highlight:       "限時優惠",
							ButtonText:      "更新訂閱",
							Description:     "更新方案說明",
							OriginPriceDesc: "原價299",
						},
					},
				},
			},
			returnCode: 200,
			expectBody: func(body []byte) {},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given(&tc.reqBody)

			reqBody, err := json.Marshal(tc.reqBody)
			suite.NoError(err)

			req := httptest.NewRequest(http.MethodPut, fmt.Sprintf("/v3/console/packages/%d", id), bytes.NewBuffer(reqBody))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()
			suite.app.ServeHTTP(w, req)

			body := w.Body.Bytes()
			log.Debug("body").RawJSON("body", body).Send()
			suite.Equal(tc.returnCode, w.Code)

			if tc.expectBody != nil {
				tc.expectBody(body)
			}
		})
	}
}

func (suite *HandlerTestSuite) assertLayoutUpdateFieldsMatched(req *updateReq, vals map[dbuser.PackageLayoutField]any) {
	if req.PackageLayout == nil {
		suite.Nil(vals)
		return
	}

	layoutReq := req.PackageLayout
	suite.Equal(layoutReq.Title, vals[dbuser.PackageLayoutFieldTitle])
	suite.Equal(layoutReq.Subtitle, vals[dbuser.PackageLayoutFieldSubtitle])
	suite.Equal(layoutReq.TextColor, vals[dbuser.PackageLayoutFieldTextColor])
	suite.Equal(layoutReq.CTAText, vals[dbuser.PackageLayoutFieldCTAText])
	suite.Equal(layoutReq.Description, vals[dbuser.PackageLayoutFieldDescription])
	suite.Equal(layoutReq.BackgroundColor, vals[dbuser.PackageLayoutFieldBackgroundColor])
	suite.Equal(layoutReq.Terms, vals[dbuser.PackageLayoutFieldTerms])
	suite.Equal(layoutReq.DisplayMoreBtn, vals[dbuser.PackageLayoutFieldDisplayMoreBtn])
}

func (suite *HandlerTestSuite) assertPackageUpdateFieldsMatched(req *updateReq, fields map[dbuser.ProductPackageField]interface{}) {
	suite.Equal(req.Platform, fields[dbuser.ProductPackageFieldPlatform])
	suite.ElementsMatch(req.Category, *(fields[dbuser.ProductPackageFieldCategory].(*dbuser.ProductPackageCategory)))
	suite.ElementsMatch(req.ProductIDs, fields[dbuser.ProductPackageFieldProductIds])
	suite.Equal(req.AutoRenew, fields[dbuser.ProductPackageFieldAutoRenew])
	suite.Equal(req.Sort, fields[dbuser.ProductPackageFieldSort])
	suite.Equal(req.PayDuration, fields[dbuser.ProductPackageFieldPayDuration])

	// Check targets field if exists
	if targetsField, ok := fields[dbuser.ProductPackageFieldTargets]; ok && len(req.Targets) > 0 {
		targets := *targetsField.(*dbuser.PackageTargets)
		suite.Len(targets, len(req.Targets))

		for i, target := range targets {
			reqTarget := req.Targets[i]

			// Check condition fields
			if reqTarget.Condition.LatestPackages != nil {
				suite.NotNil(target.Condition.LatestPackages)
				suite.ElementsMatch(reqTarget.Condition.LatestPackages.IDs, target.Condition.LatestPackages.IDs)
				suite.Equal(reqTarget.Condition.LatestPackages.Operator, target.Condition.LatestPackages.Operator)
			}

			// Check display fields
			suite.Equal(reqTarget.Display.Title, target.Display.Title)
			suite.Equal(reqTarget.Display.Price, target.Display.Price)
			suite.Equal(reqTarget.Display.Duration, target.Display.Duration)
			suite.Equal(reqTarget.Display.Highlight, target.Display.Highlight)
			suite.Equal(reqTarget.Display.ButtonText, target.Display.ButtonText)
			suite.Equal(reqTarget.Display.Description, target.Display.Description)
			suite.Equal(reqTarget.Display.OriginPriceDesc, target.Display.OriginPriceDesc)
		}
	}
}
