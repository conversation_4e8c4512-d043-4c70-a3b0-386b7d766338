//go:generate mockgen -source repository.go -destination repository_mock.go -package productpackage
package productpackage

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/duke-git/lancet/v2/slice"
	"golang.org/x/exp/maps"
	"gopkg.in/guregu/null.v3"
)

type Repository interface {
	ListAll() ([]dbuser.ProductPackage, error)
	Create(pkg dbuser.ProductPackage) (*dbuser.ProductPackage, error)
	UpdateByFields(id int64, vals map[dbuser.ProductPackageField]any) (bool, error)
	UpsertLayoutByFields(productPackageID int64, vals map[dbuser.PackageLayoutField]any) error
}

type repository struct {
	dbReader database.DB
	dbWriter database.DB
}

func NewRepository(dbPoolUser *datastore.DBPool) Repository {
	return &repository{
		dbReader: dbPoolUser.Slave().Unsafe(),
		dbWriter: dbPoolUser.Master().Unsafe(),
	}
}

func (r *repository) ListAll() ([]dbuser.ProductPackage, error) {
	layoutFields := database.GetDBFields(&dbuser.PackageLayout{}).Fields
	var layoutSelect string
	for i, field := range layoutFields {
		layoutSelect += fmt.Sprintf(`pl.%s AS "package_layout.%s"`, field, field)
		if i < len(layoutFields)-1 {
			layoutSelect += ", "
		}
	}

	query := `
		SELECT
			pp.*,` + layoutSelect +
		`
		FROM product_packages pp
		LEFT JOIN package_layout pl ON pp.id = pl.product_packages_id`

	rows, err := r.dbReader.Queryx(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var packages []dbuser.ProductPackage
	for rows.Next() {
		var pp dbuser.ProductPackage

		err := rows.StructScan(&pp)
		if err != nil {
			return nil, err
		}
		if !pp.Layout.ID.Valid {
			pp.Layout = nil
		}
		packages = append(packages, pp)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return packages, nil
}

func (r *repository) Create(pkg dbuser.ProductPackage) (*dbuser.ProductPackage, error) {
	productPackageFields := database.GetDBFields(&dbuser.ProductPackage{}).Fields
	var filteredFields []string
	for _, field := range productPackageFields {
		if field != "product" && field != "package_layout" && field != "id" {
			filteredFields = append(filteredFields, field)
		}
	}

	productPackageFieldsStr := strings.Join(filteredFields, ", ")
	productPackageParamsStr := ":" + strings.Join(filteredFields, ", :")

	query := fmt.Sprintf(`
        INSERT INTO product_packages (%s)
        VALUES (%s)
        RETURNING id
    `, productPackageFieldsStr, productPackageParamsStr)

	var newID int
	stmt, err := r.dbWriter.PrepareNamed(query)
	if err != nil {
		return nil, err
	}
	defer stmt.Close()

	err = stmt.Get(&newID, pkg)
	if err != nil {
		return nil, err
	}

	pkg.ID = newID

	if isLayoutProvided(pkg.Layout) {
		now := null.TimeFrom(time.Now())
		pkg.Layout.CreatedAt = now
		pkg.Layout.UpdatedAt = now
		layoutFields := database.GetDBFields(&dbuser.PackageLayout{}).Fields
		var filteredLayoutFields []string
		for _, field := range layoutFields {
			if field != "id" {
				filteredLayoutFields = append(filteredLayoutFields, field)
			}
		}

		layoutFieldsStr := strings.Join(filteredLayoutFields, ", ")
		layoutParamsStr := ":" + strings.Join(filteredLayoutFields, ", :")

		layoutQuery := fmt.Sprintf(`
            INSERT INTO package_layout (%s)
            VALUES (%s)
            RETURNING id
        `, layoutFieldsStr, layoutParamsStr)

		pkg.Layout.ProductPackagesID = sql.NullInt64{Int64: int64(newID), Valid: true}
		var layoutID int

		layoutStmt, err := r.dbWriter.PrepareNamed(layoutQuery)
		if err != nil {
			return nil, err
		}
		defer layoutStmt.Close()

		err = layoutStmt.Get(&layoutID, pkg.Layout)
		if err != nil {
			return nil, err
		}

		pkg.Layout.ID = sql.NullInt64{Int64: int64(layoutID), Valid: true}
	}

	return &pkg, nil
}

func isLayoutProvided(layout *dbuser.PackageLayout) bool {
	return layout != nil &&
		(layout.Title.Valid || layout.Subtitle.Valid || layout.TextColor.Valid ||
			layout.CTAText.Valid || layout.Description.Valid || layout.Image.Valid ||
			layout.BackgroundColor.Valid || layout.BackgroundImage.Valid || layout.Terms.Valid ||
			layout.DisplayMoreBtn.Valid)
}

func (r *repository) UpdateByFields(id int64, vals map[dbuser.ProductPackageField]any) (bool, error) {
	if len(vals) == 0 {
		return false, nil
	}
	namedArgs := map[string]interface{}{
		"id": id, // in case the fields include `id`
	}
	q := `UPDATE product_packages SET updated_at = NOW()`
	for k, v := range vals {
		q += fmt.Sprintf(`, %s = :%s`, k, k)
		namedArgs[k.String()] = v
	}
	q += ` WHERE id = :id`

	result, err := r.dbWriter.NamedExec(q, namedArgs)
	if err != nil {
		return false, err
	}
	affected, _ := result.RowsAffected()
	return affected > 0, nil
}

func (r *repository) UpsertLayoutByFields(productPackageID int64, vals map[dbuser.PackageLayoutField]any) error {
	if len(vals) == 0 {
		return nil
	}
	namedArgs := map[string]interface{}{
		"product_packages_id": productPackageID,
	}
	keys := maps.Keys(vals)

	fieldsStr := slice.Join(keys, ", ")            // e.g. `title, cta_text, description`
	valueNamesStr := ":" + slice.Join(keys, ", :") // e.g. `:title, :cta_text, :description`
	excludesStr := ""
	for k, v := range vals {
		excludesStr += fmt.Sprintf(`, %s = EXCLUDED.%s`, k, k)
		namedArgs[k.String()] = v
	} // e.g. `, title = EXCLUDED.title, cta_text = EXCLUDED.cta_text, description = EXCLUDED.description`


	q := fmt.Sprintf(`INSERT INTO package_layout (
	product_packages_id, %s,
	created_at,
	updated_at
) VALUES (:product_packages_id, %s, NOW(), NOW()) 
ON CONFLICT (product_packages_id) DO UPDATE SET
    updated_at = EXCLUDED.updated_at
    %s;`, fieldsStr, valueNamesStr, excludesStr)

	_, err := r.dbWriter.NamedExec(q, namedArgs)
	return err
}
