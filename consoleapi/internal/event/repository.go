//go:generate mockgen -source repository.go -destination repository_mock.go -package event
package event

import (
	"errors"
	"sort"

	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
)

type Repository interface {
	List() (event *model.Event, err error)
	Update(event *model.Event) (err error)
}

type repository struct {
	cacheReader cache.Cacher
	cacheWriter cache.Cacher
}

func NewRepository(redisPoolUser *datastore.RedisPool) Repository {
	return &repository{
		cacheReader: cache.New(redisPoolUser.Slave()),
		cacheWriter: cache.New(redisPoolUser.Master()),
	}
}

func (r *repository) List() (event *model.Event, err error) {
	event = new(model.Event)
	err = r.cacheReader.Get(key.GetMetaEvent(), event)
	if errors.Is(err, cache.ErrCacheMiss) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	sort.Sort(event.Event)
	return event, err
}

func (r *repository) Update(event *model.Event) (err error) {
	// redis write
	_, err = r.cacheWriter.SetWithOptions(key.GetMetaEvent(), event, cache.SetOptions{})
	return err
}
