package event

import (
	"encoding/json"
	"net/http"

	"github.com/KKTV/kktv-api-v3/consoleapi/internal/errs"
	"github.com/KKTV/kktv-api-v3/consoleapi/internal/rest"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/render"
)

type Handler struct {
	repo Repository
}

func NewHandler(repo Repository) *Handler {
	return &Handler{
		repo: repo,
	}
}

func (h *Handler) List(w http.ResponseWriter, r *http.Request) {
	event, err := h.repo.List()
	if err != nil {
		log.Error("ConsoleEventHandler: List :repo get event list fail").Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
		return
	}
	render.JSONOk(w, rest.Ok(event))
}
func (h *Handler) Update(w http.ResponseWriter, r *http.Request) {
	event := new(model.Event)

	jsdecoder := json.NewDecoder(r.Body)
	err := jsdecoder.Decode(event)

	if err != nil {
		render.JSONBadRequest(w, rest.Error(errs.InvalidParameter.Code, errs.InvalidParameter.Message))
		return
	}
	err = h.repo.Update(event)
	if err != nil {
		log.Error("ConsoleEventHandler: Update: redis write fail").Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
		return
	}

	message := "update event"
	dbmeta.ConsoleLog(r, "meta", message)

	render.JSONOk(w, nil)
}
