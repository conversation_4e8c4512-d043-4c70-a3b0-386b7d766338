package titlelist

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	"github.com/go-zoo/bone"
	"github.com/golang/mock/gomock"
	"github.com/jinzhu/copier"
	"github.com/stretchr/testify/suite"
)

type HandlerTestSuite struct {
	suite.Suite
	ctrl      *gomock.Controller
	app       *bone.Mux
	repo      *MockRepository
	mockClock *clock.MockClock
}

func TestHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(HandlerTestSuite))
}

func (suite *HandlerTestSuite) SetupTest() {
	suite.app = bone.New()
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockClock = clock.NewMockClock(suite.ctrl)
	suite.repo = NewMockRepository(suite.ctrl)
	handler := &Handler{
		repo:  suite.repo,
		clock: suite.mockClock,
	}

	suite.app.PostFunc("/v3/console/metatitlelist", handler.Create)
	suite.app.PutFunc("/v3/console/metatitlelist", handler.Update)
}

func (suite *HandlerTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func (suite *HandlerTestSuite) TestUpdate() {
	mockInput := TitleList{
		ID:      4,
		Caption: "test",
		Meta:    &TitleListMeta{ShareId: "josie-love", Roles: []string{"guest", "pr"}},
	}
	comments := make(map[string]string)
	comments["06000001"] = "goood"
	highlightMock := TitleList{
		ID:                4,
		Caption:           "high light",
		EditorName:        "jaguar",
		EditorDescription: "author",
		Meta: &TitleListMeta{ShareId: "5566",
			Comments:    comments,
			TitleID:     []string{"06000001"},
			Collections: []string{"genre:動漫"},
		},
	}

	suite.mockClock.EXPECT().Now().Return(time.Date(2024, 4, 29, 0, 1, 6, 0, datetimer.LocationTaipei)).AnyTimes()

	testCases := []struct {
		name  string
		input TitleList

		expectedStatus int
		given          func()
	}{
		{
			name: "WHEN input with empty share id, create new hash share id",
			input: func() TitleList {
				newOne := TitleList{}
				_ = copier.Copy(&newOne, &mockInput)
				newOne.Meta.ShareId = ""
				return newOne
			}(),
			expectedStatus: http.StatusOK,
			given: func() {
				model, _ := mockInput.ToModel()
				model.Meta.ShareId = "229a47645a"
				suite.repo.EXPECT().Update(model).Return(nil).Times(1)
			},
		},
		{
			name:           "highlight update succ",
			input:          highlightMock,
			expectedStatus: http.StatusOK,
			given: func() {
				model, _ := highlightMock.ToModel()
				suite.repo.EXPECT().Update(model).Return(nil).Times(1)
			},
		},
		{
			name:           "Update Error",
			input:          mockInput,
			expectedStatus: http.StatusInternalServerError,
			given: func() {
				model, _ := mockInput.ToModel()
				suite.repo.EXPECT().Update(model).Return(errors.New("update error")).Times(1)
			},
		},
	}
	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			tc.given()
			// Prepare request body
			body, _ := json.Marshal(tc.input)
			req := httptest.NewRequest(http.MethodPut, "/v3/console/metatitlelist", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			rr := httptest.NewRecorder()

			suite.app.ServeHTTP(rr, req)
			suite.Assert().Equal(tc.expectedStatus, rr.Code)
		})
	}

}

func (suite *HandlerTestSuite) TestCreate() {
	comments := make(map[string]string)
	comments["06000001"] = "goood"
	highlightMock := TitleList{
		ID:                4,
		Caption:           "high light",
		EditorName:        "jaguar",
		EditorDescription: "author",
		Meta: &TitleListMeta{ShareId: "5566",
			Comments:    comments,
			TitleID:     []string{"06000001"},
			Collections: []string{"genre:動漫"},
		},
	}
	testCases := []struct {
		name  string
		input TitleList

		expectedStatus int
		given          func()
	}{
		{
			name:           "high light create",
			input:          highlightMock,
			expectedStatus: http.StatusOK,
			given: func() {
				model, _ := highlightMock.ToModel()
				suite.repo.EXPECT().Create(model).Return(nil).Times(1)
			},
		},
	}
	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			tc.given()
			// Prepare request body
			body, _ := json.Marshal(tc.input)
			req := httptest.NewRequest(http.MethodPost, "/v3/console/metatitlelist", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			rr := httptest.NewRecorder()

			suite.app.ServeHTTP(rr, req)
			suite.Assert().Equal(tc.expectedStatus, rr.Code)
		})
	}

}
