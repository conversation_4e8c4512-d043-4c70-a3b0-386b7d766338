package titlelist

import (
	"fmt"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/jinzhu/copier"
	"gopkg.in/guregu/null.v3"
)

/*
caption, summary, source_image, resized_images, enabled, "order",
title_id, visible_since, visible_until, topic, uri, url, list_type,
trailer_autoplay_enabled, trailer_episode_id, dominant_color, meta
*/
type TitleList struct {
	ID                     int64          `json:"id,omitempty"`
	Caption                string         `json:"title,omitempty"`
	Summary                string         `json:"summary,omitempty"`
	SourceImage            string         `json:"source_image,omitempty"`
	Enabled                *bool          `json:"enabled,omitempty"`
	Order                  int64          `json:"order,omitempty"`
	TitleID                null.String    `json:"title_id,omitempty"`
	VisibleSince           *time.Time     `json:"visible_since,omitempty"`
	VisibleUntil           *time.Time     `json:"visible_until,omitempty"`
	Topic                  string         `json:"topic,omitempty"`
	URI                    string         `json:"uri,omitempty"`
	URL                    string         `json:"url,omitempty"`
	ListType               string         `json:"list_type,omitempty"`
	TrailerAutoplayEnabled bool           `json:"trailer_autoplay_enabled"`
	TrailerEpisodeID       null.String    `json:"trailer_episode_id,omitempty"`
	DominantColor          null.String    `json:"dominant_color,omitempty" validate:"startsnotwith=#"`
	Meta                   *TitleListMeta `json:"meta,omitempty"`
	Type                   string         `json:"type,omitempty"`
	Image                  string         `json:"image,omitempty"`
	EditorName             string         `json:"editor_name,omitempty"`
	EditorDescription      string         `json:"editor_description,omitempty"`
	EditorAvatar           string         `json:"editor_avatar,omitempty"`
}

type TitleListMeta struct {
	TitleID            []string          `json:"title_id,omitempty"`
	ShareId            string            `json:"share_id,omitempty" validate:"excludes=youwilllove"`
	Description        string            `json:"description,omitempty"`
	BackgroundImageUrl string            `json:"background_image_url,omitempty"`
	Copyright          string            `json:"copyright,omitempty"`
	OGImage            string            `json:"og_image,omitempty"`
	OGDescription      string            `json:"og_description,omitempty"`
	Collections        []string          `json:"collections,omitempty"`
	Pinned             string            `json:"pinned,omitempty"`
	Roles              []string          `json:"roles,omitempty"`
	Platforms          []string          `json:"platforms,omitempty"` // web|app|tv
	Comments           map[string]string `json:"comments,omitempty"`
	VideoUrl           string            `json:"video_url,omitempty"`
}

func (t *TitleList) ToModel() (*dbmeta.TitleList, error) {
	m := new(dbmeta.TitleList)
	if err := copier.Copy(m, t); err != nil {
		return nil, fmt.Errorf("copier: %w", err)
	}
	comments := []dbmeta.MetaComment{}
	for k, v := range t.Meta.Comments {
		comment := dbmeta.MetaComment{}
		comment.TitleID = k
		comment.Comment = v
		comments = append(comments, comment)
	}
	m.Meta.Comments = comments
	var editor *dbmeta.Editor
	if t.EditorAvatar != "" || t.EditorDescription != "" || t.EditorName != "" {
		editor = new(dbmeta.Editor)
		editor.Avatar = t.EditorAvatar
		editor.Description = t.EditorDescription
		editor.Name = t.EditorName
	}

	if editor != nil {
		m.Meta.Editor = editor
	}
	return m, nil
}
