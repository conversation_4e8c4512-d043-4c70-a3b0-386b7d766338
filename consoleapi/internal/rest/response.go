package rest

type Err struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

// Resp for api response
type Resp[T any] struct {
	Error *Err `json:"error"`
	Data  T    `json:"data"`
}

func Ok[T any](data T) Resp[T] {
	return Resp[T]{
		Data: data,
	}
}

func Error(code, message string) Resp[interface{}] {
	return Resp[interface{}]{
		Error: &Err{
			Code:    code,
			Message: message,
		},
	}
}
