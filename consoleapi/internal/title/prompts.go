package title

import "fmt"

var (
	generatePromptsReleaseInfoSyntax = func(input string) []string {
		return []string{
			"你是一個專門用於日期時間的文字編碼解析器，\n輸入：為一段文字，可能包含星期幾與時間資訊 \ne.g. `9/1 因節慶,節目停更一次。8/27起，每周一至五22:00更新一集。 周六、周日更新3集`\n\n輸出：把輸入文字中的時間日期資訊取代成語法tag後的純文字結果\ntag中的內容定義有2種：tag 1: `{{wd:{weekday}|t:{time}}}` \n- {weekday}: 代表星期幾，支持間斷或連續的天數，如 `1-4` 即星期一至四，或 `2,4,6` 代表星期二、四、六\n- {time}: 可接受值 [hhmm|none|morning|noon|night]. `hhmm` 代表當天時間，24小時制，如 `1445` 代表下午2:45。`none` 時，代表不指定時間. `morning`, `noon`, `night` 分別代表早上、午間、晚間\n\ntag 2: `{{skip:{skip_announce}}}`- {skip_announce}: 代表停播資訊或暫停原因。如 `9/1 因節慶特備節目停更一次`\n要求：1. 直接輸出語法tag取代後的純文字結果，不需要實作的程式碼; 2. input支援任何語言，但output需為繁體中文\nexample:\n- input: `9/1 因節慶特備節目停更一次。8/27起，每周一至五22:00更新一集。 周六、周日更新3集`\n- output: `{{skip:9/1 因節慶特備節目停更一次}}。8/27起，{{wd:1-5|t:2200}}更新一集。 {{wd:6,7|t:none}}更新3集`",
			"input: 逢周一至五14:30播出",
			"output: 逢{{wd:1-5|t:1430}}播出",
			"input: 周一、四12:00更新2集，周五至周日更新1集",
			"output: {{wd:1,4|t:1200}}更新2集，{{wd:5-7|t:none}}更新1集",
			"input: 每週三3pm更新",
			"output: {{wd:3|t:1500}}更新",
			"input: 9/1 因節慶特備節目停更一次。8/27起，每周一至五22:00更新一集。 周六、周日更新3集",
			"output: {{skip:9/1 因節慶特備節目停更一次}}。8/27起，{{wd:1-5|t:2200}}更新一集。 {{wd:6,7|t:none},新3集",
			"input: 9/2開始不定期更新。10/1 因故停播一次",
			"output: 9/2開始不定期更新。{{skip:10/1 因故停播一次}}",
			"input: 每天早上更新",
			"output: {{wd:1-7|t:morning}}更新",
			"input: Update at 9pm every Tuesday",
			"output: 每{{wd:2|t:2100}}更新",
			"input: From 6th September, weekly release on Saturday noon",
			"output: 9月6日起，每{{wd:6|t:noon}}播出新集數",
			"input: 週間晚上播出",
			"output: {{wd:1-5|t:night}}播出",
			"input: 週末不定時更新",
			"output: {{wd:6,7|t:none}}更新",
			fmt.Sprintf("input: %s", input),
			"output: ",
		}
	}
)
