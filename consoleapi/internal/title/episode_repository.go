//go:generate mockgen -source episode_repository.go -destination episode_repository_mock.go -package title
package title

import (
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/lib/pq"
)

type EpisodeRepository interface {
	UpdatePublishTime(episodeIDs []string, publishTime, unpublishTime *time.Time) error
}

type episodeRepository struct {
	dbReader database.DB
}

func NewEpisodeRepository(dbReader database.DB) EpisodeRepository {
	return &episodeRepository{
		dbReader: dbReader,
	}
}

func (r *episodeRepository) UpdatePublishTime(episodeIDs []string, publishTime, unpublishTime *time.Time) error {
	_, err := r.dbReader.Exec(`
		UPDATE meta_episode
		SET pub = $1, unpub = $2
		WHERE id = ANY($3)
	`, publishTime, unpublishTime, pq.Array(episodeIDs))

	return err
}
