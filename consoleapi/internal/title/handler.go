package title

import (
	"fmt"
	"net/http"

	"github.com/KKTV/kktv-api-v3/consoleapi/internal/errs"
	"github.com/KKTV/kktv-api-v3/consoleapi/internal/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/meta"
	"github.com/KKTV/kktv-api-v3/pkg/genai"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/render"
)

type Handler struct {
	aiClient    genai.Client
	episodeRepo EpisodeRepository
	legacyHelper legacyHelper
}

func NewHandler(aiClient genai.Client, episodeRepo EpisodeRepository) *Handler {
	return &Handler{
		aiClient:    aiClient,
		episodeRepo: episodeRepo,
		legacyHelper: &legacy{},
	}
}

type interpretReq struct {
	ReleaseInfo string `json:"release_info" validate:"required"`
}

func (h *Handler) InterpretReleaseInfo(w http.ResponseWriter, r *http.Request) {
	var req interpretReq
	if err := httpreq.PayloadBinding(&req, r); err != nil {
		render.JSONBadRequest(w, rest.Error(errs.InvalidParameter.Code, errs.InvalidParameter.Message))
		return
	}

	resp := interpretResp{
		ReleaseInfo: req.ReleaseInfo,
	}

	info, err := meta.InterpretAiringInfo(req.ReleaseInfo)
	if err == nil {
		resp.ReleaseInfo = info.RenderedReleaseInfo
		resp.Airing = &airingInfo{
			SkipAnnouncement: info.SkipAnnouncement,
		}
		for _, timeInfo := range info.Schedule {
			resp.Airing.Schedule = append(resp.Airing.Schedule, airingTimeInfo{
				Weekday: int(timeInfo.Weekday),
				Time:    timeInfo.Time,
				Display: timeInfo.String(),
			})
		}
	}
	render.JSONOk(w, rest.Ok(resp))
}

func (h *Handler) ReleaseInfoToSyntax(w http.ResponseWriter, r *http.Request) {
	var req interpretReq
	if err := httpreq.PayloadBinding(&req, r); err != nil {
		render.JSONBadRequest(w, rest.Error(errs.InvalidParameter.Code, errs.InvalidParameter.Message))
		return
	}
	prompts := generatePromptsReleaseInfoSyntax(req.ReleaseInfo)
	result, err := h.aiClient.GetCompletion(r.Context(), prompts)
	if err != nil {
		plog.Error("titleHandler: ReleaseInfoToSyntax: aiClient fail to get completion").Err(err).Strs("prompts", prompts).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, fmt.Sprintf("%s: %v", errs.InternalErrorLLM.Message, err)))
		return
	}

	render.JSONOk(w, rest.Ok(releaseInfoResp{
		ReleaseInfo: result,
	}))
}

type episodePublishTimeReq struct {
	EpisodeIDs []string `json:"episode_ids" validate:"required"`
}

func (h *Handler) DeleteEpisodePublishTime(w http.ResponseWriter, r *http.Request) {
	var req episodePublishTimeReq
	if err := httpreq.PayloadBinding(&req, r); err != nil {
		render.JSONBadRequest(w, rest.Error(errs.InvalidParameter.Code, errs.InvalidParameter.Message))
		return
	}

	if err := h.episodeRepo.UpdatePublishTime(req.EpisodeIDs, nil, nil); err != nil {
		plog.Error("titleHandler: DeleteEpisodePublishTime: episodeRepo failed to update publish time").Err(err).Strs("episode_ids", req.EpisodeIDs).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, fmt.Sprintf("%s: %v", errs.InternalError.Message, err)))
		return
	}
	titleIDs := map[string]struct{}{}
	for _, episodeID := range req.EpisodeIDs {
		if len(episodeID) != 14 {
			continue
		}
		titleID := episodeID[:8]
		titleIDs[titleID] = struct{}{}
	}

	for titleID := range titleIDs {
		if err := h.legacyHelper.RefreshCache(titleID); err != nil{
			plog.Warn("titleHandler: DeleteEpisodePublishTime: legacyHelper failed to refresh cache").
				Err(err).Str("title_id", titleID).Send()
		}
	}
	render.JSONNoContent(w)
}
