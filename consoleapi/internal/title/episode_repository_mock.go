// Code generated by MockGen. DO NOT EDIT.
// Source: episode_repository.go

// Package title is a generated GoMock package.
package title

import (
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
)

// MockEpisodeRepository is a mock of EpisodeRepository interface.
type MockEpisodeRepository struct {
	ctrl     *gomock.Controller
	recorder *MockEpisodeRepositoryMockRecorder
}

// MockEpisodeRepositoryMockRecorder is the mock recorder for MockEpisodeRepository.
type MockEpisodeRepositoryMockRecorder struct {
	mock *MockEpisodeRepository
}

// NewMockEpisodeRepository creates a new mock instance.
func NewMockEpisodeRepository(ctrl *gomock.Controller) *MockEpisodeRepository {
	mock := &MockEpisodeRepository{ctrl: ctrl}
	mock.recorder = &MockEpisodeRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEpisodeRepository) EXPECT() *MockEpisodeRepositoryMockRecorder {
	return m.recorder
}

// UpdatePublishTime mocks base method.
func (m *MockEpisodeRepository) UpdatePublishTime(episodeIDs []string, publishTime, unpublishTime *time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePublishTime", episodeIDs, publishTime, unpublishTime)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePublishTime indicates an expected call of UpdatePublishTime.
func (mr *MockEpisodeRepositoryMockRecorder) UpdatePublishTime(episodeIDs, publishTime, unpublishTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePublishTime", reflect.TypeOf((*MockEpisodeRepository)(nil).UpdatePublishTime), episodeIDs, publishTime, unpublishTime)
}
