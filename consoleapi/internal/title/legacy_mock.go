// Code generated by MockGen. DO NOT EDIT.
// Source: legacy.go

// Package title is a generated GoMock package.
package title

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MocklegacyHelper is a mock of legacyHelper interface.
type MocklegacyHelper struct {
	ctrl     *gomock.Controller
	recorder *MocklegacyHelperMockRecorder
}

// MocklegacyHelperMockRecorder is the mock recorder for MocklegacyHelper.
type MocklegacyHelperMockRecorder struct {
	mock *MocklegacyHelper
}

// NewMocklegacyHelper creates a new mock instance.
func NewMocklegacyHelper(ctrl *gomock.Controller) *MocklegacyHelper {
	mock := &MocklegacyHelper{ctrl: ctrl}
	mock.recorder = &MocklegacyHelperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MocklegacyHelper) EXPECT() *MocklegacyHelperMockRecorder {
	return m.recorder
}

// RefreshCache mocks base method.
func (m *MocklegacyHelper) RefreshCache(titleID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RefreshCache", titleID)
	ret0, _ := ret[0].(error)
	return ret0
}

// RefreshCache indicates an expected call of RefreshCache.
func (mr *MocklegacyHelperMockRecorder) RefreshCache(titleID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshCache", reflect.TypeOf((*MocklegacyHelper)(nil).RefreshCache), titleID)
}
