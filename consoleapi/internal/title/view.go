package title

type airingTimeInfo struct {
	Weekday int    `json:"weekday"` // mon:1, tue:2, ..., sun:7
	Time    string `json:"time"`    // HHMM
	Display string `json:"display"`
}

type airingInfo struct {
	SkipAnnouncement *string          `json:"skip_announcement"`
	Schedule         []airingTimeInfo `json:"schedule"`
}

type interpretResp struct {
	ReleaseInfo string      `json:"release_info"`
	Airing      *airingInfo `json:"airing"`
}

type releaseInfoResp struct {
	ReleaseInfo string `json:"release_info"`
}
