package consoleapi

import (
	"github.com/KKTV/kktv-api-v3/consoleapi/internal/ads"
	"github.com/KKTV/kktv-api-v3/consoleapi/internal/event"
	"github.com/KKTV/kktv-api-v3/consoleapi/internal/pkg/urls"
	"github.com/KKTV/kktv-api-v3/consoleapi/internal/productpackage"
	"github.com/KKTV/kktv-api-v3/consoleapi/internal/title"
	"github.com/KKTV/kktv-api-v3/consoleapi/internal/titlelist"
	"github.com/KKTV/kktv-api-v3/consoleapi/internal/user"
	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/auditing"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/genai"
)

type Handlers struct {
	User           *user.Handler
	ProductPackage *productpackage.Handler
	Title          *title.Handler
	Event          *event.Handler
	TitleList      *titlelist.Handler
	Ads            *ads.Handler
}

// NewHandlers returns a new instance of Handlers. All dependencies should be injected here.
func NewHandlers(env string, dbPoolUser *datastore.DBPool, dbPoolMeta *datastore.DBPool, redisPoolMeta *datastore.RedisPool) *Handlers {
	urls.Init(env)

	userRepo := user.NewRepository(dbPoolUser)
	productPackageRepo := productpackage.NewRepository(dbPoolUser)
	eventRepo := event.NewRepository(redisPoolMeta)
	titleListRepo := titlelist.NewRepository(dbPoolMeta)
	adsRepo := ads.NewRepository(redisPoolMeta)
	logRepo := auditing.NewRepository(dbPoolUser.Master().Unsafe())
	aiClient := genai.NewGemini(config.GenAIGeminiAPIKey)
	c := clock.New()
	episodeRepo := title.NewEpisodeRepository(dbPoolMeta.Slave().Unsafe())

	return &Handlers{
		User:           user.NewHandler(userRepo, logRepo),
		Title:          title.NewHandler(aiClient, episodeRepo),
		Event:          event.NewHandler(eventRepo),
		ProductPackage: productpackage.NewHandler(productPackageRepo),
		TitleList:      titlelist.NewHandler(titleListRepo, c),
		Ads:            ads.NewHandler(adsRepo),
	}
}
