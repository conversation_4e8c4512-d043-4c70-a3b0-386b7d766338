# Plan for Automate Series Data Fetching

## 1. Goal

Create a Go function that reads `series_id`s from a Google Sheet, queries a redis database for corresponding title information, and writes the results back into another sheet.

在既有的 `functions/linetv_metasheet_update` project 中擴充上述功能
參考 `functions/linetv_metasheet_update` 中已留好的實作缺口 `processSeries`
!!不需要產生 unit test!!

## 2. Project Structure

- existing project: `functions/linetv_metasheet_update`

## 3. Execution Flow

1.  **Read from Google Sheet (config.UploadSheetGID)**:
    - Target Sheet ID (gid): `256251858`
    - Read all values from column `A`, starting from cell `A2` to get the list of `kktv_series_id`s.
    - extract the title ids from kktv_series_id which are the first 8 digits of kktv_series_id.
    - 現有的 linetv_metasheet_update 架構是讀取 title_id 和 series_id 兩個欄位。需要修改現有架構來適應新的讀取方式

2.  **Query Redis Database**:
    - For each `title_ids` collected:
        - use the cache interface HGet to retrieve the title metadata. redis key is defined in redis_keys `TitleDetail` and 4 hash values should be loaded using the hmget in once query: `series`, `content_labels_without_full_access`, `airing`, `release_info`  and `total_episode_counts`.
            - the `total_episode_counts` metadata is a json like `{"**********":16}  // series_id: total_episode_count`
            - `airing`: json object, nullable: `{"rendered_release_info":"7/6起，每週日00:30更新","skip_announcement":null,"schedule":[{"weekday":7,"time":"0030"}]}`
            - `release_info`: string: `"7/6起，每{{wd:7|t:0030}}更新"`
            - `series` json object, nullable:
            ```
        [
    {
        "avod_episode_hint": "螢幕太小？升級VIP投放到大螢幕",
        "id": "**********", // series id
        "united_id": "**********",
        "united_name": "第1季",
        "title": "第1季",
        "episodes": [ // episode detail 不重要，只要存成 string 即可
            {
                "id": "**********0001",
                "title": "第1集",
                "series_title": "第1季",
                "series_id": "**********",
                "content_agent": "龍華",
                "content_provider": "SBS",
                "duration": 4455.2881,
                "end_offset": 4425000,
                "end_year": 2008,
                "release_year": 2008,
                "start_year": 2008,
                "available": true,
                "free_trial": true,
                "has_subtitles": false,
                "is_ending": true,
                "is_validated": true,
                "offline_mode": false,
                "play_zone": true,
                "is_avod": true,
                "roaming_mode": false,
                "secure_playback": false,
                "license_end": **********,
                "license_start": 1,
                "publish_time": 0,
                "pub": 1,
                "unpub": **********,
                "still": "https://images.kktv.com.tw/stills/2b/2b02b7638c11fce2c815fff4adc8b641086ff446.xs.jpg",
                "subtitles": null,
                "mezzanines": {
                    "dash": {
                        "size": 23255520725,
                        "sizes": {
                            "1080p": 23255520725,
                            "720p": 12083534097,
                            "480p": **********,
                            "360p": **********,
                            "240p": **********
                        },
                        "uri": "https://theater.kktv.com.tw/38/**********0001_faa89db862b91901677d3e8e7e592e8b/14643587923976188ab1_dash.mpd"
                    },
                    "hls": {
                        "size": 24071921063,
                        "sizes": {
                            "1080p": 24071921063,
                            "720p": 12657473207,
                            "480p": 7814575151,
                            "360p": 3898376999,
                            "240p": 2530603583
                        },
                        "uri": "https://theater.kktv.com.tw/38/**********0001_faa89db862b91901677d3e8e7e592e8b/14643587923976188ab1_hls.m3u8"
                    }
                }
            }
        ],
        "summary": "(影片版權即將於 6 月 29 日到期)\n職場女強人崔佳英意外的懷了菜鳥職員朴載成的孩子。本想拿掉孩子的佳英，卻被死纏爛打的朴載成說服，結婚生子當起了全職媽媽。過了幾年的黃臉婆生活後，佳英卻發現老公和自己當年的下屬高恩智過從甚密。面對年輕美眉的襲擊，一場大老婆的保衛戰即將展開。",
        "is_containing_avod": true,
        "casts": [
            "廉晶雅(崔佳英)",
            "奉太奎(朴載成)",
            "車藝蓮(高恩智)",
            "尹朱尚(崔宗萬)",
            "金慈玉(金福實)"
        ],
        "directors": [
            "吳鍾祿"
        ],
        "writers": [
            "金賢熙"
        ],
        "producers": [
            "\tJS Pictures"
        ],
        "content_provider": "SBS",
        "audio_track_lang": "ko-KR"
    }
]
        ```
        - If a title detail meta yields no results, it will be skipped, and nothing will be written for it in the output sheet.
        - Define a new data struct `TitleDetailMeta` to hold the TitleDetail information.

3.  **Write to Google Sheet ("kktv_episodes")**:
    - Target Sheet ID (gid): `**********` (add to the existing config, named `SERIES_COMPUTED_SHEET_GID`)
    - Before writing, clear all data in the range `A2:G`.
    - Each Series in `TitleDetailMeta` should be flattened into individual rows in the target sheet.
    - Write the collected series data into the sheet, mapping the fields as follows:
        - Column A: `series_id`
        - Column B: `title_id`
        - Column C: `series`: the last 2 digits of the series ID
        - Column D: `total episode count`: use TotalEpisodeCount map to get the total episode count for the series
        - Column E: `license episode count`: count the number of Episode object in such Series object
        - Column F: `labels`: use the TitleDetail's `content_labels_without_full_access` 
        - Column G: `release_info`: if Airing object is not null, use airing.rendered_release_info; else use release_info string.

## 4. Code Implementation Details

- **Reference Architecture**: The implementation will follow the patterns and conventions established in the existing `functions/linetv_metasheet_update` module.
- **Dependencies**:
    - Use `pkg/cahce` for cache operations.
    - Use `pkg/log` for structured logging.

## 5. Manual Execution

The script will be designed for manual execution from the command line.
