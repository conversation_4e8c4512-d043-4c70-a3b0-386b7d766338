// Code generated by MockGen. DO NOT EDIT.
// Source: cache.go

// Package cache is a generated GoMock package.
package cache

import (
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	pool "github.com/mediocregopher/radix.v2/pool"
)

// Mock<PERSON>acher is a mock of Cacher interface.
type MockCacher struct {
	ctrl     *gomock.Controller
	recorder *MockCacherMockRecorder
}

// MockCacherMockRecorder is the mock recorder for MockCacher.
type MockCacherMockRecorder struct {
	mock *MockCacher
}

// NewMockCacher creates a new mock instance.
func NewMockCacher(ctrl *gomock.Controller) *Mock<PERSON>acher {
	mock := &MockCacher{ctrl: ctrl}
	mock.recorder = &MockCacherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCacher) EXPECT() *MockCacherMockRecorder {
	return m.recorder
}

// Del mocks base method.
func (m *<PERSON><PERSON><PERSON><PERSON>) Del(keys ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range keys {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Del", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Del indicates an expected call of Del.
func (mr *MockCacherMockRecorder) Del(keys ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Del", reflect.TypeOf((*MockCacher)(nil).Del), keys...)
}

// Exists mocks base method.
func (m *MockCacher) Exists(key string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Exists", key)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Exists indicates an expected call of Exists.
func (mr *MockCacherMockRecorder) Exists(key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Exists", reflect.TypeOf((*MockCacher)(nil).Exists), key)
}

// ExpireAt mocks base method.
func (m *MockCacher) ExpireAt(key string, expireAt time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExpireAt", key, expireAt)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExpireAt indicates an expected call of ExpireAt.
func (mr *MockCacherMockRecorder) ExpireAt(key, expireAt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExpireAt", reflect.TypeOf((*MockCacher)(nil).ExpireAt), key, expireAt)
}

// Get mocks base method.
func (m *MockCacher) Get(key string, value interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", key, value)
	ret0, _ := ret[0].(error)
	return ret0
}

// Get indicates an expected call of Get.
func (mr *MockCacherMockRecorder) Get(key, value interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockCacher)(nil).Get), key, value)
}

// GetMemoryUsage mocks base method.
func (m *MockCacher) GetMemoryUsage(key string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMemoryUsage", key)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMemoryUsage indicates an expected call of GetMemoryUsage.
func (mr *MockCacherMockRecorder) GetMemoryUsage(key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMemoryUsage", reflect.TypeOf((*MockCacher)(nil).GetMemoryUsage), key)
}

// HDel mocks base method.
func (m *MockCacher) HDel(key, hashKey string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HDel", key, hashKey)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HDel indicates an expected call of HDel.
func (mr *MockCacherMockRecorder) HDel(key, hashKey interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HDel", reflect.TypeOf((*MockCacher)(nil).HDel), key, hashKey)
}

// HGet mocks base method.
func (m *MockCacher) HGet(key, hashKey string, value interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HGet", key, hashKey, value)
	ret0, _ := ret[0].(error)
	return ret0
}

// HGet indicates an expected call of HGet.
func (mr *MockCacherMockRecorder) HGet(key, hashKey, value interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HGet", reflect.TypeOf((*MockCacher)(nil).HGet), key, hashKey, value)
}

// HGetAll mocks base method.
func (m *MockCacher) HGetAll(key string) (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HGetAll", key)
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HGetAll indicates an expected call of HGetAll.
func (mr *MockCacherMockRecorder) HGetAll(key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HGetAll", reflect.TypeOf((*MockCacher)(nil).HGetAll), key)
}

// HMGet mocks base method.
func (m *MockCacher) HMGet(key string, fields ...string) ([][]byte, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{key}
	for _, a := range fields {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HMGet", varargs...)
	ret0, _ := ret[0].([][]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HMGet indicates an expected call of HMGet.
func (mr *MockCacherMockRecorder) HMGet(key interface{}, fields ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{key}, fields...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HMGet", reflect.TypeOf((*MockCacher)(nil).HMGet), varargs...)
}

// HSet mocks base method.
func (m *MockCacher) HSet(key, hashKey string, value []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HSet", key, hashKey, value)
	ret0, _ := ret[0].(error)
	return ret0
}

// HSet indicates an expected call of HSet.
func (mr *MockCacherMockRecorder) HSet(key, hashKey, value interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HSet", reflect.TypeOf((*MockCacher)(nil).HSet), key, hashKey, value)
}

// HmSet mocks base method.
func (m *MockCacher) HmSet(key string, value []interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HmSet", key, value)
	ret0, _ := ret[0].(error)
	return ret0
}

// HmSet indicates an expected call of HmSet.
func (mr *MockCacherMockRecorder) HmSet(key, value interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HmSet", reflect.TypeOf((*MockCacher)(nil).HmSet), key, value)
}

// Incr mocks base method.
func (m *MockCacher) Incr(key string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Incr", key)
	ret0, _ := ret[0].(error)
	return ret0
}

// Incr indicates an expected call of Incr.
func (mr *MockCacherMockRecorder) Incr(key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Incr", reflect.TypeOf((*MockCacher)(nil).Incr), key)
}

// IncrBy mocks base method.
func (m *MockCacher) IncrBy(key string, value int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrBy", key, value)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrBy indicates an expected call of IncrBy.
func (mr *MockCacherMockRecorder) IncrBy(key, value interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrBy", reflect.TypeOf((*MockCacher)(nil).IncrBy), key, value)
}

// New mocks base method.
func (m *MockCacher) New(pool *pool.Pool) Cacher {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "New", pool)
	ret0, _ := ret[0].(Cacher)
	return ret0
}

// New indicates an expected call of New.
func (mr *MockCacherMockRecorder) New(pool interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "New", reflect.TypeOf((*MockCacher)(nil).New), pool)
}

// Once mocks base method.
func (m *MockCacher) Once(key string, value interface{}, ttl time.Duration, do DoFunc) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Once", key, value, ttl, do)
	ret0, _ := ret[0].(error)
	return ret0
}

// Once indicates an expected call of Once.
func (mr *MockCacherMockRecorder) Once(key, value, ttl, do interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Once", reflect.TypeOf((*MockCacher)(nil).Once), key, value, ttl, do)
}

// Scan mocks base method.
func (m *MockCacher) Scan(cursor int, match string, count int) ([]string, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Scan", cursor, match, count)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Scan indicates an expected call of Scan.
func (mr *MockCacherMockRecorder) Scan(cursor, match, count interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Scan", reflect.TypeOf((*MockCacher)(nil).Scan), cursor, match, count)
}

// Set mocks base method.
func (m *MockCacher) Set(key string, value interface{}, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Set", key, value, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// Set indicates an expected call of Set.
func (mr *MockCacherMockRecorder) Set(key, value, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Set", reflect.TypeOf((*MockCacher)(nil).Set), key, value, ttl)
}

// SetNX mocks base method.
func (m *MockCacher) SetNX(key string, value interface{}, ttl time.Duration) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNX", key, value, ttl)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetNX indicates an expected call of SetNX.
func (mr *MockCacherMockRecorder) SetNX(key, value, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNX", reflect.TypeOf((*MockCacher)(nil).SetNX), key, value, ttl)
}

// SetTTL mocks base method.
func (m *MockCacher) SetTTL(key string, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetTTL", key, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetTTL indicates an expected call of SetTTL.
func (mr *MockCacherMockRecorder) SetTTL(key, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTTL", reflect.TypeOf((*MockCacher)(nil).SetTTL), key, ttl)
}

// SetWithOptions mocks base method.
func (m *MockCacher) SetWithOptions(key string, value interface{}, opt SetOptions) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWithOptions", key, value, opt)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetWithOptions indicates an expected call of SetWithOptions.
func (mr *MockCacherMockRecorder) SetWithOptions(key, value, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWithOptions", reflect.TypeOf((*MockCacher)(nil).SetWithOptions), key, value, opt)
}

// Smembers mocks base method.
func (m *MockCacher) Smembers(key string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Smembers", key)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Smembers indicates an expected call of Smembers.
func (mr *MockCacherMockRecorder) Smembers(key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Smembers", reflect.TypeOf((*MockCacher)(nil).Smembers), key)
}

// ZAdd mocks base method.
func (m *MockCacher) ZAdd(key string, score int64, value []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZAdd", key, score, value)
	ret0, _ := ret[0].(error)
	return ret0
}

// ZAdd indicates an expected call of ZAdd.
func (mr *MockCacherMockRecorder) ZAdd(key, score, value interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZAdd", reflect.TypeOf((*MockCacher)(nil).ZAdd), key, score, value)
}

// ZRemRangeByRank mocks base method.
func (m *MockCacher) ZRemRangeByRank(key string, start, stop int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRemRangeByRank", key, start, stop)
	ret0, _ := ret[0].(error)
	return ret0
}

// ZRemRangeByRank indicates an expected call of ZRemRangeByRank.
func (mr *MockCacherMockRecorder) ZRemRangeByRank(key, start, stop interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRemRangeByRank", reflect.TypeOf((*MockCacher)(nil).ZRemRangeByRank), key, start, stop)
}

// ZRevRange mocks base method.
func (m *MockCacher) ZRevRange(key string, start, stop int) ([][]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRevRange", key, start, stop)
	ret0, _ := ret[0].([][]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ZRevRange indicates an expected call of ZRevRange.
func (mr *MockCacherMockRecorder) ZRevRange(key, start, stop interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRevRange", reflect.TypeOf((*MockCacher)(nil).ZRevRange), key, start, stop)
}

// ZRevRangeWithScores mocks base method.
func (m *MockCacher) ZRevRangeWithScores(key string, start, stop int) ([][2]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRevRangeWithScores", key, start, stop)
	ret0, _ := ret[0].([][2]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ZRevRangeWithScores indicates an expected call of ZRevRangeWithScores.
func (mr *MockCacherMockRecorder) ZRevRangeWithScores(key, start, stop interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRevRangeWithScores", reflect.TypeOf((*MockCacher)(nil).ZRevRangeWithScores), key, start, stop)
}
