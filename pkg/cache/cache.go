//go:generate mockgen -source cache.go -destination cache_mock.go -package cache
package cache

import (
	"encoding/json"
	"errors"
	"reflect"
	"time"

	"github.com/mediocregopher/radix.v2/pool"
	"github.com/mediocregopher/radix.v2/redis"
)

var (
	ErrCacheMiss      = errors.New("cache: key is missing")
	ErrReceiverNotPtr = errors.New("cache: invalid receiver, must be a pointer")
)

type Cacher interface {
	Once(key string, value interface{}, ttl time.Duration, do DoFunc) error
	Get(key string, value interface{}) error
	Set(key string, value interface{}, ttl time.Duration) error
	SetWithOptions(key string, value interface{}, opt SetOptions) (string, error)
	SetNX(key string, value interface{}, ttl time.Duration) (int64, error)
	Del(keys ...string) error
	ZRev<PERSON><PERSON>e(key string, start, stop int) ([][]byte, error)
	ZRevRangeWithScores(key string, start, stop int) ([][2]string, error)
	ZRemRangeByRank(key string, start, stop int) error
	HSet(key, hashKey string, value []byte) error
	HGet(key, hashKey string, value interface{}) error
	HMGet(key string, fields ...string) ([][]byte, error)
	HDel(key, hashKey string) ([]byte, error)
	HmSet(key string, value []interface{}) error
	HGetAll(key string) (map[string]string, error)
	New(pool *pool.Pool) Cacher
	SetTTL(key string, ttl time.Duration) error
	Smembers(key string) ([]string, error)
	Scan(cursor int, match string, count int) ([]string, int, error)
	Incr(key string) error
	Exists(key string) (bool, error)
	IncrBy(key string, value int64) (int64, error)
	ExpireAt(key string, expireAt time.Time) error
	ZAdd(key string, score int64, value []byte) error
	GetMemoryUsage(key string) (int64, error)
	//TODO implement other redis commands, reference: https://redis.io/commands
}

type cacher struct {
	pool *pool.Pool
}

func New(pool *pool.Pool) Cacher {
	return &cacher{
		pool: pool,
	}
}

func (c *cacher) New(pool *pool.Pool) Cacher {
	return &cacher{
		pool: pool,
	}
}

type DoFunc func() (interface{}, error)

func (c *cacher) Once(key string, value interface{}, ttl time.Duration, do DoFunc) error {
	err := c.Get(key, value)
	if errors.Is(err, ErrCacheMiss) {
		v, err := do()
		if err != nil {
			return err
		}
		if v == nil {
			return ErrCacheMiss
		}
		if err := c.Set(key, v, ttl); err != nil {
			return err
		}
		return c.Get(key, value)
	} else if err != nil {
		return err
	}
	return nil
}

func (c *cacher) Get(key string, value interface{}) error {
	bytes, err := c.pool.Cmd("GET", key).Bytes()
	if errors.Is(err, redis.ErrRespNil) {
		return ErrCacheMiss
	} else if err != nil {
		return err
	}
	return c.unmarshal(value, bytes)
}

func (c *cacher) Set(key string, value interface{}, ttl time.Duration) error {
	var b []byte
	switch value := value.(type) {
	case nil:
		b = nil
	case []byte:
		b = value
	case string:
		b = []byte(value)
	default:
		var err error
		b, err = json.Marshal(value)
		if err != nil {
			return err
		}
	}
	return c.pool.Cmd("SET", key, b, "PX", ttl.Milliseconds()).Err
}

// SetOptions Options for SET command https://redis.io/commands/set/
type SetOptions struct {
	XX      bool
	NX      bool
	KeepTTL bool
	TTL     *time.Duration
	//TODO implement other options
}

// SetWithOptions = SET key value [NX | XX] [KEEPTTL]
// api reference: https://redis.io/commands/set/
func (c *cacher) SetWithOptions(key string, value interface{}, opt SetOptions) (string, error) {
	b, err := json.Marshal(value)
	if err != nil {
		return "", err
	}
	options := []interface{}{key, b}
	if opt.XX {
		options = append(options, "XX")
	} else if opt.NX {
		options = append(options, "NX")
	}

	if opt.TTL != nil {
		options = append(options, "PX", opt.TTL.Milliseconds())
	} else if opt.KeepTTL {
		options = append(options, "KEEPTTL")
	}
	return c.pool.Cmd("SET", options...).Str()
}

func (c *cacher) HSet(key, hashKey string, value []byte) error {
	return c.pool.Cmd("HSET", key, hashKey, value).Err
}

// HGet = HGET key field, get the value of a hash field. The value must be a pointer
// api reference: https://redis.io/commands/hget
func (c *cacher) HGet(key, hashKey string, value interface{}) error {
	bytes, err := c.pool.Cmd("HGET", key, hashKey).Bytes()
	if errors.Is(err, redis.ErrRespNil) {
		return ErrCacheMiss
	} else if err != nil {
		return err
	}
	return c.unmarshal(value, bytes)
}

// HMGet = HMGET key field [field ...], get the values of multiple hash fields
// api reference: https://redis.io/commands/hmget
func (c *cacher) HMGet(key string, fields ...string) ([][]byte, error) {
	args := make([]interface{}, len(fields)+1)
	args[0] = key
	for i, field := range fields {
		args[i+1] = field
	}

	values, err := c.pool.Cmd("HMGET", args...).ListBytes()
	if errors.Is(err, redis.ErrRespNil) {
		return nil, ErrCacheMiss
	} else if err != nil {
		return nil, err
	}
	return values, nil
}

func (c *cacher) unmarshal(value interface{}, bytes []byte) error {
	switch value := value.(type) {
	case nil:
		return nil
	case *[]byte:
		clone := make([]byte, len(bytes))
		copy(clone, bytes)
		*value = clone
		return nil
	case *string:
		*value = string(bytes)
		return nil
	}
	//use reflection to check if value is a pointer
	if reflect.ValueOf(value).Kind() != reflect.Ptr {
		return ErrReceiverNotPtr
	}
	if len(bytes) == 0 {
		return nil
	}
	return json.Unmarshal(bytes, value)
}

func (c *cacher) HDel(key, hashKey string) ([]byte, error) {
	bytes, err := c.pool.Cmd("HDEL", key, hashKey).Bytes()
	if errors.Is(err, redis.ErrRespNil) {
		return nil, ErrCacheMiss
	} else if err != nil {
		return nil, err
	}
	return bytes, nil
}

func (c *cacher) HmSet(key string, value []interface{}) error {
	return c.pool.Cmd("HMSET", key, value).Err
}

func (c *cacher) HGetAll(key string) (map[string]string, error) {
	m, err := c.pool.Cmd("HGETALL", key).Map()

	if errors.Is(err, redis.ErrRespNil) {
		return nil, ErrCacheMiss
	} else if err != nil {
		return nil, err
	}
	return m, nil
}

func (c *cacher) ZRevRange(key string, start, stop int) ([][]byte, error) {
	return c.pool.Cmd("ZREVRANGE", key, start, stop).ListBytes()
}

func (c *cacher) ZRevRangeWithScores(key string, start, stop int) ([][2]string, error) {
	reply := c.pool.Cmd("ZREVRANGE", key, start, stop, "WITHSCORES")
	if reply.Err != nil {
		return nil, reply.Err
	}

	list, err := reply.List()
	if err != nil {
		return nil, err
	}

	if len(list)%2 != 0 {
		return nil, errors.New("unexpected result length from ZREVRANGE WITHSCORES")
	}

	result := make([][2]string, len(list)/2)
	for i := 0; i < len(list); i += 2 {
		result[i/2] = [2]string{list[i], list[i+1]}
	}

	return result, nil
}

// ZRemRangeByRank https://redis.io/commands/zremrangebyrank/
func (c *cacher) ZRemRangeByRank(key string, start, stop int) error {
	return c.pool.Cmd("ZREMRANGEBYRANK", key, start, stop).Err
}

func (c *cacher) SetTTL(key string, ttl time.Duration) error {
	return c.pool.Cmd("EXPIRE", key, ttl.Seconds()).Err
}

// SetNX https://redis.io/commands/setnx
// return 1 if the key was set, 0 if the key was not set
func (c *cacher) SetNX(key string, value interface{}, ttl time.Duration) (int64, error) {
	result, err := c.SetWithOptions(key, value, SetOptions{NX: true, TTL: &ttl})
	if errors.Is(err, redis.ErrRespNil) {
		return 0, nil
	} else if result == "OK" {
		return 1, nil
	}
	return 0, err
}

// Del https://redis.io/commands/del/
func (c *cacher) Del(keys ...string) error {
	return c.pool.Cmd("DEL", keys).Err
}

// Smembers https://redis.io/commands/smembers/
func (c *cacher) Smembers(key string) ([]string, error) {
	return c.pool.Cmd("SMEMBERS", key).List()
}

// Incr https://redis.io/commands/incr/
func (c *cacher) Incr(key string) error {
	return c.pool.Cmd("INCR", key).Err
}

// IncrBy https://redis.io/commands/incrby/
func (c *cacher) IncrBy(key string, value int64) (int64, error) {
	return c.pool.Cmd("INCRBY", key, value).Int64()
}

// Exists https://redis.io/commands/exists/
func (c *cacher) Exists(key string) (bool, error) {
	e, err := c.pool.Cmd("EXISTS", key).Int()
	if err != nil {
		return false, err
	}
	return e == 1, nil
}

// ExpireAt https://redis.io/commands/expireat/
func (c *cacher) ExpireAt(key string, expireAt time.Time) error {
	return c.pool.Cmd("EXPIREAT", key, expireAt.Unix()).Err
}

// ZAdd https://redis.io/commands/zadd/
func (c *cacher) ZAdd(key string, score int64, value []byte) error {
	return c.pool.Cmd("ZADD", key, score, value).Err
}

func (c *cacher) Scan(cursor int, match string, count int) ([]string, int, error) {
	reply := c.pool.Cmd("SCAN", cursor, "MATCH", match, "COUNT", count)
	if reply.Err != nil {
		return nil, 0, reply.Err
	}

	// 解析 SCAN 命令的回應
	scanResult, err := reply.Array()
	if err != nil {
		return nil, 0, err
	}

	// 解析出游標和鍵列表
	newCursor, err := scanResult[0].Int()
	if err != nil {
		return nil, 0, err
	}
	keys, err := scanResult[1].List()
	if err != nil {
		return nil, 0, err
	}

	return keys, newCursor, nil
}

func (c *cacher) GetMemoryUsage(key string) (int64, error) {
	return c.pool.Cmd("MEMORY", "USAGE", key).Int64()
}
