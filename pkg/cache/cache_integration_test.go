package cache

import (
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/stretchr/testify/suite"
	"gopkg.in/guregu/null.v3"
)

type CacheIntegrationTestSuite struct {
	suite.Suite
	ch Cacher
}

func TestCacheIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(CacheIntegrationTestSuite))
}

func (suite *CacheIntegrationTestSuite) SetupTest() {
	suite.ch = New(datastore.NewRedisPool([]string{"localhost:6379"}).Master())
}

func (suite *CacheIntegrationTestSuite) TearDownTest() {
}

func (suite *CacheIntegrationTestSuite) TestSetAndGet() {
	const key = "test:set"
	suite.Run("set successfully", func() {
		err := suite.ch.Set(key, testObj{
			Name: "josie",
			ID:   111,
		}, 1*time.Minute)
		suite.NoError(err)

		suite.Run("THEN get successfully", func() {
			var v testObj
			err = suite.ch.Get(key, &v)
			suite.NoError(err)
			suite.Equal(testObj{
				Name: "josie",
				ID:   111,
			}, v)
		})
	})

	//set nil and  get nil
	suite.Run("set nil", func() {
		err := suite.ch.Set(key, nil, 1*time.Minute)
		suite.NoError(err)

		suite.Run("THEN get nil", func() {
			var v testObj
			err = suite.ch.Get(key, &v)
			suite.NoError(err)
			suite.Equal(testObj{}, v)
		})
	})

	suite.Run("WHEN get non-exist THEN got ErrCacheMiss", func() {
		var v testObj
		err := suite.ch.Get("no_such:key", &v)
		suite.ErrorIs(ErrCacheMiss, err)
		fmt.Println(err, v)
	})

	testCases := []struct {
		name     string
		set      any
		receiver any
		got      any
	}{
		{"a string", "hello", new(string), "hello"},
		{"a byte array", []byte("hello"), new([]byte), []byte("hello")},
		{"float64", 1.0, new(float64), 1.0},
		{"nil for string", nil, new(string), ""},
		{"0 for int", 0, new(int), 0},
	}

	for _, tc := range testCases {
		suite.Run("set "+tc.name, func() {
			err := suite.ch.Set(key, tc.set, 1*time.Minute)
			suite.NoError(err)

			suite.Run("THEN get successfully", func() {
				receiver := tc.receiver
				err = suite.ch.Get(key, receiver)
				suite.NoError(err)
				suite.Equal(tc.got, reflect.ValueOf(receiver).Elem().Interface())
			})
		})
	}
}

func (suite *CacheIntegrationTestSuite) TestOnce() {
	key := "test:once"
	var v testObj
	err := suite.ch.Once(key, &v, 3*time.Second, func() (interface{}, error) {
		return testObj{
			Name: "josie",
			ID:   429,
		}, nil
	})
	suite.NoError(err)
	suite.Equal(testObj{
		Name: "josie",
		ID:   429,
	}, v)
}

func (suite *CacheIntegrationTestSuite) TestSetNX() {
	const key = "test:setnx"
	v := testObj{
		Name: "test",
	}
	ch := suite.ch
	suite.Run("WHEN key not exist THEN return 1", func() {
		result, err := ch.SetNX(key, &v, 3*time.Second)
		suite.NoError(err)
		suite.Equal(int64(1), result)

		suite.Run("WHEN key exists THEN return 0", func() {
			result, err := ch.SetNX(key, &v, 3*time.Second)
			suite.NoError(err)
			suite.Equal(int64(0), result)
		})
	})
}

func (suite *CacheIntegrationTestSuite) TestDel() {
	const key = "test:setnx"
	ch := suite.ch
	suite.Run("WHEN key not exist THEN return no err", func() {
		err := ch.Del("test:no-really-exist")
		suite.NoError(err)
	})

	suite.Run("set first", func() {
		err := ch.Set(key, 1, 3*time.Second)
		suite.NoError(err)

		suite.Run("and del single key THEN return no err", func() {
			suite.NoError(ch.Del(key))

			suite.Run("and get again THEN got ErrCacheMiss", func() {
				var v int
				err := ch.Get(key, &v)
				suite.ErrorIs(ErrCacheMiss, err)
			})
		})
	})

	suite.Run("set first", func() {
		err := ch.Set(key, 1, 3*time.Second)
		suite.NoError(err)

		suite.Run("and del multi-key THEN return no err", func() {
			suite.NoError(ch.Del(key, "test:no-really-exist"))

			suite.Run("and get again THEN got ErrCacheMiss", func() {
				var v int
				err := ch.Get(key, &v)
				suite.ErrorIs(ErrCacheMiss, err)
			})
		})
	})
}

func (suite *CacheIntegrationTestSuite) TestHGet() {
	const key = "test:hget"
	testcases := []struct {
		name     string
		receiver any
		hashKey  string
		wantErr  error
		wantVal  any
	}{
		{
			name:     "WHEN hash key not exist THEN got ErrCacheMiss",
			receiver: &testObj{},
			hashKey:  "no_such_key",
			wantErr:  ErrCacheMiss,
		},
		{
			name:     "WHEN hash key exist THEN got value",
			receiver: &testObj{},
			hashKey:  "meta",
			wantVal: &testObj{
				Name: "josie",
				ID:   111,
			},
		},
		{

			name:     "WHEN hash key exist and receiver is not pointer THEN got ErrReceiverNotPtr",
			receiver: testObj{},
			hashKey:  "meta",
			wantErr:  ErrReceiverNotPtr,
		},
		{
			name:     "int receiver",
			receiver: new(int64),
			hashKey:  "id",
			wantVal:  null.IntFrom(111).Ptr(),
		},
		{
			name:     "string receiver",
			receiver: new(string),
			hashKey:  "name",
			wantVal:  null.StringFrom("josie").Ptr(),
		},
		{
			name:     "[]byte receiver",
			receiver: new([]byte),
			hashKey:  "name",
			wantVal: func() *[]byte {
				v := []byte("josie")
				return &v
			}(),
		},
		{
			name:     "[]string receiver",
			receiver: new([]string),
			hashKey:  "arr",
			wantVal:  &[]string{"josie", "josie"},
		},
	}

	// prepare test data
	suite.Require().NoError(suite.ch.HSet(key, "name", []byte("josie")))
	suite.Require().NoError(suite.ch.HSet(key, "id", []byte("111")))
	suite.Require().NoError(suite.ch.HSet(key, "meta", []byte(`{"name":"josie","id":111}`)))
	suite.Require().NoError(suite.ch.HSet(key, "arr", []byte(`["josie","josie"]`)))

	defer func() {
		// clean up
		suite.Require().NoError(suite.ch.Del(key))
	}()

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			err := suite.ch.HGet(key, tc.hashKey, tc.receiver)
			if tc.wantErr != nil {
				suite.ErrorIs(tc.wantErr, err)
			} else {
				suite.NoError(err)
				suite.Equal(tc.wantVal, tc.receiver)
			}
		})
	}
}

func (suite *CacheIntegrationTestSuite) TestHMGet() {
	const key = "test:hmget"
	testcases := []struct {
		name      string
		fields    []string
		wantErr   error
		wantVals  []string
	}{
		{
			name:     "WHEN multiple hash keys exist THEN got values",
			fields:   []string{"name", "id"},
			wantVals: []string{"josie", "111"},
		},
		{
			name:     "WHEN get mixed existing and non-existing fields THEN got partial values",
			fields:   []string{"name", "nonexist", "id"},
			wantVals: []string{"josie", "", "111"},
		},
		{
			name:     "WHEN get single field THEN got single value",
			fields:   []string{"name"},
			wantVals: []string{"josie"},
		},
		{
			name:     "WHEN get meta field THEN got json object",
			fields:   []string{"meta"},
			wantVals: []string{`{"name":"josie","id":111}`},
		},
	}

	// prepare test data
	suite.Require().NoError(suite.ch.HSet(key, "name", []byte("josie")))
	suite.Require().NoError(suite.ch.HSet(key, "id", []byte("111")))
	suite.Require().NoError(suite.ch.HSet(key, "meta", []byte(`{"name":"josie","id":111}`)))
	suite.Require().NoError(suite.ch.HSet(key, "arr", []byte(`["josie","josie"]`)))

	defer func() {
		// clean up
		suite.Require().NoError(suite.ch.Del(key))
	}()

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			result, err := suite.ch.HMGet(key, tc.fields...)
			if tc.wantErr != nil {
				suite.ErrorIs(tc.wantErr, err)
			} else {
				suite.NoError(err)
				suite.Require().Equal(len(tc.wantVals), len(result))
				for i, expected := range tc.wantVals {
					suite.Equal(expected, string(result[i]))
				}
			}
		})
	}
}

func (suite *CacheIntegrationTestSuite) TestExists() {
	const key = "test:exists"
	suite.Run("WHEN key not exist THEN return false", func() {
		exists, err := suite.ch.Exists("test:no-really-exist")
		suite.NoError(err)
		suite.False(exists)
	})

	suite.Run("WHEN key exists THEN return true", func() {
		suite.NoError(suite.ch.Set(key, 1, 3*time.Second))
		exists, err := suite.ch.Exists(key)
		suite.NoError(err)
		suite.True(exists)
	})
}

func (suite *CacheIntegrationTestSuite) TestIncrBy() {
	const key = "test:incrby"
	// GIVEN clean up first
	if err := suite.ch.Del(key); err != nil {
		suite.T().Fatal(err)
	}

	defer func() {
		// clean up after
		suite.Require().NoError(suite.ch.Del(key))
	}()

	suite.Run("add from zero when first time", func() {
		idx, err := suite.ch.IncrBy(key, 3)
		suite.NoError(err)
		suite.Equal(int64(3), idx)

		suite.Run("got 6 when second time", func() {
			idx, err := suite.ch.IncrBy(key, 3)
			suite.NoError(err)
			suite.Equal(int64(6), idx)
		})
	})

}

type testObj struct {
	Name string `json:"name,omitempty"`
	ID   int64  `json:"id,omitempty"`
}
