KKTV API v3 version 3, with some cron job lambda trigger via event or cloudwatch scheduler.

## Table of Contents

- [Setup project (MAC)](#setup-project-mac)
- [Require command or package](#require-command-or-package)
- [Directory](#directory)
- [Deploy](#deploy)
- [Testing](#testing)
- [Run meta table dump (maintenance when the service-mata DB is out of sync)](#run-meta-table-dump-maintenance-when-the-service-mata-db-is-out-of-sync)
- [DB Migration](#db-migration)
- [Tools](#tools)
- [Naming Convention](#naming-convention)

## Setup Local Development Environment (MAC)

### Required development tools

1. gvm
   - gvm is a tool to manage go version
   - install [gvm](https://github.com/moovweb/gvm): 
   ```sh
   bash < <(curl -s -S -L https://raw.githubusercontent.com/moovweb/gvm/master/binscripts/gvm-installer)
   ```
    - after gvm installed, install and use go version 1.23.3 
    ```sh
    gvm install go1.23.3
    gvm use go1.23.3
    ```
1. docker-compose
    - local development environment setup
    - official: https://docs.docker.com/desktop/install/mac-install/#install-interactively
    ```sh
    brew install docker-compose
    ```
1. golang-migrate
    - db schema migration
    ```sh
    brew install golang-migrate
    ```
1. ~~apex~~ (archived by author)
    - a tool to deploy Lambda application to AWS
    - https://github.com/apex/apex in case need to deploy lambda or debug

### Prepare Environment variables

- Create `.env` file in the root directory, you can copy it from [env/.env.sample](env/.env.sample) and fill in the value.

### Dev stack (Using Docker container)

1. build up the dev stacks
   - using docker-compose to build up the development environment, such as postgres, redis, elasticsearch
   ```sh
   make docker-setup
   ```
1. start the API server application
   ```sh
   go run ./functions/simple/main.go
   ```
1. if you need to connect to the Elasticsearch on TestEnv, the [aws-es-proxy](https://github.com/abutaha/aws-es-proxy) makes it easy to connect to AWS Elasticsearch from your local machine.
   ```sh
   make es-proxy-test
   ```
   after that, you can connect to the TestEnv Elasticsearch via `http://localhost:9200`
      

## Directory

```text
.
├── console                 # CMS client
│   ├── dist                # CMS build asserts
│   ├── node_modules        # CMS third party library after npm install
│   ├── public
│   ├── src                 # CMS source
│   └── 
├── consoleapi              # CMS API
├── db
│   ├── migrations          # DB Migrations (use golang-migrate to do migration)
├── functions
│   ├── dailycharts         # cronjob for dump daily title ranking from amplitude (kktv-api-v3-daily-charts-10am)
│   ├── dailysitemap        # update sitemap of website (stop on clearwatch)
│   ├── dailytokenclear     # Daily clean up expired token and refresh_token (kktv-api-v3_dailytokenclear)
│   ├── dbtitle2redis       # dump title from service-meta to redis (kktv-api-v3_dbtitle2redis)
│   ├── dumpPlayHistory     # dump user play history to S3 (kktv-api-v3-cron-dump-recommendation-source-v2)
│   ├── kkboxprimecounter   # push kkbox prime count for KKBOX dashboard (kktv-api-v3_hourly_counter_kkboxprime2kktv)
│   ├── meta2rds            # trigger event for updating data at service-meta (dynamodb trigger)
│   ├── metadump            # dump data from DynamoDB to service-meta (only use at the very first time at meta database empty)
│   ├── simple              # v3 api
│   ├── thumbnail           # trigger event for updating episode thumbnail image (dynamodb trigger)
│   └── userrenew           # Daily renew expired User (kktv-api-v3_userrenew)
└── kkapp
    ├── amplitude           # library for amplitude
    ├── dbpool              # postgresql master slave round robin library
    ├── gzip                # library for gzip
    ├── kkbilling           # kkbox billing api
    ├── kkconsole           # CMS api handler
    ├── kkhandler           # v3 api handler
    ├── kkmiddleware        # middleware
    ├── model               # model for api
    ├── redispool           # redis master slave round robin library
    └── sns                 # send message to AWS SNS, should remove later
```

### kktvapi: The kktv API application

The [kktvapi](./kktvapi/) directory follows the [Go standard project layout](https://github.com/golang-standards/project-layout).

Explanation of the sub-folder inside [kktvapi](./kktvapi/):
```bash
.
├── cmd
│   └── http.go # entrypoint of http server. the API routing aggregation is here
├── docs
│   ├── api # api documents
│   │   ├── swagger2 # [deprecated:should put the new writing doc at swagger3] the legacy api document using swaggerV2,
│   │   │   ├── data # put the sample response json here
│   │   │   ├── index.html
│   │   │   └── swagger.yaml # api specification file using swaggerV2
│   │   └── swagger3 # the api document should be defined here
│   │       ├── index.html
│   │       ├── responses # the definition of api responses
│   │       │   └── examples # examples of the api responses
│   │       ├── schemas # defined structure that can be reused. Such as TitleMeta, Ost, etc ... objects
│   │       └── swagger.yaml # api specification file using swaggerV3
│   └── embed.go # go embed definition to use the doc folder
├── internal
│   ├── pkg # shared libraries used within the `kktvapi` scope
│   │   ├── config  # app configuration from Environment Arguments
│   │   ├── container # a container wrapper to hold connection pools
│   │   ├── kktverror
│   │   │   ├── errors.go # global errors defined
│   │   │   └── rest # structures for error type responses
│   └── v4 # the v4 business scope services
└── web # web files
    ├── console # The KKTV console web application
    └── embed.go
└── pkg # shared libraries used within the `kktvapi` scope, cross-package services such as permissionService, auditService, etc ...
```

## Deploy

**always deploy from CI/CD**

in case you need to deploy from your local machine for testing

Deploy the v3 API lambda functions:

env test, alias test, deploy to test environment

```sh
$ apex deploy simple -e test --alias test
```

OR

```sh
$ make test-deploy
```

env prod, alias prod, deploy to production enviroment

```sh
$ apex deploy simple -e prod --alias prod
```

OR

```sh
$ make prod-deploy
```

Read logs

```sh
$ apex logs simple
```

## Testing

```sh
make test
```

## Run meta table dump (maintenance when the service-mata DB is out of sync)

```sh
echo '{"name":"data-keys"}' | apex invoke metadump --alias test
```

## DB Migration

please refer to the repository: https://gitlab.com/kktvkktv/backend/********************

---

## Tools

### [Campaign Image Resizer](./kktvapi/cmd/campaign/img_resize.go)

The CampaignImageResizer can resize the campaign image to the size we need.
For example, use _main_web.jpg_ as the source image, and resize it to the size of:
  - _main_web.xs.jpg_
  - _main_web.sm.jpg_ 
  - _main_web.md.jpg_  

usage: 

```sh
make campaign-img-resize {env} {year} {key}
```
- _env_: test or prod
- _year_: 2022, 2023, etc ...
- _key_: the campaign key id, such as `bonenkai`

---

# Naming Convention

## Test case

For the **Unit test**,
1. file name should be `*_test.go`
2. the function name should be `TestXXX`, `TestXXX_YYY` or `TestXXXTestSuite`

For the **Integration test**,
1. file name should be `*_integration_test.go`
2. the function name should be `TestXXX_IntegrationTest`, `TestXXX_YYY_IntegrationTest` or `TestXXXIntegrationTestSuite`
