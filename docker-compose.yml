version: "3.8"

services:
  redis:
    image: redis:6.2.4
    ports:
      - 6379:6379
    container_name: kktv-redis
    volumes:
      - '.data:/data/.data'
    environment:
      IP: '0.0.0.0'

  database:
    image: postgres:13.3
    ports:
      - 5432:5432
    container_name: kktv-db
    hostname: database
    volumes:
      - '.data:/.data'
    environment:
      - POSTGRES_PASSWORD=123456
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 5s
      timeout: 5s
      retries: 5

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.10.1
    restart: on-failure
    container_name: kktv-es
    environment:
      - node.name=es01
      - cluster.name=es-docker-cluster
      - cluster.initial_master_nodes=es01
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - .data:/usr/share/elasticsearch/data
    ports:
      - 9200:9200
