GO=go
GOBUILD=$(GO) build
GOCLEAN=$(GO) clean
GOTEST=$(GO) test
GOGET=$(GO) get
APEX=apex
PSQL=psql
REDISCLI=redis-cli
DIR := $(shell pwd)
MIGRATE=migrate
TEST_DIR=./pkg/... ./kktvapi/... ./kkapp/... ./consoleapi/...
INTEGRATE_TEST=$(GO) test -v -count=1 -tags=integration -run IntegrationTest $(TEST_DIR)

UPDATE_THUMBNAIL := $(shell git show -m --name-only --oneline | grep 'functions/thumbnail')
UPDATE_DAILYCHARTS := $(shell git show -m --name-only --oneline | grep 'functions/dailycharts')
UPDATE_INCOMEREPORT := $(shell git show -m --name-only --oneline | grep 'functions/incomereport')
UPDATE_CANCEL_INVOICES := $(shell git show -m --name-only --oneline | grep 'functions/cancel_invoices')
UPDATE_SITEMAP_VIDEOS := $(shell git show -m --name-only --oneline | grep 'functions/sitemap_videos')
UPDATE_CRONJOB := $(shell git show -m --name-only --oneline | grep 'functions/cronjob')
UPDATE_GENERATE_MEDIAACTION := $(shell git show -m --name-only --oneline | grep 'functions/generate_mediaaction')
# use native psql command to set up database schema
db-up: get-db-migrate
	$(PSQL) -c "DROP ROLE IF EXISTS foo; CREATE USER foo WITH PASSWORD 'bar'; ALTER USER foo WITH SUPERUSER" -U postgres
	$(PSQL) -c 'CREATE DATABASE kktv_users OWNER foo;' -U postgres
	$(PSQL) -c 'CREATE DATABASE meta OWNER foo;' -U postgres
	$(PSQL) -c 'CREATE DATABASE kktv_redeem OWNER foo;' -U postgres

	$(MIGRATE) -path migrations/db_changelog/user -database postgres://foo:bar@localhost:5432/kktv_users?sslmode=disable up
	$(PSQL) -d kktv_users -f .data/kktv_users.fixtures.sql

	$(MIGRATE) -path migrations/db_changelog/meta -database postgres://foo:bar@localhost:5432/meta?sslmode=disable up
	$(PSQL) -d meta -f .data/meta.fixtures.sql

	$(MIGRATE) -path migrations/db_changelog/redeem -database postgres://foo:bar@localhost:5432/kktv_redeem?sslmode=disable up
	$(PSQL) -d kktv_redeem -f .data/kktv_redeem.fixtures.sql

# use native redis-cli command to set up cache env
redis-up:
	$(REDISCLI) < .data/redis_extra_episodes.txt
	$(REDISCLI) < .data/redis_playback_title_and_episode.txt
	$(REDISCLI) < .data/redis_service_status.txt
	$(REDISCLI) < .data/redis_hot_keywords.txt
	$(REDISCLI) < .data/redis_title.txt
	$(REDISCLI) < .data/redis_coldstart.txt
	$(REDISCLI) < .data/redis_products.txt

setup:
	@echo "Setup db for local dev env"
	$(MAKE) db-up
	$(MAKE) redis-up

docker-db-up: PSQL = docker exec -i --user postgres kktv-db psql
# use docker pgsql to set up database schema
docker-db-up: db-up

docker-redis-up: REDISCLI = docker exec -i kktv-redis redis-cli
# use docker pgsql to set up cache env
docker-redis-up: redis-up

docker-setup:
	docker-compose up -d
	$(MAKE) docker-db-up
	$(MAKE) docker-redis-up

fast-test: # only run unit tests
	$(GOTEST) -count=1 -race -skip IntegrationTest $(TEST_DIR)

integration-test: # only run integration tests
	# TODO should have a independent .env.test file for integration test
	export $$(cat env/.env.sample | xargs) && $(INTEGRATE_TEST)

# setup the local environment for connecting to elasticsearch on test env, via localhost:9200
es-proxy-test:
	docker run --rm -v ~/.aws:/root/.aws -p 9200:9200 abutaha/aws-es-proxy:v1.0 -endpoint http://search-kktv-test-elasticsearch-uhzg75sj7xa7zggyj7sff2yche.ap-northeast-1.es.amazonaws.com -listen 0.0.0.0:9200

bench:
	REDISMETA=localhost:6379 \
	REDISUSER=localhost:6379 \
	DBUSER=postgres://foo:bar@localhost:5432/kktv_users \
	DBREDEEM=postgres://foo:bar@localhost:5432/kktv_redeem \
	DBMETA=postgres://foo:bar@localhost:5432/meta \
	MMDB=../../functions/simple/.GeoLite2-Country.mmdb \
	SEARCHHOST=http://localhost:9200 \
	$(GOTEST) -run=none -bench=SingleTitle ./kkapp/kkhandler/...

install:
	$(GO) mod download

.PHONY: clean
clean:
	$(GOCLEAN)
	rm -f lambda-result.json

# please use `test-build-console` or `prod-build-console` to specify the env
build-console:
	cd console && $(BuildCMD)
	mkdir -p kktvapi/web/console
	cp -rf console/dist/* kktvapi/web/console

test-build-console: BuildCMD = npm run build:test
test-build-console: build-console

prod-build-console: BuildCMD = npm run build
prod-build-console: build-console

download-geolite2:
	curl -L -o /tmp/GeoLite2-Country.tar.gz "https://download.maxmind.com/app/geoip_download?edition_id=GeoLite2-Country&license_key=****************************************&suffix=tar.gz"
	tar zxvf /tmp/GeoLite2-Country.tar.gz
	/bin/cp GeoLite2-Country*/GeoLite2-Country.mmdb functions/simple/.GeoLite2-Country.mmdb
	rm -rf GeoLite2-Country*
	ls -al ./functions/simple

download-geolite2-from-s3:
	# download from aws s3://kktv-test/GeoLite2-Country.mmdb via aws cli
	aws s3 cp s3://kktv-test/GeoLite2-Country.mmdb functions/simple/.GeoLite2-Country.mmdb

get-db-migrate:
	#download the migration changelogs from https://gitlab.com/kktvkktv/backend/******************** dev branch
	@if [ -z "$(GITLAB_PRIVATE_TOKEN)" ]; then \
		echo "Error: GITLAB_PRIVATE_TOKEN is not set. Please set it with 'export GITLAB_PRIVATE_TOKEN=your_token'"; \
		exit 1; \
	fi
	curl --request GET 'https://gitlab.com/api/v4/projects/41911944/repository/archive.tar?sha=dev&path=changelogs' --header 'PRIVATE-TOKEN: $(GITLAB_PRIVATE_TOKEN)' | tar -x
	rm ********************-*/changelogs/**/*.down.sql
	mkdir -p migrations/db_changelog
	cp -rf ********************-*/changelogs/* migrations/db_changelog/
	rm -rf ********************-*

db-migrate-%:
	$(eval db = $(filter dbuser dbmeta dbredeem, $(DB)))
	@if [ -z "$(db)" ]; then \
		echo "database got $(DB), but only support: [dbuser|dbmeta|dbredeem]"; exit 1; \
	fi;
	$(eval ENV := $(filter prod test, $*))
	@if [ -z "$(ENV)" ]; then \
		echo "got $*, but only support: [db-migrate-test | db-migrate-prod]"; exit 1; \
	fi;
	@if ! ENV=$(ENV) DB=$(db) sh scripts/db_migrate.sh; then \
		rm -f lambda-result.json && exit 2; \
	fi;
	rm -f lambda-result.json

campaign-img-resize: # should follow by 3 params [ENV] [YEAR] [KEY]
	# EXAMPLE command. make campaign-img-resize ENV=test YEAR=2023 KEY=openpoint
	$(eval EXAMPLE = "e.g. \`make campaign-img-resize ENV=test YEAR=2023 KEY=openpoint\'")
	@if [ -z "$(ENV)" ]; then \
		echo "ENV is required. $(EXAMPLE)"; exit 1; \
	fi;
	@if [ -z "$(YEAR)" ]; then \
		echo "YEAR is required. $(EXAMPLE)"; exit 1; \
	fi;
	@if [ -z "$(KEY)" ]; then \
		echo "KEY is required. $(EXAMPLE)"; exit 1; \
	fi;
	$(GO) run ./kktvapi/cmd/campaign/*.go $(ENV) $(YEAR) $(KEY)

prod-deploy:
	$(APEX) deploy simple -e prod --alias prod
	$(APEX) deploy userrenew -e prod --alias prod
	$(APEX) deploy sitemap_generator -e prod --alias prod
	$(APEX) deploy dailyuserexpire -e prod --alias prod
	$(APEX) deploy dbtitle2es -e prod --alias prod
	$(APEX) deploy cronjob -e prod --alias prod
	$(APEX) deploy meta2rds -e prod --alias prod
	$(APEX) deploy dbtitle2redis -e prod --alias prod
	$(APEX) deploy daily_check_linetv_sso_csv_file -e prod --alias prod
ifneq ($(UPDATE_THUMBNAIL),)
	@echo "thumbnail updated"
	$(APEX) deploy thumbnail -e prod --alias prod
endif
ifneq ($(UPDATE_DAILYCHARTS),)
	@echo "dailycharts updated"
	$(APEX) deploy dailycharts -e prod --alias prod
endif
ifneq ($(UPDATE_INCOMEREPORT),)
	@echo "incomereport updated"
	$(APEX) deploy incomereport -e prod --alias prod
endif
ifneq ($(UPDATE_CANCEL_INVOICES),)
	@echo "cancel_invoices updated"
	$(APEX) deploy cancel_invoices -e prod --alias prod
endif
ifneq ($(UPDATE_SITEMAP_VIDEOS),)
	@echo "sitemap_videos updated"
	$(APEX) deploy sitemap_videos -e prod --alias prod
endif
ifneq ($(UPDATE_GENERATE_MEDIAACTION),)
	@echo "generate_mediaaction updated"
	$(APEX) deploy generate_mediaaction -e prod --alias prod
endif

test-deploy:
	$(APEX) deploy simple -e test --alias test
	$(APEX) deploy userrenew -e test --alias test
	$(APEX) deploy dailyuserexpire -e test --alias test
	$(APEX) deploy dbtitle2es -e test --alias test
	$(APEX) deploy meta2rds -e test --alias test
	$(APEX) deploy cronjob -e test --alias test
	$(APEX) deploy dbtitle2redis -e test --alias test
	$(APEX) deploy daily_check_linetv_sso_csv_file -e test --alias test
ifneq ($(UPDATE_THUMBNAIL),)
	@echo "thumbnail updated"
	$(APEX) deploy thumbnail -e test --alias test
endif
ifneq ($(UPDATE_DAILYCHARTS),)
	@echo "dailycharts updated"
	$(APEX) deploy dailycharts -e test --alias test
endif
ifneq ($(UPDATE_INCOMEREPORT),)
	@echo "incomereport updated"
	$(APEX) deploy incomereport -e test --alias test
endif
ifneq ($(UPDATE_CANCEL_INVOICES),)
	@echo "cancel_invoices updated"
	$(APEX) deploy cancel_invoices -e test --alias test
endif
ifneq ($(UPDATE_SITEMAP_VIDEOS),)
	@echo "sitemap_videos updated"
	$(APEX) deploy sitemap_videos -e test --alias test
endif
ifneq ($(UPDATE_GENERATE_MEDIAACTION),)
	@echo "generate_mediaaction updated"
	$(APEX) deploy generate_mediaaction -e test --alias test
endif

amplitude-release:
	. ./scripts/create_amplitude_release.sh

.PHONY: test install clean setup docker-db-up docker-redis-up docker-setup get-db-migrate amplitude-release campaign-img-resize
