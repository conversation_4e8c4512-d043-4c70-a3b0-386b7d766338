version: "2"
linters:
  default: none
  enable:
    - errcheck
    - govet
    - ineffassign
    - misspell
    - staticcheck
    - unconvert
    - unused
    - cyclop
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    paths:
      - third_party$
      - builtin$
      - examples$
formatters:
  enable:
    - goimports
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
